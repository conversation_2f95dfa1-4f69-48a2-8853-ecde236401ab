{"name": "@escook/express-joi", "version": "1.1.1", "description": "基于 @hapi/joi，用于校验 body、query 和 params 的 express 中间件。", "main": "index.js", "scripts": {}, "keywords": ["express", "joi", "express-joi", "validator"], "repository": {"type": "git", "url": "**************:liulongbin1314/express-joi.git"}, "bugs": {"url": "https://github.com/liulongbin1314/express-joi/issues"}, "author": "LiuLongBin", "license": "ISC", "dependencies": {"joi": "^17.4.0"}}