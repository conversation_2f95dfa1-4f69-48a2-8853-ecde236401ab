{"name": "express-unless", "description": "Conditionally add a middleware to express with some common patterns.", "version": "0.3.1", "license": "MIT", "repository": {"url": "git://github.com/jfromaniello/express-unless.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com"}, "main": "index.js", "scripts": {"test": "mocha -R spec"}, "dependencies": {}, "devDependencies": {"mocha": "~1.11.0", "chai": "~1.7.0"}}