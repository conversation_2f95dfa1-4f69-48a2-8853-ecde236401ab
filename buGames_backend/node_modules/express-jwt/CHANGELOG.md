# Change Log


All notable changes to this project will be documented in this file starting from version **v4.0.0**.
This project adheres to [Semantic Versioning](http://semver.org/).

## 5.1.0 - 2016-10-04

 - A cleaner way to detect a function ([b7235714def5b4b3b91ee2d955a6a82706792825](https://github.com/auth0/express-jwt/commit/b7235714def5b4b3b91ee2d955a6a82706792825))
 - allow other auth schemes if credentialsRequired is false. closes #129 ([fbf15bd3ccb8b71fe2434b0165492e53bf56d6cd](https://github.com/auth0/express-jwt/commit/fbf15bd3ccb8b71fe2434b0165492e53bf56d6cd)), closes [#129](https://github.com/auth0/express-jwt/issues/129)
 - handle error on invalid tokens. Closes #134 ([461710185e8cba665b81b77e14895eee45b4d076](https://github.com/auth0/express-jwt/commit/461710185e8cba665b81b77e14895eee45b4d076)), closes [#134](https://github.com/auth0/express-jwt/issues/134)
 - minor ([a2c54081f631b6c1670dc6b85730b6381a87972e](https://github.com/auth0/express-jwt/commit/a2c54081f631b6c1670dc6b85730b6381a87972e))



## 5.0.0 - 2016-09-05

 - *Expose UnauthorizedError ([a6a36058b949bbffaa5969e6435aaad5201651d8](https://github.com/auth0/express-jwt/commit/a6a36058b949bbffaa5969e6435aaad5201651d8))


## 4.0.0 - 2016-05-06

 - Added express-jwt-permissions link ([ef0b848b15ce7ec7148bfbb1a97ee6a9991f7251](https://github.com/auth0/express-jwt/commit/ef0b848b15ce7ec7148bfbb1a97ee6a9991f7251))
 - remove support for deprecated option ([b894ea25b0721305861f57dbec6982eb2a462e97](https://github.com/auth0/express-jwt/commit/b894ea25b0721305861f57dbec6982eb2a462e97))
 - Update middleware to throw when token is invalid when credentials aren't required ([fd58e8961fe6034e7136ea0b31218a299ddf5178](https://github.com/auth0/express-jwt/commit/fd58e8961fe6034e7136ea0b31218a299ddf5178))
 - upgrade jwt library ([01409b3dd99306520a498894293657a88778cdd5](https://github.com/auth0/express-jwt/commit/01409b3dd99306520a498894293657a88778cdd5))

## 3.4.0 - 2016-05-06

 - doc: typo in README.md was fixed ([f6c2c3d95fd15b911f1ac6dcde0b3084df45a2fc](https://github.com/auth0/express-jwt/commit/f6c2c3d95fd15b911f1ac6dcde0b3084df45a2fc))
 - fixing syntax error in README for string value ([ae69114afe5ca84f39adfac8dc7e9b224eab5410](https://github.com/auth0/express-jwt/commit/ae69114afe5ca84f39adfac8dc7e9b224eab5410))
 - More lightweight dependency ([4861bbb9d906f8fbd8c494fe2dbc4fda0d7865c6](https://github.com/auth0/express-jwt/commit/4861bbb9d906f8fbd8c494fe2dbc4fda0d7865c6))
 - Readme fixed and license renamed ([0e9c88d592f6499bf4d4e212a39fdc50e7206832](https://github.com/auth0/express-jwt/commit/0e9c88d592f6499bf4d4e212a39fdc50e7206832))


## 3.3.0 - 2015-11-09

 - 3.3.0 ([6ae3a7f2685e0a0ac8dd0e286c1bafd00fb4b8c2](https://github.com/auth0/express-jwt/commit/6ae3a7f2685e0a0ac8dd0e286c1bafd00fb4b8c2))
 - add support for nested properties in requestProperty. closes #94 ([6b7a7349910c530d3c0f986c267276930883918f](https://github.com/auth0/express-jwt/commit/6b7a7349910c530d3c0f986c267276930883918f)), closes [#94](https://github.com/auth0/express-jwt/issues/94)



## 3.2.0 - 2015-11-09

 - added documentation on setting base64 encoding flag ([e4cddfdc432b02d48bd61b627da7c927df79d6fc](https://github.com/auth0/express-jwt/commit/e4cddfdc432b02d48bd61b627da7c927df79d6fc))
 - added documentation on setting base64 encoding flag ([0ebfd6c125314d83e98df93b9d75b91287e44c49](https://github.com/auth0/express-jwt/commit/0ebfd6c125314d83e98df93b9d75b91287e44c49))
 - added documentation on setting base64 encoding flag ([cb04d571a098e49d5dcc5d9bf15481bc6266b598](https://github.com/auth0/express-jwt/commit/cb04d571a098e49d5dcc5d9bf15481bc6266b598))
 - Clarify credentialsRequired remarks ([80fae765044ea8506cf89e1f6238ce4e12ad8d6e](https://github.com/auth0/express-jwt/commit/80fae765044ea8506cf89e1f6238ce4e12ad8d6e))
 - Tweak of description, code sample, and location ([f3024e2c4ba5ba5896983520ff9410dcc30c92e5](https://github.com/auth0/express-jwt/commit/f3024e2c4ba5ba5896983520ff9410dcc30c92e5))
 - Use npm v2 in CI build ([da3ad2bba2eae5febf1d1fc9eb04ad2c46302fd4](https://github.com/auth0/express-jwt/commit/da3ad2bba2eae5febf1d1fc9eb04ad2c46302fd4))
 - Verify token before checking revoke ([d75cec869dc9a37b6199c7615bbfa77dae97aa05](https://github.com/auth0/express-jwt/commit/d75cec869dc9a37b6199c7615bbfa77dae97aa05))



## 3.1.0 - 2015-09-09

 - Changes the README describing unless and linking to the express unless github repo. ([6447a034fb7dd44526464e02319802f15f1e5315](https://github.com/auth0/express-jwt/commit/6447a034fb7dd44526464e02319802f15f1e5315))
 - Expand on what is possible with path param for unless() and give link to express-unless so the user knows that is what is being utilized. ([f13cd5f0d55154e551b11e872668879180979640](https://github.com/auth0/express-jwt/commit/f13cd5f0d55154e551b11e872668879180979640))
 - Merge README enhancement from @rustybailey ([71e5ec53b4d631cb6b8e5b7a691ab77636044612](https://github.com/auth0/express-jwt/commit/71e5ec53b4d631cb6b8e5b7a691ab77636044612))
 - Minor typo fix ([df62ee2bca84ca3990751ba3e567c95a6f3af86e](https://github.com/auth0/express-jwt/commit/df62ee2bca84ca3990751ba3e567c95a6f3af86e))
 - Optionally pass token headers to secret callback. ([988931b2fbbfb9f694a4c25c2f867a613f3f8a81](https://github.com/auth0/express-jwt/commit/988931b2fbbfb9f694a4c25c2f867a613f3f8a81))
 - Set express-unless minor version number. ([c262caf73ca64c2175717076538786da4397894c](https://github.com/auth0/express-jwt/commit/c262caf73ca64c2175717076538786da4397894c))
 - Tweak to make .unless comment a blockquote ([f1b099ed6af12e099d4c4f43d42bf4aec0c4df36](https://github.com/auth0/express-jwt/commit/f1b099ed6af12e099d4c4f43d42bf4aec0c4df36))
 - Update package.json ([88a2be2d89e6772d19463a94d8ada56b9832367d](https://github.com/auth0/express-jwt/commit/88a2be2d89e6772d19463a94d8ada56b9832367d))
 - Updated status responses to Express 4.x format ([a481bc8eb2a2e749e9bcff92496c53b5da53c9e0](https://github.com/auth0/express-jwt/commit/a481bc8eb2a2e749e9bcff92496c53b5da53c9e0))
 - typo: revoked is the name of the argument ([3cacbf391e86b70807255dadc8fd5d88153b67e4](https://github.com/auth0/express-jwt/commit/3cacbf391e86b70807255dadc8fd5d88153b67e4))



## 3.0.0 - 2015-04-11

 - fix typo ([c39e1d1036a05b5bd3d3f7a46a03f825542c1027](https://github.com/auth0/express-jwt/commit/c39e1d1036a05b5bd3d3f7a46a03f825542c1027))
 - Fix typo on README.md ([bdab49c5c4de4a154b3043f4684a60584279d36e](https://github.com/auth0/express-jwt/commit/bdab49c5c4de4a154b3043f4684a60584279d36e))



## 2.1.0 - 2015-03-16

 - update jsonwebtoken to latest version ([7ca6a07a0c85fe4b24484c8f61ed7d15d918474b](https://github.com/auth0/express-jwt/commit/7ca6a07a0c85fe4b24484c8f61ed7d15d918474b))



## 2.0.1 - 2015-03-11

 - Fixed multitenancy bug where if a secret is a buffer, it is incorrectly treated as a callback. Also provided a test which exercises this logic. ([217474476b82d17bb39228ba7c07b8ea6e10df55](https://github.com/auth0/express-jwt/commit/217474476b82d17bb39228ba7c07b8ea6e10df55))
 - Fixed naming of my new test ([6a6b5df4846bd84550e16a38e0d06d23076bb57a](https://github.com/auth0/express-jwt/commit/6a6b5df4846bd84550e16a38e0d06d23076bb57a))
 - Replaced check for string or buffer with check for not function. Used fast+robust method rather than typeof. ([5a28821c0363b1d9d9ac558b1cc8fb13e1f97cb7](https://github.com/auth0/express-jwt/commit/5a28821c0363b1d9d9ac558b1cc8fb13e1f97cb7))
 - Updated contributors in readme ([22e82fb31b4d72f8f636a17e7e3012248fd46f29](https://github.com/auth0/express-jwt/commit/22e82fb31b4d72f8f636a17e7e3012248fd46f29))



## 2.0.0 - 2015-03-06

 - update jsonwebtoken to v4 ([f4115a56edb78b37234e38ff823d764573eba414](https://github.com/auth0/express-jwt/commit/f4115a56edb78b37234e38ff823d764573eba414))



## 1.4.0 - 2015-03-06

 - add test ([1cc3ed57389e3a9531e6c698bfd5ed08d3ff61b6](https://github.com/auth0/express-jwt/commit/1cc3ed57389e3a9531e6c698bfd5ed08d3ff61b6))



## 1.3.1 - 2015-03-06

 - fix issue decoding JWT when the payload is a string ([d335c70b7055c014f23463396907c14e232d0e72](https://github.com/auth0/express-jwt/commit/d335c70b7055c014f23463396907c14e232d0e72))
 - refactor tests ([c0f9033393e039791af68e0b7b6fec26d6b56fa5](https://github.com/auth0/express-jwt/commit/c0f9033393e039791af68e0b7b6fec26d6b56fa5))



## 1.3.0 - 2015-03-03

 - Added support for revoked JWTs ([6bba96731e0b47b30af8120ec4f68acae7ad4be8](https://github.com/auth0/express-jwt/commit/6bba96731e0b47b30af8120ec4f68acae7ad4be8))
 - Updated README.md with revoked tokens check ([226317ace92d679dfe41e8436a4e1ce43fefbf37](https://github.com/auth0/express-jwt/commit/226317ace92d679dfe41e8436a4e1ce43fefbf37))



## 1.2.0 - 2015-03-03

 - Added multitenant support ([672dd72b5e2132a5947220a24539fbbb58ee105a](https://github.com/auth0/express-jwt/commit/672dd72b5e2132a5947220a24539fbbb58ee105a))



## 1.1.0 - 2015-03-02

 - added failure test, which checks for invalid signatures ([c465af6828566017df45bbe353628c65ce3a4407](https://github.com/auth0/express-jwt/commit/c465af6828566017df45bbe353628c65ce3a4407))
 - Create LICENSE.txt ([be2b1ac8f6c2dcf7bed26a2ade876d10abd6d564](https://github.com/auth0/express-jwt/commit/be2b1ac8f6c2dcf7bed26a2ade876d10abd6d564))
 - support requestProperty (instead of userProperty) closes #41 ([c5377304dfcf1fd77cd9db61f2f8ffaa11bb338b](https://github.com/auth0/express-jwt/commit/c5377304dfcf1fd77cd9db61f2f8ffaa11bb338b)), closes [#41](https://github.com/auth0/express-jwt/issues/41)
 - Update index.js ([f20fcb66f013d7b4d4b8ada1e7252295db293451](https://github.com/auth0/express-jwt/commit/f20fcb66f013d7b4d4b8ada1e7252295db293451))
 - Update index.js ([3b3ffabe48be5c82d065c30579971bd1a1ffddf8](https://github.com/auth0/express-jwt/commit/3b3ffabe48be5c82d065c30579971bd1a1ffddf8))
 - update npm on travis script ([69cb5f71d8b268441b7ce17d4f50f3f8d4049d70](https://github.com/auth0/express-jwt/commit/69cb5f71d8b268441b7ce17d4f50f3f8d4049d70))
 - Update README.md ([6ae118e35091440c233015ef44899f972b9917ee](https://github.com/auth0/express-jwt/commit/6ae118e35091440c233015ef44899f972b9917ee))
 - Update README.md ([48b326c3b44ed92ac79f665471889bc3ef3876a5](https://github.com/auth0/express-jwt/commit/48b326c3b44ed92ac79f665471889bc3ef3876a5))



## 1.0.0 - 2015-01-15




## 0.6.2 - 2015-01-05

 - 0.6.2 ([1d00b78e7cf9572bc3843dff7ecb02eb5c9339c3](https://github.com/auth0/express-jwt/commit/1d00b78e7cf9572bc3843dff7ecb02eb5c9339c3))
 - Should not throw exception with invalid token if credentials are not required ([c68a16c01043436ce9b5851e39e000efd9ab5778](https://github.com/auth0/express-jwt/commit/c68a16c01043436ce9b5851e39e000efd9ab5778))
 - Updated test to verify that req.user is undefined if token is invalid ([014e2bdcad3f1ac42c070c2ea267f5f4206c099a](https://github.com/auth0/express-jwt/commit/014e2bdcad3f1ac42c070c2ea267f5f4206c099a))



