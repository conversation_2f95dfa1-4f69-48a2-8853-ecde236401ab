{"name": "depd", "description": "Deprecate all the things", "version": "1.1.2", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["deprecate", "deprecated"], "repository": "do<PERSON><PERSON><PERSON>/nodejs-depd", "browser": "lib/browser/index.js", "devDependencies": {"benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint": "3.19.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.7", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "~1.21.5"}, "files": ["lib/", "History.md", "LICENSE", "index.js", "Readme.md"], "engines": {"node": ">= 0.6"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --no-exit test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/"}}