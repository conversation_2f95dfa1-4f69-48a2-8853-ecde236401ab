const express = require('express')
const multer = require('multer')
const path = require('path')

const router = express.Router()

const expressJoi = require('@escook/express-joi')
const gameSchema = require('../schema/game')

const game_handler = require('../router_handler/games')

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 临时存储目录，后续会移动到具体的游戏目录
    cb(null, path.join(__dirname, '../upload/temp/'))
  },
  filename: function (req, file, cb) {
    // 保持原始文件名
    cb(null, file.originalname)
  }
})

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 只允许HTML文件
  if (file.mimetype === 'text/html' || path.extname(file.originalname).toLowerCase() === '.html') {
    cb(null, true)
  } else {
    cb(new Error('只允许上传HTML文件'), false)
  }
}

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024 // 限制文件大小为2MB
  }
})

router.get('/game', game_handler.getGameList)
router.get('/gameDesc', game_handler.getGameDesc)
router.get('/gameDesc/:game_id', game_handler.getGameDesc) // 支持URL参数方式
router.post('/newGame', upload.single('gameFile'),expressJoi(gameSchema), game_handler.addNewGame) // 使用multer处理文件上传
router.post('/editGame', upload.single('gameFile'),expressJoi(gameSchema), game_handler.editGame) // 使用multer处理文件上传
router.delete('/deleteGame', game_handler.deleteGame) // 支持查询参数方式
router.delete('/deleteGame/:game_id', game_handler.deleteGame) // 支持URL参数方式

module.exports = router