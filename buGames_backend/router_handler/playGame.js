const fs = require('fs')
const path = require('path')
const db = require('../db')

const playGame = (req, res) => {
  const game_name = req.params.game_name

  if (!game_name) {
    return res.cc('Missing necessary parameter game_name')
  }

  // URL decode parameters in case they contain encoded characters.
  const decodedGameName = decodeURIComponent(game_name)

  // Query by game_name and verify game_name matches for security.
  const sql = 'select * from games where game_name = ?'

  db.query(sql, [decodedGameName], (err, results) => {
    if(err) {
      return res.cc(err)
    }

    if(results.length === 0) {
      return res.cc('Game does not exist')
    }

    const filePath = results[0].file_path
    // Build the full file path
    const fullPath = path.join(__dirname, '..', filePath)

    if (!fs.existsSync(fullPath)) {
      return res.cc('Game file does not exist')
    }

    const text = fs.readFileSync(fullPath, 'utf8')
    res.send(text)
  })
}

module.exports = {
  playGame
}
