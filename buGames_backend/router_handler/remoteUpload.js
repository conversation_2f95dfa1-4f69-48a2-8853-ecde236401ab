const db = require('../db')
const fs = require('fs')
const path = require('path')

const uploadGame = (req, res) => {
  // 从请求体中获取游戏名称和HTML内容
  const { game_name, html_content } = req.body

  // 验证游戏名称
  if (!game_name) {
    return res.cc('游戏名称不能为空')
  }

  // 验证游戏名称格式（只允许字母、数字和下划线）
  if (!/^[a-zA-Z0-9_]+$/.test(game_name)) {
    return res.cc('游戏名称只能包含字母、数字和下划线')
  }

  // 验证HTML内容
  if (!html_content || !html_content.trim()) {
    return res.cc('HTML内容不能为空')
  }

  // 检查游戏名是否已存在
  const sql1 = 'select * from games where game_name = ?'
  db.query(sql1, game_name, (err, results) => {
    if(err) {
      return res.cc(err)
    }
    if(results.length !== 0) {
      return res.cc('游戏名已存在')
    }

    // 创建游戏目录
    const gameDir = path.join(__dirname, '../upload', game_name)

    try {
      // 如果目录不存在则创建
      if (!fs.existsSync(gameDir)) {
        fs.mkdirSync(gameDir, { recursive: true })
      }

      // 构建HTML文件路径
      const htmlFileName = `${game_name}.html`
      const htmlFilePath = path.join(gameDir, htmlFileName)
      const relativePath = `upload/${game_name}/${htmlFileName}`

      // 保存HTML内容到文件
      fs.writeFileSync(htmlFilePath, html_content, 'utf8')

      // 插入数据库（不包含game_desc）
      const sql2 = 'insert into games (game_name, game_desc, file_path) values (?, ?, ?)'
      db.query(sql2, [game_name, '', relativePath], (err, results) => {
        if(err) {
          // 如果数据库插入失败，删除已创建的文件和目录
          try {
            fs.unlinkSync(htmlFilePath)
            fs.rmdirSync(gameDir)
          } catch (cleanupErr) {
            console.error('清理文件失败:', cleanupErr)
          }
          return res.cc(err)
        }

        if(results.affectedRows !== 1) {
          // 如果插入失败，删除已创建的文件和目录
          try {
            fs.unlinkSync(htmlFilePath)
            fs.rmdirSync(gameDir)
          } catch (cleanupErr) {
            console.error('清理文件失败:', cleanupErr)
          }
          return res.cc('添加游戏失败')
        }

        res.send({
          status: 0,
          message: '游戏上传成功',
          data: {
            game_id: results.insertId,
            game_name: game_name,
            game_desc: '',
            file_path: relativePath
          }
        })
      })

    } catch (fileErr) {
      console.error('文件操作失败:', fileErr)
      return res.cc('文件保存失败')
    }
  })
}

module.exports = {
  uploadGame
}