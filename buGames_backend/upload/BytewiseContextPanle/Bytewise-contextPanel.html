<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bytewise - Customised Chatbots for Personalised Learning</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome removed for standalone functionality -->
    <style>
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        @media (max-width: 768px) {
            .sidebar-hidden { transform: translateX(-100%); }
            .chat-sidebar-mobile { 
                position: fixed;
                top: 0;
                right: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(100%);
                transition: transform 0.3s ease-in-out;
            }
            .chat-sidebar-mobile.show { 
                transform: translateX(0);
            }
        }
    </style>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        background: {
                            light: '#FFFFFF',
                            dark: '#181818'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-background-light dark:bg-background-dark text-gray-900 dark:text-gray-100 min-h-screen">
    <!-- Landing Page -->
    <div id="landing-page" class="min-h-screen flex flex-col items-center justify-center px-4">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="text-6xl md:text-8xl font-bold mb-8 text-gray-900 dark:text-white">Bytewise</h1>
            <p class="text-xl md:text-2xl mb-12 text-gray-600 dark:text-gray-300">Customised chatbots for personalised learning</p>
            
            <div class="space-y-4 max-w-md mx-auto">
                <button onclick="showSignup()" class="w-full bg-black text-white py-4 px-8 rounded-lg text-lg font-medium hover:bg-gray-800 transition-colors">
                    GET STARTED
                </button>
                <button onclick="showLogin()" class="w-full bg-black text-white py-4 px-8 rounded-lg text-lg font-medium hover:bg-gray-800 transition-colors">
                    LOGIN
                </button>
            </div>
        </div>
    </div>

    <!-- Signup Page -->
    <div id="signup-page" class="hidden min-h-screen flex items-center justify-center px-4 py-8 bg-white dark:bg-gray-900">
        <div class="max-w-md w-full">
            <h2 class="text-3xl font-bold mb-2">Join Bytewise for free</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-8">Unlock a new experience of chatbot customisation</p>
            
            <form id="signup-form" class="space-y-6">
                <div>
                    <input type="text" id="fullname" placeholder="Your full name" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                </div>
                
                <div>
                    <input type="email" id="email" placeholder="Your email" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                </div>
                
                <div>
                    <input type="password" id="password" placeholder="Create a password" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                </div>
                
                <div>
                    <input type="password" id="repeat-password" placeholder="Repeat password" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                </div>
                
                <div class="flex items-center space-x-6">
                    <span class="text-gray-700 dark:text-gray-300">I am a:</span>
                    <label class="flex items-center">
                        <input type="radio" name="role" value="teacher" class="mr-2">
                        <span>Teacher</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="role" value="student" class="mr-2">
                        <span>Student</span>
                    </label>
                </div>
                
                <p class="text-sm text-gray-600 dark:text-gray-400">If you don't have or prefer not to share your staff/student ID, please enter 0:</p>
                
                <div>
                    <input type="text" id="staff-id" placeholder="Staff ID or Student ID" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="terms" class="mr-3 w-5 h-5 text-primary">
                    <label for="terms" class="text-sm">
                        I agree to the <a href="#" class="text-primary underline">Terms and Conditions</a>
                    </label>
                </div>
                
                <button type="submit" class="w-full bg-black text-white py-4 px-8 rounded-lg text-lg font-medium hover:bg-gray-800 transition-colors">
                    GET STARTED
                </button>
            </form>
            
            <div class="mt-6 text-center">
                <button onclick="showLanding()" class="text-primary hover:underline">Back to landing page</button>
            </div>
        </div>
    </div>

    <!-- Login Page -->
    <div id="login-page" class="hidden min-h-screen flex items-center justify-center px-4 bg-white dark:bg-gray-900">
        <div class="max-w-md w-full">
            <h2 class="text-3xl font-bold mb-8">Login to Bytewise</h2>
            
            <form id="login-form" class="space-y-6">
                <div>
                    <input type="email" placeholder="Your email" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                </div>
                
                <div>
                    <input type="password" placeholder="Your password" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                </div>
                
                <button type="submit" class="w-full bg-black text-white py-4 px-8 rounded-lg text-lg font-medium hover:bg-gray-800 transition-colors">
                    LOGIN
                </button>
            </form>
            
            <div class="mt-6 text-center">
                <button onclick="showLanding()" class="text-primary hover:underline">Back to landing page</button>
            </div>
        </div>
    </div>

    <!-- Dashboard Layout -->
    <div id="dashboard" class="hidden min-h-screen flex">
        <!-- Sidebar -->
        <aside id="sidebar" class="w-80 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 sidebar-transition">
            <div class="p-6">
                <div class="flex items-center justify-between mb-8">
                    <h1 class="text-xl font-bold">Bytewise</h1>
                    <button id="sidebar-toggle" class="md:hidden">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="text-right text-sm text-gray-600 dark:text-gray-400 mb-8">Dr Simon Wang's Bytewise</div>
                
                <nav class="space-y-2">
                    <a href="#" onclick="showPage('academic-writing')" class="nav-item flex items-center px-4 py-3 text-primary bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        📚
                        Academic Writing
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 overflow-hidden">
            <!-- Mobile Header -->
            <header class="md:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
                <div class="flex items-center justify-between">
                    <h1 class="text-xl font-bold">Bytewise</h1>
                    <button id="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </header>

            <div class="p-6 md:p-8 h-full overflow-y-auto">
                <!-- Notifications Page -->
                <div id="notifications-page" class="page-content">
                    <h1 class="text-4xl font-bold mb-8">Notifications</h1>
                    
                    <button onclick="showCreateNotification()" class="bg-black text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors mb-8">
                        Create New Notification
                    </button>
                    
                    <p class="text-gray-600 dark:text-gray-400">No notifications available</p>
                </div>

                <!-- Open Courses Page -->
                <div id="open-courses-page" class="page-content hidden">
                    <h1 class="text-4xl font-bold mb-8">Open Courses</h1>
                    
                    <div class="mb-8">
                        <h2 class="text-2xl font-semibold mb-6">Generic Chatbots (OpenAI)</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div class="course-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                                <h3 class="text-xl font-semibold mb-4">OpenAI (gpt-4o)</h3>
                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-graduation-cap mr-2"></i>
                                        Course: Open Courses
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        Teacher: Admin Teacher
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar mr-2"></i>
                                        Created on: 22/11/2024, 06:58:46
                                    </div>
                                </div>
                                <button class="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-comments mr-2"></i>
                                    Start Chat
                                </button>
                            </div>

                            <div class="course-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                                <h3 class="text-xl font-semibold mb-4">OpenAI (gpt-4o-mini)</h3>
                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-graduation-cap mr-2"></i>
                                        Course: Open Courses
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        Teacher: Admin Teacher
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar mr-2"></i>
                                        Created on: 22/11/2024, 07:00:18
                                    </div>
                                </div>
                                <button class="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-comments mr-2"></i>
                                    Start Chat
                                </button>
                            </div>

                            <div class="course-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                                <h3 class="text-xl font-semibold mb-4">OpenAI (gpt-4-turbo)</h3>
                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-graduation-cap mr-2"></i>
                                        Course: Open Courses
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        Teacher: Admin Teacher
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar mr-2"></i>
                                        Created on: 22/11/2024, 07:00:36
                                    </div>
                                </div>
                                <button class="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-comments mr-2"></i>
                                    Start Chat
                                </button>
                            </div>

                            <div class="course-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                                <h3 class="text-xl font-semibold mb-4">OpenAI (o1-mini)</h3>
                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-graduation-cap mr-2"></i>
                                        Course: Open Courses
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        Teacher: Admin Teacher
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar mr-2"></i>
                                        Created on: 05/02/2025, 19:27:09
                                    </div>
                                </div>
                                <button class="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-comments mr-2"></i>
                                    Start Chat
                                </button>
                            </div>

                            <div class="course-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                                <h3 class="text-xl font-semibold mb-4">OpenAI (o3-mini-high)</h3>
                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-graduation-cap mr-2"></i>
                                        Course: Open Courses
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        Teacher: Admin Teacher
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar mr-2"></i>
                                        Created on: 05/02/2025, 19:27:38
                                    </div>
                                </div>
                                <button class="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-comments mr-2"></i>
                                    Start Chat
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chatbots Page -->
                <div id="chatbots-page" class="page-content hidden">
                    <h1 class="text-4xl font-bold mb-8">Manage Chatbots</h1>
                    
                    <button onclick="showCreateChatbot()" class="bg-black text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors mb-6">
                        Create New Chatbot
                    </button>
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                        <p class="font-medium mb-2">Hint:</p>
                        <ol class="text-sm space-y-1 list-decimal list-inside">
                            <li>After creating new chatbots, you should create new courses and modules to import those chatbots.</li>
                            <li>Then, you can click "Start to Chat" to talk to the chatbot you created.</li>
                        </ol>
                    </div>
                    
                    <div class="mb-6">
                        <h2 class="text-xl font-semibold mb-4">Chatbot List</h2>
                        <input type="text" placeholder="Enter chatbot information for filtering" class="w-full max-w-md px-4 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 focus:border-primary focus:ring-1 focus:ring-primary outline-none">
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Index</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Chatbot Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Model Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">System Prompt</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">AB_sum_practice</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">OpenAI (gpt-4-turbo)</td>
                                        <td class="px-6 py-4 text-sm max-w-xs truncate">You are an experienced tutor of academic English. Your job...</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <button class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">2</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">LRMove6-8-Grok</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">xAI Grok 2 (grok-2-1212)</td>
                                        <td class="px-6 py-4 text-sm max-w-xs truncate">You are an experienced tutor in academic writing. Your job...</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <button class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">3</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">Move6-8-Claude</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">Anthropic Claude 3.5 (claude-3.5-sonnet)</td>
                                        <td class="px-6 py-4 text-sm max-w-xs truncate">You are an experienced tutor in academic writing. Your job...</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <button class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">4</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">Move6-8-Claude-sonnet</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">Anthropic Claude 3.5 (claude-3.5-sonnet)</td>
                                        <td class="px-6 py-4 text-sm max-w-xs truncate">You are an experienced tutor in academic writing. Your job...</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <button class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Academic Writing Course Page -->
                <div id="academic-writing-page" class="page-content">
                    <div class="flex items-center justify-between mb-8">
                        <h1 class="text-4xl font-bold">Academic Writing</h1>
                        <div class="flex items-center space-x-2">
                            <button class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                                <i class="fas fa-users"></i>
                            </button>
                            <button class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="w-10 h-10 bg-gray-400 text-white rounded-full flex items-center justify-center hover:bg-gray-500 transition-colors">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Paper Writing Module -->
                    <div class="mb-12">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-3xl font-semibold">Paper Writing Assistant</h2>
                            <div class="flex items-center space-x-2">
                                <button class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-edit text-sm"></i>
                                </button>
                                <button class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-plus text-sm"></i>
                                </button>
                                <button class="w-8 h-8 bg-gray-400 text-white rounded-full flex items-center justify-center hover:bg-gray-500 transition-colors">
                                    <i class="fas fa-info-circle text-sm"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- PaperWritingBot Chatbot -->
                            <div class="chatbot-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                                <h3 class="text-xl font-semibold mb-4">PaperWritingBot</h3>
                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-6">
                                    <div class="flex items-center">
                                        <i class="fas fa-graduation-cap mr-2"></i>
                                        Course: Academic Writing
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user mr-2"></i>
                                        Teacher: Dr Simon Wang
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar mr-2"></i>
                                        Created on: 01/02/2025, 15:30:00
                                    </div>
                                </div>
                                <div class="flex items-center justify-center space-x-3">
                                    <button onclick="showChatInterface('paper-writing')" class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors" title="Start Chat">
                                        <i class="fas fa-comments"></i>
                                    </button>
                                    <button class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors" title="Download">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors" title="Settings">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <button class="w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors" title="Delete">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Create Chatbot Page -->
                <div id="create-chatbot-page" class="page-content hidden">
                    <div class="flex items-center mb-8">
                        <button onclick="showPage('chatbots')" class="mr-4 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </button>
                        <h1 class="text-4xl font-bold">Create New Chatbot</h1>
                    </div>
                    
                    <div class="max-w-4xl">
                        <h2 class="text-2xl font-semibold mb-6">Step 1: Enter Basic Chatbot Information</h2>
                        
                        <form id="create-chatbot-form" class="space-y-8">
                            <div>
                                <input type="text" placeholder="Chatbot Name" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                            </div>
                            
                            <div>
                                <label class="block text-sm text-gray-600 dark:text-gray-400 mb-2">Model Name</label>
                                <select class="w-full px-4 py-3 text-base border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 focus:border-primary focus:ring-1 focus:ring-primary outline-none">
                                    <option>OpenAI (gpt-4.1)</option>
                                    <option>OpenAI (gpt-4o)</option>
                                    <option>OpenAI (gpt-4o-mini)</option>
                                    <option>OpenAI (gpt-4-turbo)</option>
                                    <option>Anthropic Claude 3.5 (claude-3.5-sonnet)</option>
                                    <option>xAI Grok 2 (grok-2-1212)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm text-gray-600 dark:text-gray-400 mb-2">System Prompt</label>
                                <textarea rows="8" placeholder="Enter the system prompt for your chatbot..." class="w-full px-4 py-3 text-base border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 focus:border-primary focus:ring-1 focus:ring-primary outline-none resize-none"></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-sm text-gray-600 dark:text-gray-400 mb-2">Welcome Prompt</label>
                                <textarea rows="6" placeholder="Enter the welcome message for your chatbot..." class="w-full px-4 py-3 text-base border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 focus:border-primary focus:ring-1 focus:ring-primary outline-none resize-none"></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-sm text-gray-600 dark:text-gray-400 mb-2">Temperature</label>
                                <input type="number" value="0.7" step="0.1" min="0" max="2" class="w-full px-0 py-3 text-base border-0 border-b-2 border-gray-300 dark:border-gray-600 bg-transparent focus:border-primary focus:ring-0 outline-none">
                            </div>
                            
                            <div>
                                <label class="block text-sm text-primary mb-2">Chatbot Type</label>
                                <select class="w-full px-4 py-3 text-base border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 focus:border-primary focus:ring-1 focus:ring-primary outline-none">
                                    <option>CustomisedChatbot</option>
                                    <option>GeneralChatbot</option>
                                    <option>EducationalChatbot</option>
                                </select>
                            </div>
                        </form>
                        
                        <div class="mt-12">
                            <h2 class="text-2xl font-semibold mb-4">Step 2: Submit</h2>
                            <p class="text-gray-600 dark:text-gray-400 mb-6">Note: After submitting, you can add your new chatbot in the course page.</p>
                            
                            <button onclick="createChatbot()" class="bg-black text-white px-8 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors">
                                Create Chatbot
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- AB-pre Chat Interface - Full Screen -->
    <div id="chat-interface" class="hidden min-h-screen flex flex-col bg-white dark:bg-gray-900">
        <!-- Top Navigation Bar -->
        <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between z-10">
            <div class="flex items-center space-x-4">
                <button onclick="toggleChatSidebar()" class="px-3 py-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded transition-colors text-sm font-medium" title="Toggle Sessions Menu">
                    ☰ Sessions
                </button>
                <h1 class="text-xl font-bold">Bytewise</h1>
                <span class="text-sm text-gray-600 dark:text-gray-400">|</span>
                <span class="text-sm text-gray-600 dark:text-gray-400">PaperWritingBot Sessions</span>
            </div>
            <div class="flex items-center space-x-2">
                <button onclick="showDashboard()" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded" title="Back to Dashboard">
                    <i class="fas fa-home"></i>
                </button>
                <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded" title="New Session">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded" title="More Options">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
        </header>

        <!-- Main Chat Layout -->
        <div class="flex flex-1 relative">
            <!-- Left Sessions Menu -->
            <aside id="chat-left-sidebar" class="flex-shrink-0 w-80 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out h-full">
                <div class="h-full flex flex-col">
                    <!-- Menu Header (Fixed) -->
                    <div class="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between mb-3">
                            <h2 class="font-semibold text-lg text-gray-900 dark:text-white">Chat Sessions</h2>
                            <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
                                ➕
                            </button>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <div>Academic Writing</div>
                            <div class="font-medium text-primary">PaperWritingBot</div>
                        </div>
                    </div>
                    
                    <!-- Sessions List (Scrollable) -->
                    <div class="flex-1 overflow-y-auto p-4 min-h-0">
                        <div class="space-y-2">
                            <!-- Active Session -->
                            <div onclick="selectSession(1)" class="session-item cursor-pointer p-3 bg-primary text-white rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">Writing Session 1</div>
                                        <div class="text-xs opacity-75">Current session</div>
                                    </div>
                                    <div class="flex space-x-1">
                                        <button class="p-1 hover:bg-white/20 rounded" onclick="event.stopPropagation();">
                                            ✏️
                                        </button>
                                        <button class="p-1 hover:bg-white/20 rounded" onclick="event.stopPropagation();">
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Additional sessions for testing scroll -->
                            <div onclick="selectSession(2)" class="session-item cursor-pointer p-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">Writing Session 2</div>
                                        <div class="text-xs text-gray-500">Draft completed</div>
                                    </div>
                                    <div class="flex space-x-1">
                                        <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded" onclick="event.stopPropagation();">
                                            ✏️
                                        </button>
                                        <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded" onclick="event.stopPropagation();">
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div onclick="selectSession(3)" class="session-item cursor-pointer p-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">Research Session</div>
                                        <div class="text-xs text-gray-500">Literature review</div>
                                    </div>
                                    <div class="flex space-x-1">
                                        <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded" onclick="event.stopPropagation();">
                                            ✏️
                                        </button>
                                        <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded" onclick="event.stopPropagation();">
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div onclick="selectSession(4)" class="session-item cursor-pointer p-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">Methodology Discussion</div>
                                        <div class="text-xs text-gray-500">Data analysis</div>
                                    </div>
                                    <div class="flex space-x-1">
                                        <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded" onclick="event.stopPropagation();">
                                            ✏️
                                        </button>
                                        <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded" onclick="event.stopPropagation();">
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div onclick="selectSession(5)" class="session-item cursor-pointer p-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium">Citation Help</div>
                                        <div class="text-xs text-gray-500">APA formatting</div>
                                    </div>
                                    <div class="flex space-x-1">
                                        <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded" onclick="event.stopPropagation();">
                                            ✏️
                                        </button>
                                        <button class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded" onclick="event.stopPropagation();">
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Right Chat Area -->
            <main class="flex-1 flex flex-col min-w-0">
                <!-- Chat Header -->
                <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 id="chat-title" class="text-lg font-medium text-gray-900 dark:text-white">Writing Session 1 - Academic Writing Discussion</h1>
                            <p class="text-sm text-gray-500">PaperWritingBot • Academic Writing</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="toggleRightPanel()" class="px-3 py-2 bg-primary text-white rounded hover:bg-purple-600 transition-colors text-sm font-medium" title="Toggle Context Panel">
                                Context
                            </button>
                            <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded" title="Export Chat">
                                📥
                            </button>
                            <button class="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded" title="Chat Settings">
                                ⚙️
                            </button>
                        </div>
                    </div>
                </header>

                <!-- Chat Messages -->
                <div id="chat-messages" class="flex-1 overflow-y-auto p-6 space-y-6 bg-gray-50 dark:bg-gray-900">
                    <!-- User Message -->
                    <div class="flex justify-end">
                        <div class="max-w-md lg:max-w-lg">
                            <div class="bg-primary text-white px-4 py-3 rounded-lg">
                                <div class="text-sm">Hello! I need help with academic writing.</div>
                            </div>
                            <div class="flex items-center justify-end mt-2">
                                <span class="text-xs text-gray-500 mr-2">You • 2:30 PM</span>
                                <div class="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-xs text-gray-600 dark:text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chatbot Message -->
                    <div class="flex">
                        <div class="max-w-md lg:max-w-lg">
                            <div class="flex items-center mb-2">
                                <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-robot text-sm text-white"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">PaperWritingBot</span>
                                <span class="text-xs text-gray-500 ml-2">2:30 PM</span>
                            </div>
                            <div class="bg-white dark:bg-gray-800 px-4 py-3 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
                                <p class="text-sm text-gray-900 dark:text-white">Hello! I'm here to help you with your academic writing. I can assist you with structure, style, argumentation, citations, and much more. What specific aspect of academic writing would you like to work on today?</p>
                            </div>
                        </div>
                    </div>

                    <!-- User Message -->
                    <div class="flex justify-end">
                        <div class="max-w-md lg:max-w-lg">
                            <div class="bg-primary text-white px-4 py-3 rounded-lg">
                                <div class="text-sm">How do I structure an academic essay?</div>
                            </div>
                            <div class="flex items-center justify-end mt-2">
                                <span class="text-xs text-gray-500 mr-2">You • 2:31 PM</span>
                                <div class="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-xs text-gray-600 dark:text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chatbot Menu Message -->
                    <div class="flex">
                        <div class="max-w-md lg:max-w-lg">
                            <div class="flex items-center mb-2">
                                <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-robot text-sm text-white"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">PaperWritingBot</span>
                                <span class="text-xs text-gray-500 ml-2">2:31 PM</span>
                            </div>
                            <div class="bg-white dark:bg-gray-800 px-4 py-3 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
                                <p class="text-sm text-gray-900 dark:text-white mb-3">Great question! A well-structured academic essay typically follows this format:</p>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded text-sm space-y-1">
                                    <p class="text-gray-900 dark:text-white"><strong>1. Introduction:</strong> Hook, background, thesis statement</p>
                                    <p class="text-gray-900 dark:text-white"><strong>2. Body paragraphs:</strong> Topic sentence, evidence, analysis, transition</p>
                                    <p class="text-gray-900 dark:text-white"><strong>3. Conclusion:</strong> Restate thesis, summarize main points, broader implications</p>
                                </div>
                                <p class="text-sm text-gray-900 dark:text-white mt-3">Would you like me to elaborate on any of these sections? 📝</p>
                            </div>
                        </div>
                    </div>

                    <!-- Loading indicator (hidden by default) -->
                    <div id="loading-indicator" class="hidden flex">
                        <div class="max-w-md lg:max-w-lg">
                            <div class="flex items-center mb-2">
                                <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-robot text-sm text-white"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">PaperWritingBot</span>
                                <span class="text-xs text-gray-500 ml-2">Thinking...</span>
                            </div>
                            <div class="bg-white dark:bg-gray-800 px-4 py-3 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
                                <div class="flex items-center space-x-2">
                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                                    <span class="text-sm text-gray-900 dark:text-white">Thinking...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
                    <div class="flex items-end space-x-3">
                        <div class="flex-1">
                            <div class="relative">
                                <textarea id="chat-input" placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)" class="w-full px-4 py-3 pr-20 text-base border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none" rows="1"></textarea>
                                <div class="absolute right-3 bottom-3 flex items-center space-x-2">
                                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" title="Attach File">
                                        <i class="fas fa-paperclip"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" title="Voice Input">
                                        <i class="fas fa-microphone"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button onclick="sendAIMessage()" id="send-button" class="bg-primary text-white p-3 rounded-lg hover:bg-purple-600 transition-colors flex-shrink-0" title="Send Message">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="mt-2 text-xs text-gray-500 text-center">
                        Press Enter to send • Shift+Enter for new line • Streaming enabled
                    </div>
                </div>
            </main>

            <!-- Right Panel for Draft and Notes -->
            <aside id="right-panel" class="hidden flex-shrink-0 w-96 bg-gray-50 dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out">
                <div class="h-full flex flex-col">
                    <!-- Panel Header -->
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between mb-3">
                            <h2 class="font-semibold text-lg text-gray-900 dark:text-white">Context Panel</h2>
                            <button onclick="toggleRightPanel()" class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded">
                                <i class="fas fa-times text-sm"></i>
                            </button>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            Contextual info for input and output
                        </div>
                    </div>

                    <!-- Panel Content -->
                    <div class="flex-1 overflow-y-auto p-4 space-y-6">
                        <!-- Draft Module -->
                        <div class="module-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="font-semibold text-gray-900 dark:text-white">Draft</h3>
                                <div class="flex items-center space-x-2">
                                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" title="Word Count">
                                        <i class="fas fa-sort-numeric-up text-xs"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" title="Save">
                                        <i class="fas fa-save text-xs"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="mb-3">
                                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Chatbot Instructions for this module:</label>
                                    <textarea class="w-full h-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary resize-none" placeholder="Tell the chatbot how to use this content (e.g., 'Use this draft to provide feedback on structure and content')">Use this draft content to provide specific feedback on academic writing structure, argument development, and citation style. Focus on improving clarity and coherence.</textarea>
                                </div>
                                <textarea id="draft-content" class="w-full h-64 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none" placeholder="Start writing your draft here...">The Impact of Digital Technology on Modern Education

Digital technology has fundamentally transformed the educational landscape in the 21st century. This essay examines how technological advancements have revolutionized teaching methodologies, student engagement, and learning outcomes across various educational institutions.

Introduction:
The rapid advancement of digital technology has brought unprecedented changes to education systems worldwide. From traditional blackboard teaching to interactive digital platforms, the evolution has been remarkable. This transformation raises important questions about the effectiveness of digital tools in enhancing educational quality and accessibility.

Body Paragraph 1:
One of the most significant impacts of digital technology is the democratization of knowledge. Online learning platforms such as MOOCs (Massive Open Online Courses) have made high-quality education accessible to millions of students regardless of their geographical location or economic status...

[Continue writing your academic essay here]</textarea>
                                <div class="mt-2 text-xs text-gray-500 flex justify-between">
                                    <span>Last saved: 2 minutes ago</span>
                                    <span id="word-count">Word count: 156</span>
                                </div>
                            </div>
                        </div>

                        <!-- Background Info Module -->
                        <div class="module-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="font-semibold text-gray-900 dark:text-white">Background Info</h3>
                                <div class="flex items-center space-x-2">
                                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" title="Add Source">
                                        <i class="fas fa-plus text-xs"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" title="Save">
                                        <i class="fas fa-save text-xs"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="mb-3">
                                    <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Chatbot Instructions for this module:</label>
                                    <textarea class="w-full h-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary resize-none" placeholder="Tell the chatbot how to use this content (e.g., 'Use this research to support arguments and provide citations')">Use this background research to provide relevant citations, support arguments with evidence, and suggest additional sources when responding to questions.</textarea>
                                </div>
                                <textarea id="background-content" class="w-full h-48 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none" placeholder="Add background information, sources, and research notes...">Key Sources and Background Information:

1. Smith, J. (2023). "Digital Transformation in Higher Education." Journal of Educational Technology, 45(3), 123-145.
   - Key finding: 78% increase in student engagement with interactive digital tools
   - Methodology: Survey of 2,500 students across 15 universities
   - Relevance: Strong support for thesis about technology improving engagement

2. Chen, L. &amp; Rodriguez, M. (2022). "The Future of Online Learning: Post-Pandemic Perspectives." Educational Research Quarterly, 58(2), 67-89.
   - Focus: Long-term impacts of remote learning adoption
   - Statistical data: 40% of institutions plan to maintain hybrid models
   - Critical perspective: Discusses digital divide concerns

3. Important concepts to include:
   - Digital literacy requirements
   - Accessibility and equity issues
   - Cost-benefit analysis of technology integration
   - Student performance metrics

Research Questions:
- How has technology changed assessment methods?
- What are the long-term implications for traditional education models?</textarea>
                                <div class="mt-2 text-xs text-gray-500">
                                    <span>Last updated: 5 minutes ago</span>
                                </div>
                            </div>
                        </div>

                        <!-- Add New Module Button -->
                        <button onclick="showAddModuleForm()" class="w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-primary hover:text-primary transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Add New Module
                        </button>

                        <!-- Add Module Form (Hidden by default) -->
                        <div id="add-module-form" class="hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 dark:text-white mb-4">Create New Module</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Module Name</label>
                                    <input type="text" id="new-module-name" placeholder="Enter module name (e.g., Research Notes, Outline, etc.)" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Chatbot Instructions</label>
                                    <textarea id="new-module-instructions" placeholder="Tell the chatbot how to use this content..." class="w-full h-20 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none"></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Initial Content (Optional)</label>
                                    <textarea id="new-module-content" placeholder="Add some initial content to get started..." class="w-full h-32 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none"></textarea>
                                </div>
                                <div class="flex space-x-3">
                                    <button onclick="createNewModule()" class="flex-1 bg-primary text-white py-2 px-4 rounded text-sm font-medium hover:bg-purple-600 transition-colors">
                                        Create Module
                                    </button>
                                    <button onclick="hideAddModuleForm()" class="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded text-sm font-medium hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors">
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Dynamic Modules Container -->
                        <div id="dynamic-modules" class="space-y-6">
                            <!-- Additional modules will be added here -->
                        </div>
                    </div>
                </div>
            </aside>
        </div>
    </div>

    <script>
        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Navigation functions
        function showLanding() {
            hideAllPages();
            document.getElementById('landing-page').classList.remove('hidden');
        }

        function showSignup() {
            hideAllPages();
            document.getElementById('signup-page').classList.remove('hidden');
        }

        function showLogin() {
            hideAllPages();
            document.getElementById('login-page').classList.remove('hidden');
        }

        function showDashboard() {
            hideAllPages();
            document.getElementById('dashboard').classList.remove('hidden');
            showPage('notifications');
        }

        function hideAllPages() {
            const pages = ['landing-page', 'signup-page', 'login-page', 'dashboard', 'chat-interface'];
            pages.forEach(page => {
                document.getElementById(page).classList.add('hidden');
            });
        }

        function showPage(pageId) {
            // Hide all page content
            const pageContents = document.querySelectorAll('.page-content');
            pageContents.forEach(content => content.classList.add('hidden'));
            
            // Show selected page
            document.getElementById(pageId + '-page').classList.remove('hidden');
            
            // Update navigation active state
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('text-primary', 'bg-blue-50', 'dark:bg-blue-900/20');
            });
        }

        function showCreateChatbot() {
            showPage('create-chatbot');
        }

        function showCreateNotification() {
            alert('Create notification functionality would be implemented here');
        }

        function createChatbot() {
            alert('Chatbot created successfully!');
            showPage('chatbots');
        }

        function toggleSubmenu(submenuId) {
            const submenu = document.getElementById(submenuId);
            submenu.classList.toggle('hidden');
        }

        // Mobile menu functionality
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('sidebar-hidden');
        });

        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.add('sidebar-hidden');
        });

        // Form submissions
        document.getElementById('signup-form').addEventListener('submit', function(e) {
            e.preventDefault();
            showDashboard();
        });

        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            showDashboard();
        });

        // Chat Interface Functions
        function showChatInterface(chatbotType) {
            // Hide all main pages including dashboard
            hideAllPages();
            
            // Show full-screen chat interface
            document.getElementById('chat-interface').classList.remove('hidden');
        }

        function selectSession(sessionNumber) {
            // Remove active state from all sessions
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('bg-blue-100', 'dark:bg-blue-900/30', 'text-blue-700', 'dark:text-blue-300');
                item.classList.add('hover:bg-gray-100', 'dark:hover:bg-gray-800');
            });
            
            // Add active state to selected session
            const selectedSession = document.querySelector(`[onclick="selectSession(${sessionNumber})"]`);
            if (selectedSession) {
                selectedSession.classList.add('bg-blue-100', 'dark:bg-blue-900/30', 'text-blue-700', 'dark:text-blue-300');
                selectedSession.classList.remove('hover:bg-gray-100', 'dark:hover:bg-gray-800');
            }
            
            // Update chat title
            document.getElementById('chat-title').textContent = `Annotated Bibliography - AB-pre - Session ${sessionNumber}`;
            
            // Load session messages (would typically load from database)
            loadSessionMessages(sessionNumber);
        }

        function loadSessionMessages(sessionNumber) {
            const messagesContainer = document.getElementById('chat-messages');
            
            // Sample messages for different sessions
            const sessionMessages = {
                14: [
                    { type: 'user', content: 'another test' },
                    { type: 'bot', content: 'Thank you for sharing the test input! It looks like you\'ve provided some topics related to smart cities, extended reality, IoT, and urban planning. If you\'d like to proceed with exploring the Annotated Bibliography pre-course writing task, please type <strong>menu</strong> 😊\n\nIf you\'re testing further, feel free to continue, or let me know how I can assist you!' },
                    { type: 'user', content: 'menu' },
                    { type: 'bot', content: 'Please choose one of the following options by typing the corresponding number:\n\n<strong>1.</strong> Learn more about the pre-writing task\n<strong>2.</strong> Get feedback on your completed pre-writing task\n\nLet me know how I can assist you! 😊' }
                ],
                13: [
                    { type: 'user', content: 'Hello' },
                    { type: 'bot', content: 'Hello! Welcome to the Annotated Bibliography pre-course writing assistant. How can I help you today?' }
                ],
                12: [
                    { type: 'user', content: 'What is an annotated bibliography?' },
                    { type: 'bot', content: 'An annotated bibliography is a list of citations to books, articles, and documents. Each citation is followed by a brief descriptive and evaluative paragraph, the annotation.' }
                ]
            };
            
            const messages = sessionMessages[sessionNumber] || [];
            
            // Clear existing messages
            messagesContainer.innerHTML = '';
            
            // Add messages
            messages.forEach(message => {
                const messageDiv = document.createElement('div');
                
                if (message.type === 'user') {
                    messageDiv.innerHTML = `
                        <div class="flex justify-end">
                            <div class="max-w-xs lg:max-w-md">
                                <div class="bg-primary text-white px-4 py-3 rounded-lg">
                                    ${message.content}
                                </div>
                                <div class="flex items-center justify-end mt-1">
                                    <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-sm text-gray-600 dark:text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="flex">
                            <div class="max-w-xs lg:max-w-md">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-gray-400 dark:bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-robot text-sm text-white"></i>
                                    </div>
                                    <span class="text-sm font-medium">Chatbot</span>
                                </div>
                                <div class="bg-gray-100 dark:bg-gray-700 px-4 py-3 rounded-lg ml-11">
                                    <p class="text-sm">${message.content}</p>
                                </div>
                            </div>
                        </div>
                    `;
                }
                
                messagesContainer.appendChild(messageDiv);
            });
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            
            if (message) {
                const messagesContainer = document.getElementById('chat-messages');
                
                // Add user message
                const userMessageDiv = document.createElement('div');
                userMessageDiv.innerHTML = `
                    <div class="flex justify-end">
                        <div class="max-w-xs lg:max-w-md">
                            <div class="bg-primary text-white px-4 py-3 rounded-lg">
                                ${message}
                            </div>
                            <div class="flex items-center justify-end mt-1">
                                <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-sm text-gray-600 dark:text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.appendChild(userMessageDiv);
                
                // Clear input
                input.value = '';
                
                // Simulate bot response
                setTimeout(() => {
                    const botMessageDiv = document.createElement('div');
                    botMessageDiv.innerHTML = `
                        <div class="flex">
                            <div class="max-w-xs lg:max-w-md">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-gray-400 dark:bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-robot text-sm text-white"></i>
                                    </div>
                                    <span class="text-sm font-medium">Chatbot</span>
                                </div>
                                <div class="bg-gray-100 dark:bg-gray-700 px-4 py-3 rounded-lg ml-11">
                                    <p class="text-sm">Thank you for your message: "${message}". How can I assist you further with the Annotated Bibliography task?</p>
                                </div>
                            </div>
                        </div>
                    `;
                    messagesContainer.appendChild(botMessageDiv);
                    
                    // Scroll to bottom
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }, 1000);
                
                // Scroll to bottom
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }

        // Collapsible Sidebar Functions
        function toggleChatSidebar() {
            const sidebar = document.getElementById('chat-left-sidebar');
            
            // Check if sidebar is currently collapsed
            if (sidebar.style.width === '0px' || sidebar.classList.contains('w-0')) {
                // Show sidebar
                sidebar.style.width = '';
                sidebar.classList.remove('w-0');
                sidebar.classList.add('w-80');
            } else {
                // Hide sidebar by setting width to 0
                sidebar.style.width = '0px';
                sidebar.classList.remove('w-80');
                sidebar.classList.add('w-0');
            }
        }

        // AI Chat Functions
        function sendAIMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Disable input and show loading
            input.disabled = true;
            const sendButton = document.getElementById('send-button');
            sendButton.disabled = true;
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            // Add user message
            addUserMessage(message);
            
            // Clear input
            input.value = '';
            
            // Show loading indicator
            showLoadingIndicator();
            
            // Send message to AI
            sendToChatbot(message);
        }

        function addUserMessage(message) {
            const messagesContainer = document.getElementById('chat-messages');
            const userMessageDiv = document.createElement('div');
            userMessageDiv.innerHTML = `
                <div class="flex justify-end">
                    <div class="max-w-xs lg:max-w-md">
                        <div class="bg-primary text-white px-4 py-3 rounded-lg">
                            ${message}
                        </div>
                        <div class="flex items-center justify-end mt-1">
                            <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-sm text-gray-600 dark:text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(userMessageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function addBotMessage(content, isComplete = true) {
            const messagesContainer = document.getElementById('chat-messages');
            
            // Remove existing incomplete message if updating
            const existingIncomplete = messagesContainer.querySelector('.bot-message-incomplete');
            if (existingIncomplete) {
                existingIncomplete.remove();
            }
            
            const botMessageDiv = document.createElement('div');
            if (!isComplete) {
                botMessageDiv.classList.add('bot-message-incomplete');
            }
            
            botMessageDiv.innerHTML = `
                <div class="flex">
                    <div class="max-w-xs lg:max-w-md">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-gray-400 dark:bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-robot text-sm text-white"></i>
                            </div>
                            <span class="text-sm font-medium">AB-pre Chatbot</span>
                        </div>
                        <div class="bg-gray-100 dark:bg-gray-700 px-4 py-3 rounded-lg ml-11">
                            <div class="text-sm">${content.replace(/\n/g, '<br>')}</div>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(botMessageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showLoadingIndicator() {
            document.getElementById('loading-indicator').classList.remove('hidden');
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideLoadingIndicator() {
            document.getElementById('loading-indicator').classList.add('hidden');
        }

        function resetInputState() {
            const input = document.getElementById('chat-input');
            const sendButton = document.getElementById('send-button');
            
            input.disabled = false;
            sendButton.disabled = false;
            sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
            input.focus();
        }

        function sendToChatbot(message) {
            // Demo message - no AI functionality
            setTimeout(() => {
                hideLoadingIndicator();
                addBotMessage(`This is a demo with no AI function. Once AI is enabled, the chatbot will respond based on LLM's processing of user input, system prompt and other contextual info. You sent: "${message}"`, true);
                resetInputState();
            }, 1500);
        }

        // Allow sending messages with Enter key
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendAIMessage();
                    }
                });
            }
        });

        // Right Panel Functions
        function toggleRightPanel() {
            const panel = document.getElementById('right-panel');
            
            if (panel.classList.contains('hidden') || panel.style.display === 'none') {
                // Show panel
                panel.classList.remove('hidden');
                panel.style.display = '';
                panel.style.width = '';
                panel.classList.remove('w-0');
                panel.classList.add('w-96');
            } else {
                // Hide panel
                panel.classList.add('hidden');
                panel.style.display = 'none';
                panel.style.width = '0px';
                panel.classList.remove('w-96');
                panel.classList.add('w-0');
            }
        }

        // Module Management Functions
        let moduleCounter = 0;

        // New Module Management Functions
        function showAddModuleForm() {
            const form = document.getElementById('add-module-form');
            const button = document.querySelector('button[onclick="showAddModuleForm()"]');
            
            if (form && button) {
                form.classList.remove('hidden');
                button.style.display = 'none';
                
                // Focus on the name input
                const nameInput = document.getElementById('new-module-name');
                if (nameInput) {
                    nameInput.focus();
                }
            }
        }

        function hideAddModuleForm() {
            const form = document.getElementById('add-module-form');
            const button = document.querySelector('button[onclick="showAddModuleForm()"]');
            
            if (form && button) {
                form.classList.add('hidden');
                button.style.display = 'block';
                
                // Clear form inputs
                document.getElementById('new-module-name').value = '';
                document.getElementById('new-module-instructions').value = '';
                document.getElementById('new-module-content').value = '';
            }
        }

        function createNewModule() {
            const nameInput = document.getElementById('new-module-name');
            const instructionsInput = document.getElementById('new-module-instructions');
            const contentInput = document.getElementById('new-module-content');
            
            const moduleName = nameInput.value.trim();
            const instructions = instructionsInput.value.trim();
            const initialContent = contentInput.value.trim();
            
            if (!moduleName) {
                alert('Please enter a module name');
                nameInput.focus();
                return;
            }
            
            moduleCounter++;
            const dynamicModulesContainer = document.getElementById('dynamic-modules');
            
            if (!dynamicModulesContainer) {
                console.error('Dynamic modules container not found');
                return;
            }
            
            const newModuleWrapper = document.createElement('div');
            newModuleWrapper.innerHTML = `
                <div class="module-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="font-semibold text-gray-900 dark:text-white">${moduleName.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</h3>
                        <div class="flex items-center space-x-2">
                            <button onclick="deleteModule(this)" class="text-gray-400 hover:text-red-600 dark:hover:text-red-400" title="Delete Module">
                                <i class="fas fa-trash text-xs"></i>
                            </button>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" title="Save">
                                <i class="fas fa-save text-xs"></i>
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="mb-3">
                            <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Chatbot Instructions for this module:</label>
                            <textarea class="w-full h-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary resize-none"
                                     placeholder="Tell the chatbot how to use this content...">${instructions.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</textarea>
                        </div>
                        <textarea class="w-full h-48 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary resize-none"
                                 placeholder="Add your notes here...">${initialContent.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</textarea>
                        <div class="mt-2 text-xs text-gray-500">
                            <span>Created: Just now</span>
                        </div>
                    </div>
                </div>
            `;
            
            dynamicModulesContainer.appendChild(newModuleWrapper);
            console.log('Module created successfully:', moduleName);
            
            // Hide the form and show success message
            hideAddModuleForm();
            
            // Optional: Show a brief success message
            const successMessage = document.createElement('div');
            successMessage.className = 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 px-4 py-2 rounded-lg text-sm';
            successMessage.textContent = `Module "${moduleName}" created successfully!`;
            
            const button = document.querySelector('button[onclick="showAddModuleForm()"]');
            if (button && button.parentNode) {
                button.parentNode.insertBefore(successMessage, button);
                
                // Remove success message after 3 seconds
                setTimeout(() => {
                    if (successMessage.parentNode) {
                        successMessage.parentNode.removeChild(successMessage);
                    }
                }, 3000);
            }
        }

        // Legacy function (keeping for backward compatibility)
        function addNewModule() {
            showAddModuleForm();
        }

        function deleteModule(button) {
            if (confirm('Are you sure you want to delete this module?')) {
                const moduleCard = button.closest('.module-card').parentElement;
                moduleCard.remove();
            }
        }

        // Word Count Function
        function updateWordCount() {
            const draftContent = document.getElementById('draft-content').value;
            const wordCount = draftContent.trim().split(/\s+/).filter(word => word.length > 0).length;
            document.getElementById('word-count').textContent = `Word count: ${wordCount}`;
        }

        // Auto-save functionality
        function autoSave() {
            // In a real application, this would save to a backend
            console.log('Auto-saving content...');
            
            // Update last saved time
            const now = new Date();
            const timeString = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            document.querySelector('.text-xs.text-gray-500 span').textContent = `Last saved: Just now`;
        }

        // Event listeners for content changes
        document.addEventListener('DOMContentLoaded', function() {
            const draftContent = document.getElementById('draft-content');
            const backgroundContent = document.getElementById('background-content');
            
            if (draftContent) {
                draftContent.addEventListener('input', updateWordCount);
                draftContent.addEventListener('input', debounce(autoSave, 2000));
                updateWordCount(); // Initial count
            }
            
            if (backgroundContent) {
                backgroundContent.addEventListener('input', debounce(autoSave, 2000));
            }
        });

        // Debounce function for auto-save
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Initialize - Go directly to chat session
        hideAllPages();
        document.getElementById('chat-interface').classList.remove('hidden');
    </script>



</body></html>