<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Creator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <h1 class="text-3xl font-bold text-center mb-8 text-primary">Test Creator</h1>
        
        <!-- Test Metadata -->
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Configuration</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Test ID</label>
                    <input type="text" id="test-id" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., toefl_official_54">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Duration (minutes)</label>
                    <input type="number" id="test-duration" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="150">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Full Score</label>
                    <input type="number" id="test-full-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="100">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Pass Score</label>
                    <input type="number" id="test-pass-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="60">
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700 mb-6">
            <button onclick="switchTab('reading')" id="tab-reading" class="tab-button active px-6 py-3 text-sm font-medium border-b-2 border-primary text-primary">
                Reading
            </button>
            <button onclick="switchTab('writing')" id="tab-writing" class="tab-button px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                Writing
            </button>
            <button onclick="switchTab('listening')" id="tab-listening" class="tab-button px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                Listening
            </button>
            <button onclick="switchTab('speaking')" id="tab-speaking" class="tab-button px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                Speaking
            </button>
        </div>

        <!-- Reading Component -->
        <div id="reading-content" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <h2 class="text-2xl font-semibold">Reading</h2>
                    
                    <!-- Reading Section Metadata -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Section Configuration</h3>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Duration (minutes)</label>
                                <input type="number" id="reading-duration" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="30">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Full Score</label>
                                <input type="number" id="reading-full-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="40">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Unit Score</label>
                                <input type="number" id="reading-unit-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="1">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Pass Score</label>
                                <input type="number" id="reading-pass-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="24">
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Number of Passages</label>
                                <input type="number" id="reading-num-passages" min="1" max="5" value="1" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            </div>
                            <div>
                                <button onclick="setupReadingPassages()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors w-full mt-6">
                                    Setup Passages
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Reading Instructions -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Reading Instructions</label>
                        <textarea id="reading-instructions" rows="6" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter reading section instructions...">This section measures your ability to understand academic passages in English. You will read passages. In an actual test you will have time to read the passages and answer the questions.

Most questions are worth 1 point but the last question in each set is worth more than 1 point. The directions indicate how many points you may receive.</textarea>
                    </div>

                    <!-- Dynamic Passage Creation Area -->
                    <div id="reading-passages-container">
                        <!-- Passages will be added dynamically -->
                    </div>
                </div>

                <!-- Preview -->
                <div class="space-y-6">
                    <h3 class="text-xl font-semibold">Preview</h3>
                    <div id="reading-preview" class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg min-h-96 max-h-96 overflow-y-auto">
                        <p class="text-gray-500 dark:text-gray-400">Reading content will appear here as you add passages and questions...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Writing Component -->
        <div id="writing-content" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <h2 class="text-2xl font-semibold">Writing Component</h2>
                    
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Writing Situation/Context</label>
                        <textarea id="writing-situation" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe the writing context or situation..."></textarea>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Add Writing Task</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Task Title</label>
                                <input type="text" id="writing-task-title" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., Writing Requirement 1">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Task Description</label>
                                <textarea id="writing-task-description" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe what students need to write..."></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Word Count/Requirements</label>
                                <input type="text" id="writing-requirements" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., 120 words">
                            </div>

                            <button onclick="addWritingTask()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
                                Add Writing Task
                            </button>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <h3 class="text-xl font-semibold">Preview</h3>
                    <div id="writing-preview" class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg min-h-96 max-h-96 overflow-y-auto">
                        <p class="text-gray-500 dark:text-gray-400">Writing tasks will appear here as you add them...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Listening Component -->
        <div id="listening-content" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <h2 class="text-2xl font-semibold">Listening Component</h2>
                    
                    <!-- Audio Upload -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Audio File</label>
                        <input type="file" id="listening-audio" accept="audio/*" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-opacity-90">
                        <p class="text-xs text-gray-500 mt-1">Upload an audio file for the listening component</p>
                    </div>

                    <!-- Listening Context -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Listening Situation/Context</label>
                        <textarea id="listening-situation" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe the listening context..."></textarea>
                    </div>

                    <!-- Add Question Form -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Add Question</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Question Type</label>
                                <select id="listening-question-type" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                                    <option value="multiple-choice">Multiple Choice</option>
                                    <option value="cloze">Cloze Summary/Fill in Blanks</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Question Text</label>
                                <textarea id="listening-question-text" rows="3" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter question text..."></textarea>
                            </div>

                            <div id="listening-options-container">
                                <!-- Options will be added dynamically -->
                            </div>

                            <button onclick="addListeningQuestion()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
                                Add Question
                            </button>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <h3 class="text-xl font-semibold">Preview</h3>
                    <div id="listening-preview" class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg min-h-96 max-h-96 overflow-y-auto">
                        <p class="text-gray-500 dark:text-gray-400">Questions will appear here as you add them...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Speaking Component -->
        <div id="speaking-content" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <h2 class="text-2xl font-semibold">Speaking Component</h2>
                    
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Speaking Situation/Context</label>
                        <textarea id="speaking-situation" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe the speaking context..."></textarea>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Speaking Task</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Task Description</label>
                                <textarea id="speaking-task-description" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe the speaking task..."></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Preparation Time (minutes)</label>
                                <input type="number" id="speaking-prep-time" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="1" min="0" max="10">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Speaking Time (minutes)</label>
                                <input type="number" id="speaking-time" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="3" min="1" max="30">
                            </div>

                            <button onclick="updateSpeakingPreview()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
                                Update Preview
                            </button>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <h3 class="text-xl font-semibold">Preview</h3>
                    <div id="speaking-preview" class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg min-h-96 max-h-96 overflow-y-auto">
                        <p class="text-gray-500 dark:text-gray-400">Speaking task will appear here...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Section -->
        <div class="mt-12 text-center">
            <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg inline-block">
                <h3 class="text-lg font-semibold mb-4">Export Test</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">Test Title</label>
                        <input type="text" id="test-title" class="px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter test title" value="Test Document">
                    </div>
                    <button onclick="exportTest()" class="bg-primary text-white px-6 py-3 rounded-md hover:bg-opacity-90 transition-colors font-semibold">
                        Download Test Package (ZIP)
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Global data storage
        let testData = {
            metadata: {
                id: '',
                duration: 150,
                fullScore: 100,
                passScore: 60
            },
            reading: {
                duration: 30,
                unitScore: 1,
                fullScore: 40,
                passScore: 24,
                instructions: '',
                passages: []
            },
            writing: {
                duration: 30,
                unitScore: 10,
                fullScore: 20,
                passScore: 12,
                situation: '',
                tasks: []
            },
            listening: {
                duration: 30,
                unitScore: 1,
                fullScore: 20,
                passScore: 12,
                situation: '',
                audioFile: null,
                dialogs: []
            },
            speaking: {
                duration: 30,
                unitScore: 10,
                fullScore: 20,
                passScore: 12,
                situation: '',
                topics: []
            }
        };

        let currentReadingPassage = 0;
        let questionCounter = 1;

        // Tab switching
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'dark:text-gray-400', 'dark:hover:text-gray-300');
            });
            
            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // Add active class to selected tab
            const activeTab = document.getElementById('tab-' + tabName);
            activeTab.classList.add('active', 'border-primary', 'text-primary');
            activeTab.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'dark:text-gray-400', 'dark:hover:text-gray-300');
        }

        // Reading component functions - removed old event listeners

        // Reading passage setup functions
        function setupReadingPassages() {
            const numPassages = parseInt(document.getElementById('reading-num-passages').value);
            const container = document.getElementById('reading-passages-container');
            
            // Update test data structure
            testData.reading.passages = [];
            
            let html = '';
            
            for (let i = 0; i < numPassages; i++) {
                testData.reading.passages.push({
                    title: '',
                    content: '',
                    image: '',
                    fullScore: 15,
                    questions: []
                });
                
                html += `
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-4" id="passage-${i}">
                        <h3 class="text-lg font-semibold mb-4">Passage ${i + 1}</h3>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Passage Title</label>
                                <input type="text" id="passage-title-${i}" onchange="updatePassageTitle(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., The Commercialization of Lumber">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Full Score</label>
                                <input type="number" id="passage-score-${i}" onchange="updatePassageScore(${i})" value="15" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Image (optional)</label>
                            <input type="file" id="passage-image-${i}" accept="image/*" onchange="handlePassageImage(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-opacity-90">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Passage Content</label>
                            <textarea id="passage-content-${i}" rows="8" onchange="updatePassageContent(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter the reading passage here. Paragraphs will be automatically numbered."></textarea>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Number of Questions</label>
                                <input type="number" id="passage-num-questions-${i}" min="1" max="20" value="5" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            </div>
                            <div>
                                <button onclick="setupPassageQuestions(${i})" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors w-full mt-6">
                                    Setup Questions
                                </button>
                            </div>
                        </div>
                        
                        <div id="passage-questions-${i}">
                            <!-- Questions will be added here -->
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            updateReadingPreview();
        }

        function updatePassageTitle(passageIndex) {
            const title = document.getElementById(`passage-title-${passageIndex}`).value;
            testData.reading.passages[passageIndex].title = title;
            updateReadingPreview();
        }

        function updatePassageScore(passageIndex) {
            const score = document.getElementById(`passage-score-${passageIndex}`).value;
            testData.reading.passages[passageIndex].fullScore = parseInt(score);
            updateReadingPreview();
        }

        function updatePassageContent(passageIndex) {
            let content = document.getElementById(`passage-content-${passageIndex}`).value;
            
            // Number paragraphs automatically
            const paragraphs = content.split('\n\n').filter(p => p.trim());
            const numberedContent = paragraphs.map((p, index) => {
                if (p.trim()) {
                    return `<span id="paragraph-${index + 1}">${p.trim()}</span>`;
                }
                return p;
            }).join('\n\n');
            
            testData.reading.passages[passageIndex].content = numberedContent;
            updateReadingPreview();
        }

        function handlePassageImage(passageIndex) {
            const file = document.getElementById(`passage-image-${passageIndex}`).files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    testData.reading.passages[passageIndex].image = e.target.result;
                    updateReadingPreview();
                };
                reader.readAsDataURL(file);
            }
        }

        function setupPassageQuestions(passageIndex) {
            const numQuestions = parseInt(document.getElementById(`passage-num-questions-${passageIndex}`).value);
            const container = document.getElementById(`passage-questions-${passageIndex}`);
            
            testData.reading.passages[passageIndex].questions = [];
            
            let html = '<h4 class="text-md font-semibold mt-4 mb-3">Questions:</h4>';
            
            for (let i = 0; i < numQuestions; i++) {
                testData.reading.passages[passageIndex].questions.push({
                    type: 'multiple-choice',
                    text: '',
                    options: [],
                    answer: '',
                    score: 1
                });
                
                html += `
                    <div class="bg-white dark:bg-gray-700 p-4 rounded mb-3" id="question-${passageIndex}-${i}">
                        <div class="flex justify-between items-center mb-3">
                            <h5 class="font-medium">Question ${i + 1}</h5>
                            <div class="flex gap-2">
                                <label class="text-sm">Score:</label>
                                <input type="number" id="question-score-${passageIndex}-${i}" value="1" min="1" max="5" onchange="updateQuestionScore(${passageIndex}, ${i})" class="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600">
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium mb-1">Question Type</label>
                                <select id="question-type-${passageIndex}-${i}" onchange="updateQuestionType(${passageIndex}, ${i})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                                    <option value="multiple-choice">Multiple Choice</option>
                                    <option value="true-false-ng">True/False/Not Given</option>
                                    <option value="fill-blanks">Fill in Blanks</option>
                                    <option value="word-meaning">Word Meaning</option>
                                    <option value="summary">Summary (2 points)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium mb-1">Question Text</label>
                                <textarea id="question-text-${passageIndex}-${i}" onchange="updateQuestionText(${passageIndex}, ${i})" rows="2" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter question text..."></textarea>
                            </div>
                            
                            <div id="question-options-${passageIndex}-${i}">
                                <!-- Question-specific options will be added here -->
                            </div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            
            // Initialize question types for each question
            for (let i = 0; i < numQuestions; i++) {
                updateQuestionType(passageIndex, i);
            }
        }

        function updateQuestionScore(passageIndex, questionIndex) {
            const score = parseInt(document.getElementById(`question-score-${passageIndex}-${questionIndex}`).value);
            testData.reading.passages[passageIndex].questions[questionIndex].score = score;
            updateReadingPreview();
        }

        function updateQuestionType(passageIndex, questionIndex) {
            const questionType = document.getElementById(`question-type-${passageIndex}-${questionIndex}`).value;
            const container = document.getElementById(`question-options-${passageIndex}-${questionIndex}`);
            
            testData.reading.passages[passageIndex].questions[questionIndex].type = questionType;
            
            let html = '';
            
            if (questionType === 'multiple-choice') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Options (one per line)</label>
                        <textarea id="question-options-text-${passageIndex}-${questionIndex}" onchange="updateQuestionOptions(${passageIndex}, ${questionIndex})" rows="3" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option A&#10;Option B&#10;Option C&#10;Option D"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Correct Answer</label>
                        <input type="text" id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., Option A">
                    </div>
                `;
            } else if (questionType === 'true-false-ng') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Correct Answer</label>
                        <select id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            <option value="T">True</option>
                            <option value="F">False</option>
                            <option value="NG">Not Given</option>
                        </select>
                    </div>
                `;
            } else if (questionType === 'fill-blanks') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Answers (one per line, in order)</label>
                        <textarea id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" rows="3" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Answer 1&#10;Answer 2&#10;Answer 3"></textarea>
                    </div>
                `;
            } else if (questionType === 'word-meaning') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Correct Answer</label>
                        <input type="text" id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="The word or phrase">
                    </div>
                `;
            } else if (questionType === 'summary') {
                // Set score to 2 for summary questions
                document.getElementById(`question-score-${passageIndex}-${questionIndex}`).value = 2;
                testData.reading.passages[passageIndex].questions[questionIndex].score = 2;
                
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Summary Options (one per line)</label>
                        <textarea id="question-options-text-${passageIndex}-${questionIndex}" onchange="updateQuestionOptions(${passageIndex}, ${questionIndex})" rows="6" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option A&#10;Option B&#10;Option C&#10;Option D&#10;Option E&#10;Option F"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Correct Answers (comma-separated)</label>
                        <input type="text" id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., Option A, Option C, Option E">
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        function updateQuestionText(passageIndex, questionIndex) {
            const text = document.getElementById(`question-text-${passageIndex}-${questionIndex}`).value;
            testData.reading.passages[passageIndex].questions[questionIndex].text = text;
            updateReadingPreview();
        }

        function updateQuestionOptions(passageIndex, questionIndex) {
            const optionsText = document.getElementById(`question-options-text-${passageIndex}-${questionIndex}`).value;
            const options = optionsText.split('\n').filter(opt => opt.trim());
            testData.reading.passages[passageIndex].questions[questionIndex].options = options;
            updateReadingPreview();
        }

        function updateQuestionAnswer(passageIndex, questionIndex) {
            const answer = document.getElementById(`question-answer-${passageIndex}-${questionIndex}`).value;
            testData.reading.passages[passageIndex].questions[questionIndex].answer = answer;
            updateReadingPreview();
        }

        function addReadingQuestion() {
            const questionType = document.getElementById('reading-question-type').value;
            const questionText = document.getElementById('reading-question-text').value.trim();
            const answer = document.getElementById('reading-answer').value.trim();
            
            if (!questionText || !answer) {
                alert('Please fill in all fields');
                return;
            }
            
            let options = [];
            if (questionType === 'multiple-choice') {
                const optionsText = document.getElementById('reading-options').value.trim();
                if (!optionsText) {
                    alert('Please provide options for multiple choice question');
                    return;
                }
                options = optionsText.split('\n').filter(opt => opt.trim());
            }
            
            const question = {
                type: questionType,
                text: questionText,
                options: options,
                answer: answer
            };
            
            testData.reading.questions.push(question);
            updateReadingPreview();
            
            // Clear form
            document.getElementById('reading-question-text').value = '';
            document.getElementById('reading-answer').value = '';
            if (document.getElementById('reading-options')) {
                document.getElementById('reading-options').value = '';
            }
        }

        function updateReadingPreview() {
            const instructions = document.getElementById('reading-instructions')?.value || '';
            testData.reading.instructions = instructions;
            
            const preview = document.getElementById('reading-preview');
            let html = '';
            
            // Show section metadata
            html += `<div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900 rounded">
                <h4 class="font-semibold text-blue-800 dark:text-blue-200">Reading Section</h4>
                <div class="text-sm text-blue-700 dark:text-blue-300">
                    Duration: ${testData.reading.duration} min | Full Score: ${testData.reading.fullScore} | Pass Score: ${testData.reading.passScore}
                </div>
            </div>`;
            
            if (instructions) {
                html += `<div class="mb-4"><h4 class="font-semibold">Instructions:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${instructions}</div></div>`;
            }
            
            // Show passages
            if (testData.reading.passages && testData.reading.passages.length > 0) {
                testData.reading.passages.forEach((passage, passageIndex) => {
                    if (passage.title || passage.content || passage.questions.length > 0) {
                        html += `<div class="mb-6 border-l-4 border-primary pl-4">`;
                        
                        if (passage.title) {
                            html += `<h4 class="font-semibold text-lg mb-2">${passage.title}</h4>`;
                        }
                        
                        if (passage.fullScore) {
                            html += `<div class="text-sm text-gray-600 dark:text-gray-400 mb-2">Full Score: ${passage.fullScore}</div>`;
                        }
                        
                        if (passage.image) {
                            html += `<div class="mb-3"><img src="${passage.image}" alt="Passage image" class="max-w-full h-auto rounded"></div>`;
                        }
                        
                        if (passage.content) {
                            html += `<div class="bg-white dark:bg-gray-700 p-3 rounded mb-3 text-sm">${passage.content.substring(0, 300)}${passage.content.length > 300 ? '...' : ''}</div>`;
                        }
                        
                        if (passage.questions && passage.questions.length > 0) {
                            html += '<h5 class="font-medium mb-2">Questions:</h5>';
                            passage.questions.forEach((q, qIndex) => {
                                if (q.text) {
                                    html += `<div class="mb-3 p-3 bg-gray-50 dark:bg-gray-600 rounded">
                                        <div class="font-medium">Question ${qIndex + 1} ${q.score > 1 ? `(${q.score} points)` : ''}</div>
                                        <div class="text-sm mt-1">${q.text}</div>
                                        ${q.options && q.options.length > 0 ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1">Options: ${q.options.join(', ')}</div>` : ''}
                                        ${q.answer ? `<div class="text-xs text-green-600 dark:text-green-400 mt-1">Answer: ${q.answer}</div>` : ''}
                                    </div>`;
                                }
                            });
                        }
                        
                        html += `</div>`;
                    }
                });
            }
            
            if (!html || html === '') {
                html = '<p class="text-gray-500 dark:text-gray-400">Reading content will appear here as you add passages and questions...</p>';
            }
            
            preview.innerHTML = html;
        }

        // Auto-update reading preview when instructions change
        document.getElementById('reading-instructions').addEventListener('input', updateReadingPreview);

        // Writing component functions
        function addWritingTask() {
            const title = document.getElementById('writing-task-title').value.trim();
            const description = document.getElementById('writing-task-description').value.trim();
            const requirements = document.getElementById('writing-requirements').value.trim();
            
            if (!title || !description) {
                alert('Please fill in title and description');
                return;
            }
            
            const task = {
                title: title,
                description: description,
                requirements: requirements
            };
            
            testData.writing.tasks.push(task);
            updateWritingPreview();
            
            // Clear form
            document.getElementById('writing-task-title').value = '';
            document.getElementById('writing-task-description').value = '';
            document.getElementById('writing-requirements').value = '';
        }

        function updateWritingPreview() {
            const situation = document.getElementById('writing-situation').value;
            testData.writing.situation = situation;
            
            const preview = document.getElementById('writing-preview');
            let html = '';
            
            if (situation) {
                html += `<div class="mb-4"><h4 class="font-semibold">Situation:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${situation}</div></div>`;
            }
            
            if (testData.writing.tasks.length > 0) {
                html += '<h4 class="font-semibold mb-3">Writing Tasks:</h4>';
                testData.writing.tasks.forEach((task, index) => {
                    html += `<div class="mb-4 p-3 bg-white dark:bg-gray-700 rounded">
                        <div class="font-medium">${task.title}</div>
                        <div class="text-sm mt-1">${task.description}</div>
                        ${task.requirements ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1">Requirements: ${task.requirements}</div>` : ''}
                    </div>`;
                });
            }
            
            if (!html) {
                html = '<p class="text-gray-500 dark:text-gray-400">Writing tasks will appear here as you add them...</p>';
            }
            
            preview.innerHTML = html;
        }

        document.getElementById('writing-situation').addEventListener('input', updateWritingPreview);

        // Listening component functions
        document.getElementById('listening-question-type').addEventListener('change', function() {
            updateListeningOptions();
        });

        function updateListeningOptions() {
            const questionType = document.getElementById('listening-question-type').value;
            const container = document.getElementById('listening-options-container');
            
            let html = '';
            
            if (questionType === 'multiple-choice') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-2">Options (one per line)</label>
                        <textarea id="listening-options" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option A&#10;Option B&#10;Option C&#10;Option D"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Correct Answer</label>
                        <input type="text" id="listening-answer" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., Option A">
                    </div>
                `;
            } else if (questionType === 'cloze') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-2">Answers (one per line, for each blank)</label>
                        <textarea id="listening-answer" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Answer for blank 1&#10;Answer for blank 2&#10;Answer for blank 3"></textarea>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        updateListeningOptions();

        function addListeningQuestion() {
            const questionType = document.getElementById('listening-question-type').value;
            const questionText = document.getElementById('listening-question-text').value.trim();
            const answer = document.getElementById('listening-answer').value.trim();
            
            if (!questionText || !answer) {
                alert('Please fill in all fields');
                return;
            }
            
            let options = [];
            if (questionType === 'multiple-choice') {
                const optionsText = document.getElementById('listening-options').value.trim();
                if (!optionsText) {
                    alert('Please provide options for multiple choice question');
                    return;
                }
                options = optionsText.split('\n').filter(opt => opt.trim());
            }
            
            const question = {
                type: questionType,
                text: questionText,
                options: options,
                answer: answer
            };
            
            testData.listening.questions.push(question);
            updateListeningPreview();
            
            // Clear form
            document.getElementById('listening-question-text').value = '';
            document.getElementById('listening-answer').value = '';
            if (document.getElementById('listening-options')) {
                document.getElementById('listening-options').value = '';
            }
        }

        function updateListeningPreview() {
            const situation = document.getElementById('listening-situation').value;
            testData.listening.situation = situation;
            
            const preview = document.getElementById('listening-preview');
            let html = '';
            
            if (situation) {
                html += `<div class="mb-4"><h4 class="font-semibold">Situation:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${situation}</div></div>`;
            }
            
            if (testData.listening.questions.length > 0) {
                html += '<h4 class="font-semibold mb-3">Questions:</h4>';
                testData.listening.questions.forEach((q, index) => {
                    html += `<div class="mb-4 p-3 bg-white dark:bg-gray-700 rounded">
                        <div class="font-medium">Question ${index + 1} (${q.type})</div>
                        <div class="text-sm mt-1">${q.text}</div>
                        ${q.options.length > 0 ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1">Options: ${q.options.join(', ')}</div>` : ''}
                        <div class="text-xs text-green-600 dark:text-green-400 mt-1">Answer: ${q.answer}</div>
                    </div>`;
                });
            }
            
            if (!html) {
                html = '<p class="text-gray-500 dark:text-gray-400">Questions will appear here as you add them...</p>';
            }
            
            preview.innerHTML = html;
        }

        document.getElementById('listening-situation').addEventListener('input', updateListeningPreview);

        // Handle audio file upload
        document.getElementById('listening-audio').addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                testData.listening.audioFile = e.target.files[0];
            }
        });

        // Speaking component functions
        function updateSpeakingPreview() {
            const situation = document.getElementById('speaking-situation').value;
            const taskDescription = document.getElementById('speaking-task-description').value;
            const prepTime = document.getElementById('speaking-prep-time').value;
            const speakingTime = document.getElementById('speaking-time').value;
            
            testData.speaking.situation = situation;
            testData.speaking.taskDescription = taskDescription;
            testData.speaking.prepTime = prepTime;
            testData.speaking.speakingTime = speakingTime;
            
            const preview = document.getElementById('speaking-preview');
            let html = '';
            
            if (situation) {
                html += `<div class="mb-4"><h4 class="font-semibold">Situation:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${situation}</div></div>`;
            }
            
            if (taskDescription) {
                html += `<div class="mb-4"><h4 class="font-semibold">Task:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${taskDescription}</div></div>`;
            }
            
            if (prepTime || speakingTime) {
                html += `<div class="mb-4"><h4 class="font-semibold">Timing:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">
                    Preparation: ${prepTime || 1} minute(s)<br>
                    Speaking: ${speakingTime || 3} minute(s)
                </div></div>`;
            }
            
            if (!html) {
                html = '<p class="text-gray-500 dark:text-gray-400">Speaking task will appear here...</p>';
            }
            
            preview.innerHTML = html;
        }

        // Export function
        async function exportTest() {
            const zip = new JSZip();
            const testTitle = document.getElementById('test-title').value || 'Test Document';
            
            // Generate markdown content
            let markdown = `---\ntitle: '${testTitle}'\n---\n\n`;
            markdown += `There are 4 sections in this test!\n\n`;
            
            // Reading Component
            if (testData.reading.passage || testData.reading.questions.length > 0) {
                markdown += `## Reading Component\n\`\`\`\nSituation:\n${testData.reading.situation || 'Complete the reading task below.'}\n\`\`\`\n\n`;
                
                if (testData.reading.passage) {
                    markdown += `### Reading Passage\n\n${testData.reading.passage}\n\n`;
                }
                
                if (testData.reading.questions.length > 0) {
                    markdown += `#### Questions\n\n`;
                    testData.reading.questions.forEach((q, index) => {
                        markdown += `#### Question ${index + 1}\n${q.text}\n\n`;
                        
                        if (q.type === 'multiple-choice') {
                            q.options.forEach(option => {
                                markdown += `- [ ] ${option}\n`;
                            });
                            markdown += `\n`;
                        } else if (q.type === 'true-false-ng') {
                            markdown += `- [ ] T\n- [ ] F\n- [ ] NG\n\n`;
                        } else if (q.type === 'fill-blanks') {
                            const answers = q.answer.split('\n');
                            answers.forEach((_, i) => {
                                markdown += `- [ ] (${i + 1}) ___________\n`;
                            });
                            markdown += `\n`;
                        } else if (q.type === 'word-meaning') {
                            markdown += `Answer: ___________\n\n`;
                        }
                    });
                }
            }
            
            // Writing Component
            if (testData.writing.situation || testData.writing.tasks.length > 0) {
                markdown += `## Writing Component\n\`\`\`\nSituation:\n${testData.writing.situation || 'Complete the writing tasks below.'}\n\`\`\`\n\n`;
                
                testData.writing.tasks.forEach((task, index) => {
                    markdown += `### ${task.title}\n${task.description}\n`;
                    if (task.requirements) {
                        markdown += `**Requirements:** ${task.requirements}\n`;
                    }
                    markdown += `\n#### Question ${index + 1}\nPlease write your response here.\n\n`;
                });
            }
            
            // Listening Component
            if (testData.listening.situation || testData.listening.questions.length > 0) {
                markdown += `## Listening Component\n\`\`\`\nSituation:\n${testData.listening.situation || 'Listen to the audio and answer the questions.'}\n\`\`\`\n\n`;
                
                if (testData.listening.audioFile) {
                    markdown += `<audio id="examAudio" preload="auto">\n  <source src="${testData.listening.audioFile.name}" type="audio/mpeg">\n  The browser does not support the audio element.\n</audio>\n\n`;
                }
                
                testData.listening.questions.forEach((q, index) => {
                    markdown += `#### Question ${index + 1}\n${q.text}\n\n`;
                    
                    if (q.type === 'multiple-choice') {
                        q.options.forEach(option => {
                            markdown += `- [ ] ${option}\n`;
                        });
                        markdown += `\n`;
                    } else if (q.type === 'cloze') {
                        const answers = q.answer.split('\n');
                        answers.forEach((_, i) => {
                            markdown += `#### Question ${index + 1}.${i + 1}\nFill in the blank: ___________\n\n`;
                        });
                    }
                });
            }
            
            // Speaking Component
            if (testData.speaking.situation || testData.speaking.taskDescription) {
                markdown += `## Speaking Component\n\`\`\`\nSituation:\n${testData.speaking.situation || 'Complete the speaking task below.'}\n\`\`\`\n\n`;
                
                if (testData.speaking.taskDescription) {
                    markdown += `${testData.speaking.taskDescription}\n\n`;
                    markdown += `**Preparation time:** ${testData.speaking.prepTime} minute(s)\n`;
                    markdown += `**Speaking time:** ${testData.speaking.speakingTime} minute(s)\n\n`;
                }
            }
            
            // Add answer key
            markdown += `## Answer Key\n\n`;
            
            if (testData.reading.questions.length > 0) {
                markdown += `### Reading Component\n`;
                testData.reading.questions.forEach((q, index) => {
                    markdown += `Question ${index + 1}: ${q.answer}\n`;
                });
                markdown += `\n`;
            }
            
            if (testData.listening.questions.length > 0) {
                markdown += `### Listening Component\n`;
                testData.listening.questions.forEach((q, index) => {
                    markdown += `Question ${index + 1}: ${q.answer}\n`;
                });
                markdown += `\n`;
            }
            
            // Add files to zip
            zip.file(`${testTitle}.md`, markdown);
            
            // Add audio file if present
            if (testData.listening.audioFile) {
                zip.file(testData.listening.audioFile.name, testData.listening.audioFile);
            }
            
            // Generate and download zip
            try {
                const content = await zip.generateAsync({type: "blob"});
                const url = URL.createObjectURL(content);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${testTitle.replace(/[^a-z0-9]/gi, '_')}_package.zip`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            } catch (error) {
                alert('Error creating zip file: ' + error.message);
            }
        }
    </script>



</body></html>