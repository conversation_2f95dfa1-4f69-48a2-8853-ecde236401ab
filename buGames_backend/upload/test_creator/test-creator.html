<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Creator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <h1 class="text-3xl font-bold text-center mb-8 text-primary">Test Creator</h1>
        
        <!-- Test Metadata -->
        <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Configuration</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Test ID</label>
                    <input type="text" id="test-id" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., toefl_official_54">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Duration (minutes)</label>
                    <input type="number" id="test-duration" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="150">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Full Score</label>
                    <input type="number" id="test-full-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="100">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Pass Score</label>
                    <input type="number" id="test-pass-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="60">
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700 mb-6">
            <button onclick="switchTab('reading')" id="tab-reading" class="tab-button active px-6 py-3 text-sm font-medium border-b-2 border-primary text-primary">
                Reading
            </button>
            <button onclick="switchTab('writing')" id="tab-writing" class="tab-button px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                Writing
            </button>
            <button onclick="switchTab('listening')" id="tab-listening" class="tab-button px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                Listening
            </button>
            <button onclick="switchTab('speaking')" id="tab-speaking" class="tab-button px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                Speaking
            </button>
        </div>

        <!-- Reading Component -->
        <div id="reading-content" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <h2 class="text-2xl font-semibold">Reading</h2>
                    
                    <!-- Reading Section Metadata -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Section Configuration</h3>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Duration (minutes)</label>
                                <input type="number" id="reading-duration" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="30">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Full Score</label>
                                <input type="number" id="reading-full-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="40">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Unit Score</label>
                                <input type="number" id="reading-unit-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="1">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Pass Score</label>
                                <input type="number" id="reading-pass-score" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="24">
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Number of Passages</label>
                                <input type="number" id="reading-num-passages" min="1" max="5" value="1" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            </div>
                            <div>
                                <button onclick="setupReadingPassages()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors w-full mt-6">
                                    Setup Passages
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Reading Instructions -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Reading Instructions</label>
                        <textarea id="reading-instructions" rows="6" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter reading section instructions...">This section measures your ability to understand academic passages in English. You will read passages. In an actual test you will have time to read the passages and answer the questions.

Most questions are worth 1 point but the last question in each set is worth more than 1 point. The directions indicate how many points you may receive.</textarea>
                    </div>

                    <!-- Dynamic Passage Creation Area -->
                    <div id="reading-passages-container">
                        <!-- Passages will be added dynamically -->
                    </div>
                </div>

                <!-- Preview -->
                <div class="space-y-6">
                    <h3 class="text-xl font-semibold">Preview</h3>
                    <div id="reading-preview" class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg min-h-96 max-h-96 overflow-y-auto">
                        <p class="text-gray-500 dark:text-gray-400">Reading content will appear here as you add passages and questions...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Writing Component -->
        <div id="writing-content" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <h2 class="text-2xl font-semibold">Writing</h2>
                    
                    <!-- Writing Section Metadata -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Section Configuration</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Duration (minutes)</label>
                                <input type="number" id="writing-duration" onchange="updateWritingMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="30">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Full Score</label>
                                <input type="number" id="writing-full-score" onchange="updateWritingMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="20">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Unit Score</label>
                                <input type="number" id="writing-unit-score" onchange="updateWritingMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="10">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Pass Score</label>
                                <input type="number" id="writing-pass-score" onchange="updateWritingMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="12">
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Writing Situation/Context</label>
                        <textarea id="writing-situation" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe the writing context or situation..."></textarea>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Add Writing Task</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Task Title</label>
                                <input type="text" id="writing-task-title" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., Writing Requirement 1">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Task Description</label>
                                <textarea id="writing-task-description" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe what students need to write..."></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Word Count/Requirements</label>
                                <input type="text" id="writing-requirements" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., 120 words">
                            </div>

                            <button onclick="addWritingTask()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
                                Add Writing Task
                            </button>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <h3 class="text-xl font-semibold">Preview</h3>
                    <div id="writing-preview" class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg min-h-96 max-h-96 overflow-y-auto">
                        <p class="text-gray-500 dark:text-gray-400">Writing tasks will appear here as you add them...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Listening Component -->
        <div id="listening-content" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <h2 class="text-2xl font-semibold">Listening</h2>
                    
                    <!-- Listening Section Metadata -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Section Configuration</h3>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Duration (minutes)</label>
                                <input type="number" id="listening-duration" onchange="updateListeningMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="30">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Full Score</label>
                                <input type="number" id="listening-full-score" onchange="updateListeningMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="20">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Unit Score</label>
                                <input type="number" id="listening-unit-score" onchange="updateListeningMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="1">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Pass Score</label>
                                <input type="number" id="listening-pass-score" onchange="updateListeningMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="12">
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Number of Listening Sections</label>
                                <input type="number" id="listening-num-sections" min="1" max="5" value="1" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            </div>
                            <div>
                                <button onclick="setupListeningSections()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors w-full mt-6">
                                    Setup Sections
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Listening Instructions -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Listening Instructions</label>
                        <textarea id="listening-instructions" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter listening section instructions...">This section measures your ability to understand spoken English. You will listen to audio recordings and answer questions about what you hear.</textarea>
                    </div>

                    <!-- Dynamic Listening Sections Creation Area -->
                    <div id="listening-sections-container">
                        <!-- Listening sections will be added dynamically -->
                    </div>
                </div>

                <div class="space-y-6">
                    <h3 class="text-xl font-semibold">Preview</h3>
                    <div id="listening-preview" class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg min-h-96 max-h-96 overflow-y-auto">
                        <p class="text-gray-500 dark:text-gray-400">Questions will appear here as you add them...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Speaking Component -->
        <div id="speaking-content" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-6">
                    <h2 class="text-2xl font-semibold">Speaking</h2>
                    
                    <!-- Speaking Section Metadata -->
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Section Configuration</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Duration (minutes)</label>
                                <input type="number" id="speaking-duration" onchange="updateSpeakingMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="30">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Full Score</label>
                                <input type="number" id="speaking-full-score" onchange="updateSpeakingMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="20">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Unit Score</label>
                                <input type="number" id="speaking-unit-score" onchange="updateSpeakingMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="10">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Pass Score</label>
                                <input type="number" id="speaking-pass-score" onchange="updateSpeakingMetadata()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="12">
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <label class="block text-sm font-medium mb-2">Speaking Situation/Context</label>
                        <textarea id="speaking-situation" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe the speaking context..."></textarea>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold mb-4">Speaking Task</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Task Description</label>
                                <textarea id="speaking-task-description" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Describe the speaking task..."></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Preparation Time (minutes)</label>
                                <input type="number" id="speaking-prep-time" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="1" min="0" max="10">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Speaking Time (minutes)</label>
                                <input type="number" id="speaking-time" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="3" min="1" max="30">
                            </div>

                            <div>
                                <label class="block text-sm font-medium mb-2">Audio File (optional)</label>
                                <input type="file" id="speaking-audio" accept="audio/*" onchange="handleSpeakingAudio()" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-opacity-90">
                                <div id="speaking-audio-preview" class="mt-3 hidden">
                                    <audio controls="" class="w-full" id="speaking-audio-player">
                                        <source id="speaking-audio-source" src="" type="audio/mpeg">
                                        Your browser does not support the audio element.
                                    </audio>
                                </div>
                            </div>

                            <button onclick="updateSpeakingPreview()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
                                Update Preview
                            </button>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <h3 class="text-xl font-semibold">Preview</h3>
                    <div id="speaking-preview" class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg min-h-96 max-h-96 overflow-y-auto">
                        <p class="text-gray-500 dark:text-gray-400">Speaking task will appear here...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Section -->
        <div class="mt-12 text-center">
            <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg inline-block">
                <h3 class="text-lg font-semibold mb-4">Export Test</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">Test Title</label>
                        <input type="text" id="test-title" class="px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter test title" value="Test Document">
                    </div>
                    <button onclick="exportTest()" class="bg-primary text-white px-6 py-3 rounded-md hover:bg-opacity-90 transition-colors font-semibold">
                        Download Test Package (ZIP)
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Global data storage
        let testData = {
            metadata: {
                id: '',
                duration: 150,
                fullScore: 100,
                passScore: 60
            },
            reading: {
                duration: 30,
                unitScore: 1,
                fullScore: 40,
                passScore: 24,
                instructions: '',
                passages: []
            },
            writing: {
                duration: 30,
                unitScore: 10,
                fullScore: 20,
                passScore: 12,
                situation: '',
                tasks: []
            },
            listening: {
                duration: 30,
                unitScore: 1,
                fullScore: 20,
                passScore: 12,
                instructions: '',
                sections: []
            },
            speaking: {
                duration: 30,
                unitScore: 10,
                fullScore: 20,
                passScore: 12,
                situation: '',
                topics: []
            }
        };

        let currentReadingPassage = 0;
        let questionCounter = 1;

        // Tab switching
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'dark:text-gray-400', 'dark:hover:text-gray-300');
            });
            
            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // Add active class to selected tab
            const activeTab = document.getElementById('tab-' + tabName);
            activeTab.classList.add('active', 'border-primary', 'text-primary');
            activeTab.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'dark:text-gray-400', 'dark:hover:text-gray-300');
        }

        // Reading component functions - removed old event listeners

        // Reading passage setup functions
        function setupReadingPassages() {
            const numPassages = parseInt(document.getElementById('reading-num-passages').value);
            const container = document.getElementById('reading-passages-container');
            
            // Update test data structure
            testData.reading.passages = [];
            
            let html = '';
            
            for (let i = 0; i < numPassages; i++) {
                testData.reading.passages.push({
                    title: '',
                    content: '',
                    image: '',
                    fullScore: 15,
                    questions: []
                });
                
                html += `
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-4" id="passage-${i}">
                        <h3 class="text-lg font-semibold mb-4">Passage ${i + 1}</h3>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Passage Title</label>
                                <input type="text" id="passage-title-${i}" onchange="updatePassageTitle(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., The Commercialization of Lumber">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Full Score</label>
                                <input type="number" id="passage-score-${i}" onchange="updatePassageScore(${i})" value="15" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Image (optional)</label>
                            <input type="file" id="passage-image-${i}" accept="image/*" onchange="handlePassageImage(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-opacity-90">
                            <div id="passage-image-preview-${i}" class="mt-3 hidden">
                                <img id="passage-image-display-${i}" src="" alt="Passage preview" class="max-w-full h-48 object-contain rounded border">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Number of Paragraphs</label>
                                    <input type="number" id="passage-num-paragraphs-${i}" min="1" max="10" value="3" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                                </div>
                                <div>
                                    <button onclick="setupPassageParagraphs(${i})" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors w-full mt-6">
                                        Setup Paragraphs
                                    </button>
                                </div>
                            </div>
                            <div id="passage-paragraphs-${i}">
                                <!-- Individual paragraph fields will be added here -->
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Number of Questions</label>
                                <input type="number" id="passage-num-questions-${i}" min="1" max="20" value="5" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            </div>
                            <div>
                                <button onclick="setupPassageQuestions(${i})" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors w-full mt-6">
                                    Setup Questions
                                </button>
                            </div>
                        </div>
                        
                        <div id="passage-questions-${i}">
                            <!-- Questions will be added here -->
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            updateReadingPreview();
        }

        function updatePassageTitle(passageIndex) {
            const title = document.getElementById(`passage-title-${passageIndex}`).value;
            testData.reading.passages[passageIndex].title = title;
            updateReadingPreview();
        }

        function updatePassageScore(passageIndex) {
            const score = document.getElementById(`passage-score-${passageIndex}`).value;
            testData.reading.passages[passageIndex].fullScore = parseInt(score);
            updateReadingPreview();
        }

        function setupPassageParagraphs(passageIndex) {
            const numParagraphs = parseInt(document.getElementById(`passage-num-paragraphs-${passageIndex}`).value);
            const container = document.getElementById(`passage-paragraphs-${passageIndex}`);
            
            let html = '<div class="space-y-3">';
            
            for (let i = 0; i < numParagraphs; i++) {
                html += `
                    <div>
                        <label class="block text-sm font-medium mb-1">Paragraph ${i + 1}</label>
                        <textarea id="paragraph-${passageIndex}-${i}" onchange="updatePassageContent(${passageIndex})" rows="3" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter paragraph ${i + 1} content..."></textarea>
                    </div>
                `;
            }
            
            html += '</div>';
            html += `<div class="mt-4">
                <button onclick="updatePassageContent(${passageIndex})" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                    Refresh Preview
                </button>
            </div>`;
            
            container.innerHTML = html;
        }

        function updatePassageContent(passageIndex) {
            const numParagraphs = parseInt(document.getElementById(`passage-num-paragraphs-${passageIndex}`).value);
            let content = '';
            
            // Collect content from individual paragraph fields
            const paragraphs = [];
            for (let i = 0; i < numParagraphs; i++) {
                const paragraphElement = document.getElementById(`paragraph-${passageIndex}-${i}`);
                if (paragraphElement && paragraphElement.value.trim()) {
                    paragraphs.push(`<span id="paragraph-${i + 1}">${paragraphElement.value.trim()}</span>`);
                }
            }
            
            content = paragraphs.join('\n\n');
            testData.reading.passages[passageIndex].content = content;
            updateReadingPreview();
        }

        function handlePassageImage(passageIndex) {
            const file = document.getElementById(`passage-image-${passageIndex}`).files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    testData.reading.passages[passageIndex].image = e.target.result;
                    
                    // Show image preview
                    const preview = document.getElementById(`passage-image-preview-${passageIndex}`);
                    const display = document.getElementById(`passage-image-display-${passageIndex}`);
                    
                    display.src = e.target.result;
                    preview.classList.remove('hidden');
                    
                    updateReadingPreview();
                };
                reader.readAsDataURL(file);
            }
        }

        function setupPassageQuestions(passageIndex) {
            const numQuestions = parseInt(document.getElementById(`passage-num-questions-${passageIndex}`).value);
            const container = document.getElementById(`passage-questions-${passageIndex}`);
            
            testData.reading.passages[passageIndex].questions = [];
            
            let html = '<h4 class="text-md font-semibold mt-4 mb-3">Questions:</h4>';
            
            for (let i = 0; i < numQuestions; i++) {
                testData.reading.passages[passageIndex].questions.push({
                    type: 'multiple-choice',
                    text: '',
                    options: [],
                    answer: '',
                    score: 1
                });
                
                html += `
                    <div class="bg-white dark:bg-gray-700 p-4 rounded mb-3" id="question-${passageIndex}-${i}">
                        <div class="flex justify-between items-center mb-3">
                            <h5 class="font-medium">Question ${i + 1}</h5>
                            <div class="flex gap-2">
                                <label class="text-sm">Score:</label>
                                <input type="number" id="question-score-${passageIndex}-${i}" value="1" min="1" max="5" onchange="updateQuestionScore(${passageIndex}, ${i})" class="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600">
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium mb-1">Question Type</label>
                                <select id="question-type-${passageIndex}-${i}" onchange="updateQuestionType(${passageIndex}, ${i})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                                    <option value="multiple-choice">Multiple Choice</option>
                                    <option value="true-false-ng">True/False/Not Given</option>
                                    <option value="fill-blanks">Fill in Blanks</option>
                                    <option value="word-meaning">Word Meaning</option>
                                    <option value="summary">Summary (2 points)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium mb-1">Question Text</label>
                                <textarea id="question-text-${passageIndex}-${i}" onchange="updateQuestionText(${passageIndex}, ${i})" rows="2" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter question text..."></textarea>
                            </div>
                            
                            <div id="question-options-${passageIndex}-${i}">
                                <!-- Question-specific options will be added here -->
                            </div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            
            // Initialize question types for each question
            for (let i = 0; i < numQuestions; i++) {
                updateQuestionType(passageIndex, i);
            }
        }

        function updateQuestionScore(passageIndex, questionIndex) {
            const score = parseInt(document.getElementById(`question-score-${passageIndex}-${questionIndex}`).value);
            testData.reading.passages[passageIndex].questions[questionIndex].score = score;
            updateReadingPreview();
        }

        function updateQuestionType(passageIndex, questionIndex) {
            const questionType = document.getElementById(`question-type-${passageIndex}-${questionIndex}`).value;
            const container = document.getElementById(`question-options-${passageIndex}-${questionIndex}`);
            
            testData.reading.passages[passageIndex].questions[questionIndex].type = questionType;
            
            let html = '';
            
            if (questionType === 'multiple-choice') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-2">Answer Options</label>
                        <div class="space-y-2">
                            <div class="flex items-center gap-3">
                                <input type="radio" id="question-correct-a-${passageIndex}-${questionIndex}" name="question-correct-${passageIndex}-${questionIndex}" value="A" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="text-primary focus:ring-primary">
                                <label for="question-correct-a-${passageIndex}-${questionIndex}" class="text-sm font-medium">A.</label>
                                <input type="text" id="question-option-a-${passageIndex}-${questionIndex}" onchange="updateQuestionOptions(${passageIndex}, ${questionIndex})" class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option A">
                            </div>
                            <div class="flex items-center gap-3">
                                <input type="radio" id="question-correct-b-${passageIndex}-${questionIndex}" name="question-correct-${passageIndex}-${questionIndex}" value="B" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="text-primary focus:ring-primary">
                                <label for="question-correct-b-${passageIndex}-${questionIndex}" class="text-sm font-medium">B.</label>
                                <input type="text" id="question-option-b-${passageIndex}-${questionIndex}" onchange="updateQuestionOptions(${passageIndex}, ${questionIndex})" class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option B">
                            </div>
                            <div class="flex items-center gap-3">
                                <input type="radio" id="question-correct-c-${passageIndex}-${questionIndex}" name="question-correct-${passageIndex}-${questionIndex}" value="C" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="text-primary focus:ring-primary">
                                <label for="question-correct-c-${passageIndex}-${questionIndex}" class="text-sm font-medium">C.</label>
                                <input type="text" id="question-option-c-${passageIndex}-${questionIndex}" onchange="updateQuestionOptions(${passageIndex}, ${questionIndex})" class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option C">
                            </div>
                            <div class="flex items-center gap-3">
                                <input type="radio" id="question-correct-d-${passageIndex}-${questionIndex}" name="question-correct-${passageIndex}-${questionIndex}" value="D" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="text-primary focus:ring-primary">
                                <label for="question-correct-d-${passageIndex}-${questionIndex}" class="text-sm font-medium">D.</label>
                                <input type="text" id="question-option-d-${passageIndex}-${questionIndex}" onchange="updateQuestionOptions(${passageIndex}, ${questionIndex})" class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option D">
                            </div>
                        </div>
                    </div>
                `;
            } else if (questionType === 'true-false-ng') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Correct Answer</label>
                        <select id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            <option value="T">True</option>
                            <option value="F">False</option>
                            <option value="NG">Not Given</option>
                        </select>
                    </div>
                `;
            } else if (questionType === 'fill-blanks') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Answers (one per line, in order)</label>
                        <textarea id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" rows="3" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Answer 1&#10;Answer 2&#10;Answer 3"></textarea>
                    </div>
                `;
            } else if (questionType === 'word-meaning') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Correct Answer</label>
                        <input type="text" id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="The word or phrase">
                    </div>
                `;
            } else if (questionType === 'summary') {
                // Set score to 2 for summary questions
                document.getElementById(`question-score-${passageIndex}-${questionIndex}`).value = 2;
                testData.reading.passages[passageIndex].questions[questionIndex].score = 2;
                
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Summary Options (one per line)</label>
                        <textarea id="question-options-text-${passageIndex}-${questionIndex}" onchange="updateQuestionOptions(${passageIndex}, ${questionIndex})" rows="6" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option A&#10;Option B&#10;Option C&#10;Option D&#10;Option E&#10;Option F"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Correct Answers (comma-separated)</label>
                        <input type="text" id="question-answer-${passageIndex}-${questionIndex}" onchange="updateQuestionAnswer(${passageIndex}, ${questionIndex})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., Option A, Option C, Option E">
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        function updateQuestionText(passageIndex, questionIndex) {
            const text = document.getElementById(`question-text-${passageIndex}-${questionIndex}`).value;
            testData.reading.passages[passageIndex].questions[questionIndex].text = text;
            updateReadingPreview();
        }

        function updateQuestionOptions(passageIndex, questionIndex) {
            const questionType = testData.reading.passages[passageIndex].questions[questionIndex].type;
            
            if (questionType === 'multiple-choice') {
                // Collect options from individual input fields
                const options = [];
                const optionA = document.getElementById(`question-option-a-${passageIndex}-${questionIndex}`)?.value || '';
                const optionB = document.getElementById(`question-option-b-${passageIndex}-${questionIndex}`)?.value || '';
                const optionC = document.getElementById(`question-option-c-${passageIndex}-${questionIndex}`)?.value || '';
                const optionD = document.getElementById(`question-option-d-${passageIndex}-${questionIndex}`)?.value || '';
                
                if (optionA) options.push(optionA);
                if (optionB) options.push(optionB);
                if (optionC) options.push(optionC);
                if (optionD) options.push(optionD);
                
                testData.reading.passages[passageIndex].questions[questionIndex].options = options;
            } else {
                // For summary questions, use textarea
                const optionsText = document.getElementById(`question-options-text-${passageIndex}-${questionIndex}`)?.value || '';
                const options = optionsText.split('\n').filter(opt => opt.trim());
                testData.reading.passages[passageIndex].questions[questionIndex].options = options;
            }
            
            updateReadingPreview();
        }

        function updateQuestionAnswer(passageIndex, questionIndex) {
            const questionType = testData.reading.passages[passageIndex].questions[questionIndex].type;
            
            if (questionType === 'multiple-choice') {
                // Get selected radio button value
                const selectedRadio = document.querySelector(`input[name="question-correct-${passageIndex}-${questionIndex}"]:checked`);
                const answer = selectedRadio ? selectedRadio.value : '';
                testData.reading.passages[passageIndex].questions[questionIndex].answer = answer;
            } else {
                // For other question types, use input/textarea
                const answer = document.getElementById(`question-answer-${passageIndex}-${questionIndex}`)?.value || '';
                testData.reading.passages[passageIndex].questions[questionIndex].answer = answer;
            }
            
            updateReadingPreview();
        }

        function addReadingQuestion() {
            const questionType = document.getElementById('reading-question-type').value;
            const questionText = document.getElementById('reading-question-text').value.trim();
            const answer = document.getElementById('reading-answer').value.trim();
            
            if (!questionText || !answer) {
                alert('Please fill in all fields');
                return;
            }
            
            let options = [];
            if (questionType === 'multiple-choice') {
                const optionsText = document.getElementById('reading-options').value.trim();
                if (!optionsText) {
                    alert('Please provide options for multiple choice question');
                    return;
                }
                options = optionsText.split('\n').filter(opt => opt.trim());
            }
            
            const question = {
                type: questionType,
                text: questionText,
                options: options,
                answer: answer
            };
            
            testData.reading.questions.push(question);
            updateReadingPreview();
            
            // Clear form
            document.getElementById('reading-question-text').value = '';
            document.getElementById('reading-answer').value = '';
            if (document.getElementById('reading-options')) {
                document.getElementById('reading-options').value = '';
            }
        }

        function updateReadingPreview() {
            const instructions = document.getElementById('reading-instructions')?.value || '';
            testData.reading.instructions = instructions;
            
            const preview = document.getElementById('reading-preview');
            let html = '';
            
            // Show section metadata
            html += `<div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900 rounded">
                <h4 class="font-semibold text-blue-800 dark:text-blue-200">Reading Section</h4>
                <div class="text-sm text-blue-700 dark:text-blue-300">
                    Duration: ${testData.reading.duration} min | Full Score: ${testData.reading.fullScore} | Pass Score: ${testData.reading.passScore}
                </div>
            </div>`;
            
            if (instructions) {
                html += `<div class="mb-4"><h4 class="font-semibold">Instructions:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${instructions}</div></div>`;
            }
            
            // Show passages
            if (testData.reading.passages && testData.reading.passages.length > 0) {
                testData.reading.passages.forEach((passage, passageIndex) => {
                    if (passage.title || passage.content || passage.questions.length > 0) {
                        html += `<div class="mb-6 border-l-4 border-primary pl-4">`;
                        
                        if (passage.title) {
                            html += `<h4 class="font-semibold text-lg mb-2">${passage.title}</h4>`;
                        }
                        
                        if (passage.fullScore) {
                            html += `<div class="text-sm text-gray-600 dark:text-gray-400 mb-2">Full Score: ${passage.fullScore}</div>`;
                        }
                        
                        if (passage.image) {
                            html += `<div class="mb-3"><img src="${passage.image}" alt="Passage image" class="max-w-full h-auto rounded"></div>`;
                        }
                        
                        if (passage.content) {
                            // Show full content with numbered paragraphs in preview
                            let processedContent = passage.content;
                            
                            // Convert numbered spans to visible paragraph numbers
                            processedContent = processedContent.replace(/<span id="paragraph-(\d+)">(.*?)<\/span>/g, 
                                '<div class="mb-4"><span class="text-xs font-bold text-blue-600 dark:text-blue-400">¶$1</span> $2</div>');
                            
                            html += `<div class="bg-white dark:bg-gray-700 p-3 rounded mb-3 text-sm">${processedContent}</div>`;
                        }
                        
                        if (passage.questions && passage.questions.length > 0) {
                            html += '<h5 class="font-medium mb-2">Questions:</h5>';
                            passage.questions.forEach((q, qIndex) => {
                                if (q.text) {
                                    html += `<div class="mb-3 p-3 bg-gray-50 dark:bg-gray-600 rounded">
                                        <div class="font-medium">Question ${qIndex + 1} ${q.score > 1 ? `(${q.score} points)` : ''}</div>
                                        <div class="text-sm mt-1">${q.text}</div>
                                        ${q.options && q.options.length > 0 ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1">Options: ${q.options.join(', ')}</div>` : ''}
                                        ${q.answer ? `<div class="text-xs text-green-600 dark:text-green-400 mt-1">Answer: ${q.answer}</div>` : ''}
                                    </div>`;
                                }
                            });
                        }
                        
                        html += `</div>`;
                    }
                });
            }
            
            if (!html || html === '') {
                html = '<p class="text-gray-500 dark:text-gray-400">Reading content will appear here as you add passages and questions...</p>';
            }
            
            preview.innerHTML = html;
        }

        // Auto-update reading preview when instructions change
        document.getElementById('reading-instructions').addEventListener('input', updateReadingPreview);

        // Writing component functions
        function addWritingTask() {
            const title = document.getElementById('writing-task-title').value.trim();
            const description = document.getElementById('writing-task-description').value.trim();
            const requirements = document.getElementById('writing-requirements').value.trim();
            
            if (!title || !description) {
                alert('Please fill in title and description');
                return;
            }
            
            const task = {
                title: title,
                description: description,
                requirements: requirements
            };
            
            testData.writing.tasks.push(task);
            updateWritingPreview();
            
            // Clear form
            document.getElementById('writing-task-title').value = '';
            document.getElementById('writing-task-description').value = '';
            document.getElementById('writing-requirements').value = '';
        }

        function updateWritingPreview() {
            const situation = document.getElementById('writing-situation').value;
            testData.writing.situation = situation;
            
            const preview = document.getElementById('writing-preview');
            let html = '';
            
            if (situation) {
                html += `<div class="mb-4"><h4 class="font-semibold">Situation:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${situation}</div></div>`;
            }
            
            if (testData.writing.tasks.length > 0) {
                html += '<h4 class="font-semibold mb-3">Writing Tasks:</h4>';
                testData.writing.tasks.forEach((task, index) => {
                    html += `<div class="mb-4 p-3 bg-white dark:bg-gray-700 rounded">
                        <div class="font-medium">${task.title}</div>
                        <div class="text-sm mt-1">${task.description}</div>
                        ${task.requirements ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1">Requirements: ${task.requirements}</div>` : ''}
                    </div>`;
                });
            }
            
            if (!html) {
                html = '<p class="text-gray-500 dark:text-gray-400">Writing tasks will appear here as you add them...</p>';
            }
            
            preview.innerHTML = html;
        }

        document.getElementById('writing-situation').addEventListener('input', updateWritingPreview);

        // Section metadata update functions
        function updateWritingMetadata() {
            testData.writing.duration = parseInt(document.getElementById('writing-duration').value) || 30;
            testData.writing.unitScore = parseInt(document.getElementById('writing-unit-score').value) || 10;
            testData.writing.fullScore = parseInt(document.getElementById('writing-full-score').value) || 20;
            testData.writing.passScore = parseInt(document.getElementById('writing-pass-score').value) || 12;
            updateWritingPreview();
        }

        function updateListeningMetadata() {
            testData.listening.duration = parseInt(document.getElementById('listening-duration').value) || 30;
            testData.listening.unitScore = parseInt(document.getElementById('listening-unit-score').value) || 1;
            testData.listening.fullScore = parseInt(document.getElementById('listening-full-score').value) || 20;
            testData.listening.passScore = parseInt(document.getElementById('listening-pass-score').value) || 12;
            updateListeningPreview();
        }

        function updateSpeakingMetadata() {
            testData.speaking.duration = parseInt(document.getElementById('speaking-duration').value) || 30;
            testData.speaking.unitScore = parseInt(document.getElementById('speaking-unit-score').value) || 10;
            testData.speaking.fullScore = parseInt(document.getElementById('speaking-full-score').value) || 20;
            testData.speaking.passScore = parseInt(document.getElementById('speaking-pass-score').value) || 12;
            updateSpeakingPreview();
        }

        // Old listening question type functionality removed - now handled in individual sections

        // Listening sections setup functions
        function setupListeningSections() {
            const numSections = parseInt(document.getElementById('listening-num-sections').value);
            const container = document.getElementById('listening-sections-container');
            
            // Update test data structure
            testData.listening.sections = [];
            
            let html = '';
            
            for (let i = 0; i < numSections; i++) {
                testData.listening.sections.push({
                    title: '',
                    audioFile: null,
                    audioUrl: '',
                    image: '',
                    description: '',
                    questions: []
                });
                
                html += `
                    <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-4" id="listening-section-${i}">
                        <h3 class="text-lg font-semibold mb-4">Listening Section ${i + 1}</h3>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Section Title</label>
                                <input type="text" id="listening-section-title-${i}" onchange="updateListeningSectionTitle(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="e.g., Dialog 1">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Section Description</label>
                                <input type="text" id="listening-section-desc-${i}" onchange="updateListeningSectionDesc(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Brief description">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Audio File</label>
                            <input type="file" id="listening-section-audio-${i}" accept="audio/*" onchange="handleListeningSectionAudio(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-opacity-90">
                            <div id="listening-section-audio-preview-${i}" class="mt-3 hidden">
                                <audio controls class="w-full" id="listening-section-audio-player-${i}">
                                    <source id="listening-section-audio-source-${i}" src="" type="audio/mpeg">
                                    Your browser does not support the audio element.
                                </audio>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Image (optional)</label>
                            <input type="file" id="listening-section-image-${i}" accept="image/*" onchange="handleListeningSectionImage(${i})" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-opacity-90">
                            <div id="listening-section-image-preview-${i}" class="mt-3 hidden">
                                <img id="listening-section-image-display-${i}" src="" alt="Section preview" class="max-w-full h-48 object-contain rounded border">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Number of Questions</label>
                                <input type="number" id="listening-section-num-questions-${i}" min="1" max="20" value="3" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                            </div>
                            <div>
                                <button onclick="setupListeningSectionQuestions(${i})" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors w-full mt-6">
                                    Setup Questions
                                </button>
                            </div>
                        </div>
                        
                        <div id="listening-section-questions-${i}">
                            <!-- Questions will be added here -->
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            updateListeningPreview();
        }

        function updateListeningSectionTitle(sectionIndex) {
            const title = document.getElementById(`listening-section-title-${sectionIndex}`).value;
            testData.listening.sections[sectionIndex].title = title;
            updateListeningPreview();
        }

        function updateListeningSectionDesc(sectionIndex) {
            const desc = document.getElementById(`listening-section-desc-${sectionIndex}`).value;
            testData.listening.sections[sectionIndex].description = desc;
            updateListeningPreview();
        }

        function handleListeningSectionAudio(sectionIndex) {
            const file = document.getElementById(`listening-section-audio-${sectionIndex}`).files[0];
            if (file) {
                testData.listening.sections[sectionIndex].audioFile = file;
                
                // Show audio preview
                const preview = document.getElementById(`listening-section-audio-preview-${sectionIndex}`);
                const player = document.getElementById(`listening-section-audio-player-${sectionIndex}`);
                const source = document.getElementById(`listening-section-audio-source-${sectionIndex}`);
                
                const fileURL = URL.createObjectURL(file);
                source.src = fileURL;
                testData.listening.sections[sectionIndex].audioUrl = fileURL;
                
                // Reload the audio element
                player.load();
                preview.classList.remove('hidden');
                
                updateListeningPreview();
            }
        }

        function handleListeningSectionImage(sectionIndex) {
            const file = document.getElementById(`listening-section-image-${sectionIndex}`).files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    testData.listening.sections[sectionIndex].image = e.target.result;
                    
                    // Show image preview
                    const preview = document.getElementById(`listening-section-image-preview-${sectionIndex}`);
                    const display = document.getElementById(`listening-section-image-display-${sectionIndex}`);
                    
                    display.src = e.target.result;
                    preview.classList.remove('hidden');
                    
                    updateListeningPreview();
                };
                reader.readAsDataURL(file);
            }
        }

        function setupListeningSectionQuestions(sectionIndex) {
            const numQuestions = parseInt(document.getElementById(`listening-section-num-questions-${sectionIndex}`).value);
            const container = document.getElementById(`listening-section-questions-${sectionIndex}`);
            
            testData.listening.sections[sectionIndex].questions = [];
            
            let html = '<h4 class="text-md font-semibold mt-4 mb-3">Questions:</h4>';
            
            for (let i = 0; i < numQuestions; i++) {
                testData.listening.sections[sectionIndex].questions.push({
                    type: 'multiple-choice',
                    text: '',
                    options: [],
                    answer: ''
                });
                
                html += `
                    <div class="bg-white dark:bg-gray-700 p-4 rounded mb-3" id="listening-question-${sectionIndex}-${i}">
                        <div class="flex justify-between items-center mb-3">
                            <h5 class="font-medium">Question ${i + 1}</h5>
                        </div>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium mb-1">Question Type</label>
                                <select id="listening-question-type-${sectionIndex}-${i}" onchange="updateListeningQuestionType(${sectionIndex}, ${i})" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700">
                                    <option value="multiple-choice">Multiple Choice</option>
                                    <option value="cloze">Cloze Summary/Fill in Blanks</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium mb-1">Question Text</label>
                                <textarea id="listening-question-text-${sectionIndex}-${i}" onchange="updateListeningQuestionText(${sectionIndex}, ${i})" rows="2" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Enter question text..."></textarea>
                            </div>
                            
                            <div id="listening-question-options-${sectionIndex}-${i}">
                                <!-- Question-specific options will be added here -->
                            </div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
            
            // Initialize question types for each question
            for (let i = 0; i < numQuestions; i++) {
                updateListeningQuestionType(sectionIndex, i);
            }
        }

        function updateListeningQuestionType(sectionIndex, questionIndex) {
            const questionType = document.getElementById(`listening-question-type-${sectionIndex}-${questionIndex}`).value;
            const container = document.getElementById(`listening-question-options-${sectionIndex}-${questionIndex}`);
            
            testData.listening.sections[sectionIndex].questions[questionIndex].type = questionType;
            
            let html = '';
            
            if (questionType === 'multiple-choice') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-2">Answer Options</label>
                        <div class="space-y-2">
                            <div class="flex items-center gap-3">
                                <input type="radio" id="listening-correct-a-${sectionIndex}-${questionIndex}" name="listening-correct-${sectionIndex}-${questionIndex}" value="A" onchange="updateListeningQuestionAnswer(${sectionIndex}, ${questionIndex})" class="text-primary focus:ring-primary">
                                <label for="listening-correct-a-${sectionIndex}-${questionIndex}" class="text-sm font-medium">A.</label>
                                <input type="text" id="listening-option-a-${sectionIndex}-${questionIndex}" onchange="updateListeningQuestionOptions(${sectionIndex}, ${questionIndex})" class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option A">
                            </div>
                            <div class="flex items-center gap-3">
                                <input type="radio" id="listening-correct-b-${sectionIndex}-${questionIndex}" name="listening-correct-${sectionIndex}-${questionIndex}" value="B" onchange="updateListeningQuestionAnswer(${sectionIndex}, ${questionIndex})" class="text-primary focus:ring-primary">
                                <label for="listening-correct-b-${sectionIndex}-${questionIndex}" class="text-sm font-medium">B.</label>
                                <input type="text" id="listening-option-b-${sectionIndex}-${questionIndex}" onchange="updateListeningQuestionOptions(${sectionIndex}, ${questionIndex})" class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option B">
                            </div>
                            <div class="flex items-center gap-3">
                                <input type="radio" id="listening-correct-c-${sectionIndex}-${questionIndex}" name="listening-correct-${sectionIndex}-${questionIndex}" value="C" onchange="updateListeningQuestionAnswer(${sectionIndex}, ${questionIndex})" class="text-primary focus:ring-primary">
                                <label for="listening-correct-c-${sectionIndex}-${questionIndex}" class="text-sm font-medium">C.</label>
                                <input type="text" id="listening-option-c-${sectionIndex}-${questionIndex}" onchange="updateListeningQuestionOptions(${sectionIndex}, ${questionIndex})" class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option C">
                            </div>
                            <div class="flex items-center gap-3">
                                <input type="radio" id="listening-correct-d-${sectionIndex}-${questionIndex}" name="listening-correct-${sectionIndex}-${questionIndex}" value="D" onchange="updateListeningQuestionAnswer(${sectionIndex}, ${questionIndex})" class="text-primary focus:ring-primary">
                                <label for="listening-correct-d-${sectionIndex}-${questionIndex}" class="text-sm font-medium">D.</label>
                                <input type="text" id="listening-option-d-${sectionIndex}-${questionIndex}" onchange="updateListeningQuestionOptions(${sectionIndex}, ${questionIndex})" class="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Option D">
                            </div>
                        </div>
                    </div>
                `;
            } else if (questionType === 'cloze') {
                html = `
                    <div>
                        <label class="block text-sm font-medium mb-1">Answers (one per line, for each blank)</label>
                        <textarea id="listening-question-answer-${sectionIndex}-${questionIndex}" onchange="updateListeningQuestionAnswer(${sectionIndex}, ${questionIndex})" rows="3" class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700" placeholder="Answer 1&#10;Answer 2&#10;Answer 3"></textarea>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        function updateListeningQuestionText(sectionIndex, questionIndex) {
            const text = document.getElementById(`listening-question-text-${sectionIndex}-${questionIndex}`).value;
            testData.listening.sections[sectionIndex].questions[questionIndex].text = text;
            updateListeningPreview();
        }

        function updateListeningQuestionOptions(sectionIndex, questionIndex) {
            const questionType = testData.listening.sections[sectionIndex].questions[questionIndex].type;
            
            if (questionType === 'multiple-choice') {
                // Collect options from individual input fields
                const options = [];
                const optionA = document.getElementById(`listening-option-a-${sectionIndex}-${questionIndex}`)?.value || '';
                const optionB = document.getElementById(`listening-option-b-${sectionIndex}-${questionIndex}`)?.value || '';
                const optionC = document.getElementById(`listening-option-c-${sectionIndex}-${questionIndex}`)?.value || '';
                const optionD = document.getElementById(`listening-option-d-${sectionIndex}-${questionIndex}`)?.value || '';
                
                if (optionA) options.push(optionA);
                if (optionB) options.push(optionB);
                if (optionC) options.push(optionC);
                if (optionD) options.push(optionD);
                
                testData.listening.sections[sectionIndex].questions[questionIndex].options = options;
            }
            
            updateListeningPreview();
        }

        function updateListeningQuestionAnswer(sectionIndex, questionIndex) {
            const questionType = testData.listening.sections[sectionIndex].questions[questionIndex].type;
            
            if (questionType === 'multiple-choice') {
                // Get selected radio button value
                const selectedRadio = document.querySelector(`input[name="listening-correct-${sectionIndex}-${questionIndex}"]:checked`);
                const answer = selectedRadio ? selectedRadio.value : '';
                testData.listening.sections[sectionIndex].questions[questionIndex].answer = answer;
            } else {
                // For cloze questions, use textarea
                const answer = document.getElementById(`listening-question-answer-${sectionIndex}-${questionIndex}`)?.value || '';
                testData.listening.sections[sectionIndex].questions[questionIndex].answer = answer;
            }
            
            updateListeningPreview();
        }

        function addListeningQuestion() {
            const questionType = document.getElementById('listening-question-type').value;
            const questionText = document.getElementById('listening-question-text').value.trim();
            const answer = document.getElementById('listening-answer').value.trim();
            
            if (!questionText || !answer) {
                alert('Please fill in all fields');
                return;
            }
            
            let options = [];
            if (questionType === 'multiple-choice') {
                const optionsText = document.getElementById('listening-options').value.trim();
                if (!optionsText) {
                    alert('Please provide options for multiple choice question');
                    return;
                }
                options = optionsText.split('\n').filter(opt => opt.trim());
            }
            
            const question = {
                type: questionType,
                text: questionText,
                options: options,
                answer: answer
            };
            
            testData.listening.questions.push(question);
            updateListeningPreview();
            
            // Clear form
            document.getElementById('listening-question-text').value = '';
            document.getElementById('listening-answer').value = '';
            if (document.getElementById('listening-options')) {
                document.getElementById('listening-options').value = '';
            }
        }

        function updateListeningPreview() {
            const instructions = document.getElementById('listening-instructions')?.value || '';
            testData.listening.instructions = instructions;
            
            const preview = document.getElementById('listening-preview');
            let html = '';
            
            // Show section metadata
            html += `<div class="mb-4 p-3 bg-green-50 dark:bg-green-900 rounded">
                <h4 class="font-semibold text-green-800 dark:text-green-200">Listening Section</h4>
                <div class="text-sm text-green-700 dark:text-green-300">
                    Duration: ${testData.listening.duration} min | Full Score: ${testData.listening.fullScore} | Pass Score: ${testData.listening.passScore}
                </div>
            </div>`;
            
            if (instructions) {
                html += `<div class="mb-4"><h4 class="font-semibold">Instructions:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${instructions}</div></div>`;
            }
            
            // Show listening sections
            if (testData.listening.sections && testData.listening.sections.length > 0) {
                testData.listening.sections.forEach((section, sectionIndex) => {
                    if (section.title || section.description || section.audioFile || section.questions.length > 0) {
                        html += `<div class="mb-6 border-l-4 border-green-500 pl-4">`;
                        
                        if (section.title) {
                            html += `<h4 class="font-semibold text-lg mb-2">${section.title}</h4>`;
                        }
                        
                        if (section.description) {
                            html += `<div class="text-sm text-gray-600 dark:text-gray-400 mb-2">${section.description}</div>`;
                        }
                        
                        if (section.audioFile) {
                            html += `<div class="mb-3">
                                <div class="text-sm font-medium mb-1">Audio: ${section.audioFile.name}</div>
                                <audio controls class="w-full max-w-md">
                                    <source src="${section.audioUrl}" type="audio/mpeg">
                                    Your browser does not support the audio element.
                                </audio>
                            </div>`;
                        }
                        
                        if (section.image) {
                            html += `<div class="mb-3"><img src="${section.image}" alt="Section image" class="max-w-full h-auto rounded"></div>`;
                        }
                        
                        if (section.questions && section.questions.length > 0) {
                            html += '<h5 class="font-medium mb-2">Questions:</h5>';
                            section.questions.forEach((q, qIndex) => {
                                if (q.text) {
                                    html += `<div class="mb-3 p-3 bg-gray-50 dark:bg-gray-600 rounded">
                                        <div class="font-medium">Question ${qIndex + 1} (${q.type})</div>
                                        <div class="text-sm mt-1">${q.text}</div>
                                        ${q.options && q.options.length > 0 ? `<div class="text-xs text-gray-600 dark:text-gray-400 mt-1">Options: ${q.options.join(', ')}</div>` : ''}
                                        ${q.answer ? `<div class="text-xs text-green-600 dark:text-green-400 mt-1">Answer: ${q.answer}</div>` : ''}
                                    </div>`;
                                }
                            });
                        }
                        
                        html += `</div>`;
                    }
                });
            }
            
            if (!html || html.trim() === '') {
                html = '<p class="text-gray-500 dark:text-gray-400">Listening content will appear here as you add sections and questions...</p>';
            }
            
            preview.innerHTML = html;
        }

        // Auto-update listening preview when instructions change
        document.getElementById('listening-instructions').addEventListener('input', updateListeningPreview);

        // Old listening audio upload removed - now handled in individual sections

        // Speaking component functions
        function handleSpeakingAudio() {
            const file = document.getElementById('speaking-audio').files[0];
            if (file) {
                testData.speaking.audioFile = file;
                
                // Show audio preview
                const preview = document.getElementById('speaking-audio-preview');
                const player = document.getElementById('speaking-audio-player');
                const source = document.getElementById('speaking-audio-source');
                
                const fileURL = URL.createObjectURL(file);
                source.src = fileURL;
                testData.speaking.audioUrl = fileURL;
                
                // Reload the audio element
                player.load();
                preview.classList.remove('hidden');
                
                updateSpeakingPreview();
            }
        }

        function updateSpeakingPreview() {
            const situation = document.getElementById('speaking-situation').value;
            const taskDescription = document.getElementById('speaking-task-description').value;
            const prepTime = document.getElementById('speaking-prep-time').value;
            const speakingTime = document.getElementById('speaking-time').value;
            
            testData.speaking.situation = situation;
            testData.speaking.taskDescription = taskDescription;
            testData.speaking.prepTime = prepTime;
            testData.speaking.speakingTime = speakingTime;
            
            const preview = document.getElementById('speaking-preview');
            let html = '';
            
            if (situation) {
                html += `<div class="mb-4"><h4 class="font-semibold">Situation:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${situation}</div></div>`;
            }
            
            if (taskDescription) {
                html += `<div class="mb-4"><h4 class="font-semibold">Task:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">${taskDescription}</div></div>`;
            }
            
            if (prepTime || speakingTime) {
                html += `<div class="mb-4"><h4 class="font-semibold">Timing:</h4><div class="bg-white dark:bg-gray-700 p-3 rounded mt-2 text-sm">
                    Preparation: ${prepTime || 1} minute(s)<br>
                    Speaking: ${speakingTime || 3} minute(s)
                </div></div>`;
            }
            
            if (!html) {
                html = '<p class="text-gray-500 dark:text-gray-400">Speaking task will appear here...</p>';
            }
            
            preview.innerHTML = html;
        }

        // Export function
        async function exportTest() {
            const zip = new JSZip();
            const testTitle = document.getElementById('test-title').value || 'Test Document';
            const testId = document.getElementById('test-id').value || 'test_document';
            const testDuration = document.getElementById('test-duration').value || '150';
            const testFullScore = document.getElementById('test-full-score').value || '100';
            const testPassScore = document.getElementById('test-pass-score').value || '60';
            let imageCounter = 1;
            let audioCounter = 1;
            
            // Generate markdown content following sample format
            let markdown = `# ${testTitle}\n`;
            markdown += '```\n';
            markdown += `id: ${testId}\n`;
            markdown += `duration: ${testDuration}\n`;
            markdown += `full score: ${testFullScore}\n`;
            markdown += `pass socre: ${testPassScore}\n`;
            markdown += '```\n\n';
            
            // Reading Component
            if (testData.reading.instructions || (testData.reading.passages && testData.reading.passages.length > 0)) {
                markdown += `## Reading\n`;
                markdown += '```\n';
                markdown += `duration: ${document.getElementById('reading-duration').value || '30'}\n`;
                markdown += `unit score: ${document.getElementById('reading-unit-score').value || '1'}\n`;
                markdown += `full score: ${document.getElementById('reading-full-score').value || '40'}\n`;
                markdown += `pass socre: ${document.getElementById('reading-pass-score').value || '24'}\n`;
                markdown += '```\n';
                
                if (testData.reading.instructions) {
                    markdown += `${testData.reading.instructions}\n\n`;
                }
                
                // Process reading passages
                if (testData.reading.passages && testData.reading.passages.length > 0) {
                    for (let passageIndex = 0; passageIndex < testData.reading.passages.length; passageIndex++) {
                        const passage = testData.reading.passages[passageIndex];
                        
                        if (passage.title) {
                            markdown += `### ${passage.title}\n\n`;
                        }
                        
                        // Add passage image if present
                        if (passage.image) {
                            const imageName = `reading_passage_${passageIndex + 1}_image_${imageCounter}.png`;
                            // Convert base64 to blob and add to zip
                            const base64Data = passage.image.split(',')[1];
                            const imageBlob = base64ToBlob(base64Data, 'image/png');
                            zip.file(imageName, imageBlob);
                            markdown += `![Passage Image](${imageName})\n\n`;
                            imageCounter++;
                        }
                        
                        if (passage.content) {
                            // Format content with numbered paragraphs
                            const cleanContent = passage.content.replace(/<span[^>]*>/g, '').replace(/<\/span>/g, '');
                            const paragraphs = cleanContent.split('\n\n').filter(p => p.trim());
                            const numberedParagraphs = paragraphs.map((paragraph, index) => {
                                if (paragraph.trim()) {
                                    return `<span id="paragraph-${index + 1}">${paragraph.trim()}</span>`;
                                }
                                return paragraph;
                            }).join('\n\n');
                            
                            markdown += `${numberedParagraphs}\n\n`;
                        }
                        
                        // Add questions
                        if (passage.questions && passage.questions.length > 0) {
                            markdown += `#### Questions\n\n`;
                            passage.questions.forEach((q, qIndex) => {
                                markdown += `**Question ${qIndex + 1}** ${q.score > 1 ? `(${q.score} points)` : ''}\n${q.text}\n\n`;
                                
                                if (q.type === 'multiple-choice') {
                                    q.options.forEach((option, optIndex) => {
                                        markdown += `${String.fromCharCode(65 + optIndex)}. ${option}\n`;
                                    });
                                    markdown += `\n`;
                                } else if (q.type === 'true-false-ng') {
                                    markdown += `A. True\nB. False\nC. Not Given\n\n`;
                                } else if (q.type === 'fill-blanks') {
                                    const answers = q.answer.split('\n');
                                    answers.forEach((_, i) => {
                                        markdown += `(${i + 1}) ___________  `;
                                    });
                                    markdown += `\n\n`;
                                } else if (q.type === 'word-meaning') {
                                    markdown += `Answer: ___________\n\n`;
                                } else if (q.type === 'summary') {
                                    markdown += `Select the THREE main points from the options below:\n\n`;
                                    q.options.forEach((option, optIndex) => {
                                        markdown += `${String.fromCharCode(65 + optIndex)}. ${option}\n`;
                                    });
                                    markdown += `\n`;
                                }
                            });
                        }
                    }
                }
            }
            
            // Writing Component
            if (testData.writing.situation || testData.writing.tasks.length > 0) {
                markdown += `## Writing\n`;
                markdown += '```\n';
                markdown += `duration: ${document.getElementById('writing-duration').value || '30'}\n`;
                markdown += `unit score: ${document.getElementById('writing-unit-score').value || '10'}\n`;
                markdown += `full score: ${document.getElementById('writing-full-score').value || '20'}\n`;
                markdown += `pass socre: ${document.getElementById('writing-pass-score').value || '12'}\n`;
                markdown += '```\n';
                
                testData.writing.tasks.forEach((task, index) => {
                    markdown += `### ${task.title}\n\n`;
                    markdown += `#### Question ${index + 1}\n`;
                    markdown += `${task.description}\n\n`;
                });
            }
            
            // Listening Component
            if (testData.listening.instructions || (testData.listening.sections && testData.listening.sections.length > 0)) {
                markdown += `## Listening\n`;
                markdown += '```\n';
                markdown += `duration: ${document.getElementById('listening-duration').value || '30'}\n`;
                markdown += `unit score: ${document.getElementById('listening-unit-score').value || '1'}\n`;
                markdown += `full score: ${document.getElementById('listening-full-score').value || '20'}\n`;
                markdown += `pass socre: ${document.getElementById('listening-pass-score').value || '12'}\n`;
                markdown += '```\n';
                
                // Process listening sections
                if (testData.listening.sections && testData.listening.sections.length > 0) {
                    for (let sectionIndex = 0; sectionIndex < testData.listening.sections.length; sectionIndex++) {
                        const section = testData.listening.sections[sectionIndex];
                        
                        if (section.title) {
                            markdown += `### ${section.title}\n\n`;
                        }
                        
                        if (section.description) {
                            markdown += `${section.description}\n\n`;
                        }
                        
                        // Add section audio file
                        if (section.audioFile) {
                            const audioName = `listening_section_${sectionIndex + 1}_audio_${audioCounter}.${getFileExtension(section.audioFile.name)}`;
                            zip.file(audioName, section.audioFile);
                            markdown += `**Audio:** [${audioName}](${audioName})\n\n`;
                            markdown += `<audio controls>\n  <source src="${audioName}" type="audio/mpeg">\n  Your browser does not support the audio element.\n</audio>\n\n`;
                            audioCounter++;
                        }
                        
                        // Add section image if present
                        if (section.image) {
                            const imageName = `listening_section_${sectionIndex + 1}_image_${imageCounter}.png`;
                            const base64Data = section.image.split(',')[1];
                            const imageBlob = base64ToBlob(base64Data, 'image/png');
                            zip.file(imageName, imageBlob);
                            markdown += `![Section Image](${imageName})\n\n`;
                            imageCounter++;
                        }
                        
                        // Add questions  
                        if (section.questions && section.questions.length > 0) {
                            let questionCounter = 1;
                            section.questions.forEach((q, qIndex) => {
                                markdown += `#### Question ${questionCounter}\n`;
                                markdown += `${q.text}\n\n`;
                                
                                if (q.type === 'multiple-choice') {
                                    q.options.forEach((option, optIndex) => {
                                        const isCorrect = q.answer === String.fromCharCode(65 + optIndex);
                                        markdown += `- [${isCorrect ? 'x' : ' '}] ${option}\n`;
                                    });
                                    markdown += `\n`;
                                }
                                questionCounter++;
                            });
                        }
                    }
                }
            }
            
            // Speaking Component
            if (testData.speaking.situation || testData.speaking.taskDescription) {
                markdown += `## Speaking\n`;
                markdown += '```\n';
                markdown += `duration: ${document.getElementById('speaking-duration').value || '30'}\n`;
                markdown += `unit score: ${document.getElementById('speaking-unit-score').value || '10'}\n`;
                markdown += `full score: ${document.getElementById('speaking-full-score').value || '20'}\n`;
                markdown += `pass socre: ${document.getElementById('speaking-pass-score').value || '12'}\n`;
                markdown += '```\n';
                
                if (testData.speaking.taskDescription) {
                    markdown += `### Topic 1\n\n`;
                    markdown += `#### Question 1\n`;
                    markdown += `${testData.speaking.taskDescription}\n\n`;
                    
                    // Add speaking audio file if present
                    if (testData.speaking.audioFile) {
                        const audioName = `speaking_audio_${audioCounter}.${getFileExtension(testData.speaking.audioFile.name)}`;
                        zip.file(audioName, testData.speaking.audioFile);
                        audioCounter++;
                    }
                }
            }
            
            // Add answer key
            markdown += `## Answer Key\n\n`;
            
            // Reading answers
            if (testData.reading.passages && testData.reading.passages.length > 0) {
                markdown += `### Reading Component\n`;
                testData.reading.passages.forEach((passage, passageIndex) => {
                    if (passage.questions && passage.questions.length > 0) {
                        markdown += `**${passage.title || `Passage ${passageIndex + 1}`}:**\n`;
                        passage.questions.forEach((q, qIndex) => {
                            markdown += `Question ${qIndex + 1}: ${q.answer}\n`;
                        });
                        markdown += `\n`;
                    }
                });
            }
            
            // Listening answers
            if (testData.listening.sections && testData.listening.sections.length > 0) {
                markdown += `### Listening Component\n`;
                testData.listening.sections.forEach((section, sectionIndex) => {
                    if (section.questions && section.questions.length > 0) {
                        markdown += `**${section.title || `Section ${sectionIndex + 1}`}:**\n`;
                        section.questions.forEach((q, qIndex) => {
                            markdown += `Question ${qIndex + 1}: ${q.answer}\n`;
                        });
                        markdown += `\n`;
                    }
                });
            }
            
            // Add files to zip
            zip.file(`${testTitle}.md`, markdown);
            
            // Generate and download zip
            try {
                const content = await zip.generateAsync({type: "blob"});
                const url = URL.createObjectURL(content);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${testTitle.replace(/[^a-z0-9]/gi, '_')}_package.zip`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            } catch (error) {
                alert('Error creating zip file: ' + error.message);
            }
        }

        // Helper functions for export
        function base64ToBlob(base64, mimeType) {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], {type: mimeType});
        }

        function getFileExtension(filename) {
            return filename.split('.').pop() || 'mp3';
        }
    </script>



</body></html>