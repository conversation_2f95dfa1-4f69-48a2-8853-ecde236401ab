<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flexible Chatbot Customization Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        background: {
                            light: '#FFFFFF',
                            dark: '#181818'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @media (prefers-color-scheme: dark) {
            html { color-scheme: dark; }
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark text-gray-900 dark:text-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <header class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">Flexible Chatbot Customization Generator</h1>
            <p class="text-gray-600 dark:text-gray-400">Build your chatbot specification section by section</p>
        </header>

        <!-- Input/Output Selectors -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-3">
                    <label for="inputSelect" class="text-lg font-medium text-blue-600 dark:text-blue-400 flex items-center gap-2">
                        📥 Input Configuration
                    </label>
                    <select id="inputSelect" class="w-full px-3 py-2 text-base border border-blue-300 dark:border-blue-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700">
                        <option value="">Choose input type to configure...</option>
                        <option value="taskBackground">Task Background &amp; Context</option>
                        <option value="techniques">Techniques &amp; Examples</option>
                        <option value="objectives">Objectives &amp; Tasks</option>
                        <option value="chatbotConfig">Chatbot Configuration</option>
                        <option value="inputHandling">Specific Input Types</option>
                        <option value="documentation">Documentation</option>
                        <option value="wildcard">➕ Add Custom Input</option>
                    </select>
                    <button id="addCustomInputBtn" class="hidden w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
                        Add Custom Input
                    </button>
                </div>
                
                <div class="space-y-3">
                    <label for="outputSelect" class="text-lg font-medium text-green-600 dark:text-green-400 flex items-center gap-2">
                        📤 Output Configuration
                    </label>
                    <select id="outputSelect" class="w-full px-3 py-2 text-base border border-green-300 dark:border-green-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white dark:bg-gray-700">
                        <option value="">Choose output type to configure...</option>
                        <option value="assignmentDraft">Assignment Draft</option>
                        <option value="reportDraft">Report Draft</option>
                        <option value="feedbackSummary">Feedback Summary</option>
                        <option value="progressReport">Progress Report</option>
                        <option value="analysisResult">Analysis Result</option>
                        <option value="recommendationList">Recommendation List</option>
                        <option value="studyGuide">Study Guide</option>
                        <option value="customOutput">➕ Add Custom Output</option>
                    </select>
                    <button id="addCustomOutputBtn" class="hidden w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors">
                        Add Custom Output
                    </button>
                </div>
            </div>
        </div>

        <!-- Progress Indicator -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg mb-6">
            <h3 class="text-sm font-medium mb-2">Configuration Progress</h3>
            <div id="progressContainer" class="flex flex-wrap gap-2"></div>
        </div>

        <!-- Custom Section Creator -->
        <div id="customSectionCreator" class="hidden bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-6">
            <h3 class="text-xl font-semibold mb-4 text-primary">Create Custom Section</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="customSectionName" class="block text-sm font-medium mb-2">Section Name</label>
                    <input type="text" id="customSectionName" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="e.g., Assessment Criteria">
                </div>
                <div>
                    <label for="customSectionType" class="block text-sm font-medium mb-2">Section Type</label>
                    <select id="customSectionType" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700">
                        <option value="keyValue">Key-Value Pairs</option>
                        <option value="list">List Items</option>
                        <option value="text">Text Content</option>
                        <option value="structured">Structured Data</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Dynamic Content Area -->
        <div id="contentArea" class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-6 hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 id="sectionTitle" class="text-xl font-semibold text-primary"></h3>
                <button id="saveSection" class="px-4 py-2 bg-primary hover:bg-purple-700 text-white rounded-md transition-colors">
                    Save Section
                </button>
            </div>
            <div id="tableContainer"></div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button type="button" id="previewBtn" class="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors">
                Preview Markdown
            </button>
            <button type="button" id="generateBtn" class="px-6 py-3 bg-primary hover:bg-purple-700 text-white font-medium rounded-lg transition-colors">
                Generate &amp; Download
            </button>
            <button type="button" id="clearAllBtn" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors">
                Clear All Data
            </button>
        </div>

        <!-- Preview Modal -->
        <div id="previewModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[80vh] overflow-hidden">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xl font-semibold">Markdown Preview</h3>
                </div>
                <div class="p-6 overflow-y-auto max-h-[60vh]">
                    <pre id="previewContent" class="text-sm bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-x-auto whitespace-pre-wrap"></pre>
                </div>
                <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
                    <button id="closePreviewBtn" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                        Close
                    </button>
                    <button id="downloadFromPreviewBtn" class="px-4 py-2 bg-primary hover:bg-purple-700 text-white rounded">
                        Download
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode handling
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Data storage
        let chatbotData = {};
        let customSections = {};

        // Section configurations
        const sectionConfigs = {
            taskBackground: {
                title: 'Task Background & Context',
                type: 'keyValue',
                fields: [
                    { key: 'background', label: 'Background Description', type: 'textarea' },
                    { key: 'context', label: 'Additional Context', type: 'textarea' },
                    { key: 'constraints', label: 'Constraints', type: 'textarea' },
                    { key: 'scope', label: 'Scope', type: 'text' },
                    { key: 'inputHandling', label: 'How Chatbot Should Handle This Input', type: 'textarea', placeholder: 'Explain how the chatbot should process and use this background information...' }
                ]
            },
            techniques: {
                title: 'Techniques & Examples',
                type: 'structured',
                fields: [
                    { key: 'technique', label: 'Technique Name', type: 'text' },
                    { key: 'description', label: 'Description', type: 'textarea' },
                    { key: 'example', label: 'Example Application', type: 'textarea' },
                    { key: 'difficulty', label: 'Difficulty Level', type: 'select', options: ['Beginner', 'Intermediate', 'Advanced'] },
                    { key: 'inputHandling', label: 'How Chatbot Should Use This Technique', type: 'textarea', placeholder: 'Explain how the chatbot should apply this technique in conversations...' }
                ]
            },
            objectives: {
                title: 'Objectives & Tasks',
                type: 'structured',
                fields: [
                    { key: 'objective', label: 'Objective', type: 'text' },
                    { key: 'description', label: 'Description', type: 'textarea' },
                    { key: 'priority', label: 'Priority', type: 'select', options: ['High', 'Medium', 'Low'] },
                    { key: 'deliverable', label: 'Expected Deliverable', type: 'text' },
                    { key: 'inputHandling', label: 'How Chatbot Should Guide Toward This Objective', type: 'textarea', placeholder: 'Explain how the chatbot should help users achieve this objective...' }
                ]
            },
            chatbotConfig: {
                title: 'Chatbot Configuration',
                type: 'keyValue',
                fields: [
                    { key: 'role', label: 'Chatbot Role', type: 'text' },
                    { key: 'personality', label: 'Personality Traits', type: 'textarea' },
                    { key: 'menuItems', label: 'Menu Items & Responses', type: 'textarea' },
                    { key: 'workflow', label: 'Interaction Workflow', type: 'textarea' },
                    { key: 'responseStyle', label: 'Response Style', type: 'text' },
                    { key: 'inputHandling', label: 'General Input Processing Guidelines', type: 'textarea', placeholder: 'Explain how the chatbot should generally process user inputs...' }
                ]
            },
            inputHandling: {
                title: 'Input Handling',
                type: 'structured',
                fields: [
                    { key: 'inputType', label: 'Input Type', type: 'select', options: ['Text Message', 'File Upload', 'Voice Input', 'Image Upload', 'Structured Data', 'Command', 'Question', 'Request for Help'] },
                    { key: 'inputDescription', label: 'Input Description', type: 'textarea' },
                    { key: 'processingMethod', label: 'Processing Method', type: 'textarea', placeholder: 'How should the chatbot process this type of input?' },
                    { key: 'expectedResponse', label: 'Expected Response Type', type: 'text' },
                    { key: 'handlingInstructions', label: 'Detailed Handling Instructions', type: 'textarea', placeholder: 'Provide specific instructions for how the chatbot should handle this input...' }
                ]
            },
            assignmentDraft: {
                title: 'Assignment Draft',
                type: 'keyValue',
                fields: [
                    { key: 'structure', label: 'Document Structure', type: 'textarea', placeholder: 'Define the structure of the assignment (e.g., introduction, main body, conclusion)...' },
                    { key: 'contentGuidelines', label: 'Content Guidelines', type: 'textarea', placeholder: 'Specify what content should be included in each section...' },
                    { key: 'formattingRules', label: 'Formatting Rules', type: 'textarea', placeholder: 'Define formatting requirements (citations, headings, etc.)...' },
                    { key: 'updateTriggers', label: 'When to Update Draft', type: 'textarea', placeholder: 'Specify when the chatbot should update or regenerate the draft...' },
                    { key: 'updateInstructions', label: 'How Chatbot Should Update This Output', type: 'textarea', placeholder: 'Explain how the chatbot should modify and improve the assignment draft...' }
                ]
            },
            reportDraft: {
                title: 'Report Draft',
                type: 'keyValue',
                fields: [
                    { key: 'reportType', label: 'Report Type', type: 'select', options: ['Research Report', 'Progress Report', 'Analysis Report', 'Summary Report', 'Evaluation Report'] },
                    { key: 'sections', label: 'Report Sections', type: 'textarea', placeholder: 'Define the sections and their purposes (e.g., Executive Summary, Methodology, Findings)...' },
                    { key: 'dataRequirements', label: 'Data Requirements', type: 'textarea', placeholder: 'Specify what data or information should be included...' },
                    { key: 'visualElements', label: 'Visual Elements', type: 'textarea', placeholder: 'Define charts, graphs, or tables to include...' },
                    { key: 'updateInstructions', label: 'How Chatbot Should Update This Output', type: 'textarea', placeholder: 'Explain how the chatbot should develop and refine the report...' }
                ]
            },
            feedbackSummary: {
                title: 'Feedback Summary',
                type: 'keyValue',
                fields: [
                    { key: 'feedbackCategories', label: 'Feedback Categories', type: 'textarea', placeholder: 'Define categories of feedback (e.g., strengths, areas for improvement, specific suggestions)...' },
                    { key: 'format', label: 'Summary Format', type: 'textarea', placeholder: 'Specify how feedback should be organized and presented...' },
                    { key: 'tone', label: 'Tone and Style', type: 'textarea', placeholder: 'Define the tone (constructive, encouraging, specific)...' },
                    { key: 'actionItems', label: 'Action Items', type: 'textarea', placeholder: 'How should actionable recommendations be formatted...' },
                    { key: 'updateInstructions', label: 'How Chatbot Should Update This Output', type: 'textarea', placeholder: 'Explain how the chatbot should compile and update feedback...' }
                ]
            },
            progressReport: {
                title: 'Progress Report',
                type: 'keyValue',
                fields: [
                    { key: 'metrics', label: 'Progress Metrics', type: 'textarea', placeholder: 'Define what metrics to track (completion percentage, milestones, etc.)...' },
                    { key: 'timeframes', label: 'Reporting Timeframes', type: 'textarea', placeholder: 'Specify reporting periods (daily, weekly, by milestone)...' },
                    { key: 'visualFormat', label: 'Visual Format', type: 'textarea', placeholder: 'Define how progress should be visualized (charts, progress bars, etc.)...' },
                    { key: 'alerts', label: 'Progress Alerts', type: 'textarea', placeholder: 'When should the chatbot alert about progress issues...' },
                    { key: 'updateInstructions', label: 'How Chatbot Should Update This Output', type: 'textarea', placeholder: 'Explain how the chatbot should track and report progress...' }
                ]
            },
            analysisResult: {
                title: 'Analysis Result',
                type: 'keyValue',
                fields: [
                    { key: 'analysisType', label: 'Analysis Type', type: 'select', options: ['Content Analysis', 'Performance Analysis', 'Comparative Analysis', 'Trend Analysis', 'Quality Analysis'] },
                    { key: 'methodology', label: 'Analysis Methodology', type: 'textarea', placeholder: 'Define how the analysis should be conducted...' },
                    { key: 'presentationFormat', label: 'Result Presentation', type: 'textarea', placeholder: 'Specify how results should be presented (summary, detailed findings, recommendations)...' },
                    { key: 'confidenceLevels', label: 'Confidence Levels', type: 'textarea', placeholder: 'How should certainty/confidence be communicated...' },
                    { key: 'updateInstructions', label: 'How Chatbot Should Update This Output', type: 'textarea', placeholder: 'Explain how the chatbot should perform and present analysis...' }
                ]
            },
            recommendationList: {
                title: 'Recommendation List',
                type: 'keyValue',
                fields: [
                    { key: 'categories', label: 'Recommendation Categories', type: 'textarea', placeholder: 'Define types of recommendations (immediate actions, long-term goals, resources)...' },
                    { key: 'prioritization', label: 'Prioritization Method', type: 'textarea', placeholder: 'How should recommendations be prioritized and ranked...' },
                    { key: 'format', label: 'List Format', type: 'textarea', placeholder: 'Define the format (numbered, bulleted, categorized)...' },
                    { key: 'rationale', label: 'Include Rationale', type: 'textarea', placeholder: 'Should reasons be provided for each recommendation...' },
                    { key: 'updateInstructions', label: 'How Chatbot Should Update This Output', type: 'textarea', placeholder: 'Explain how the chatbot should generate and update recommendations...' }
                ]
            },
            studyGuide: {
                title: 'Study Guide',
                type: 'keyValue',
                fields: [
                    { key: 'structure', label: 'Guide Structure', type: 'textarea', placeholder: 'Define the organization (topics, subtopics, difficulty levels)...' },
                    { key: 'contentTypes', label: 'Content Types', type: 'textarea', placeholder: 'Specify types of content (summaries, key points, examples, practice questions)...' },
                    { key: 'learningObjectives', label: 'Learning Objectives', type: 'textarea', placeholder: 'How should learning objectives be integrated...' },
                    { key: 'interactiveElements', label: 'Interactive Elements', type: 'textarea', placeholder: 'Define interactive components (self-assessment, checkboxes, etc.)...' },
                    { key: 'updateInstructions', label: 'How Chatbot Should Update This Output', type: 'textarea', placeholder: 'Explain how the chatbot should create and maintain the study guide...' }
                ]
            },
            documentation: {
                title: 'Documentation',
                type: 'keyValue',
                fields: [
                    { key: 'contextDoc', label: 'Context Document Structure', type: 'textarea' },
                    { key: 'outputDoc', label: 'Output Document Format', type: 'textarea' },
                    { key: 'updateProcess', label: 'Update Process', type: 'textarea' },
                    { key: 'versionControl', label: 'Version Control Strategy', type: 'text' },
                    { key: 'inputHandling', label: 'Documentation Handling Instructions', type: 'textarea', placeholder: 'How should the chatbot maintain and update documentation...' }
                ]
            }
        };

        function updateProgress() {
            const container = document.getElementById('progressContainer');
            container.innerHTML = '';
            
            const allSections = [...Object.keys(sectionConfigs), ...Object.keys(customSections)];
            allSections.forEach(sectionId => {
                const hasData = chatbotData[sectionId] && Object.keys(chatbotData[sectionId]).length > 0;
                const sectionName = sectionConfigs[sectionId]?.title || customSections[sectionId]?.title || sectionId;
                
                const badge = document.createElement('span');
                let badgeColor = 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400';
                
                if (hasData) {
                    if (sectionId === 'inputHandling') {
                        badgeColor = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                    } else if (sectionId === 'outputDefinition') {
                        badgeColor = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                    } else {
                        badgeColor = 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
                    }
                }
                
                badge.className = `px-3 py-1 rounded-full text-sm ${badgeColor}`;
                
                // Add icons for input/output sections
                let iconPrefix = '';
                if (sectionId === 'inputHandling') {
                    iconPrefix = '📥 ';
                } else if (sectionId === 'outputDefinition') {
                    iconPrefix = '📤 ';
                }
                
                badge.textContent = iconPrefix + sectionName;
                container.appendChild(badge);
            });
        }

        function createTable(config) {
            const container = document.getElementById('tableContainer');
            container.innerHTML = '';

            if (config.type === 'keyValue') {
                createKeyValueTable(config, container);
            } else if (config.type === 'structured') {
                createStructuredTable(config, container);
            } else if (config.type === 'list') {
                createListTable(config, container);
            } else if (config.type === 'text') {
                createTextTable(config, container);
            }
        }

        function createKeyValueTable(config, container) {
            const table = document.createElement('div');
            table.className = 'space-y-4';

            config.fields.forEach(field => {
                const row = document.createElement('div');
                row.className = 'grid grid-cols-1 md:grid-cols-4 gap-4 items-start';

                const label = document.createElement('label');
                label.className = 'font-medium text-sm md:text-right md:pt-2';
                label.textContent = field.label;

                const inputContainer = document.createElement('div');
                inputContainer.className = 'md:col-span-3';

                let input;
                if (field.type === 'textarea') {
                    input = document.createElement('textarea');
                    input.rows = 3;
                } else if (field.type === 'select') {
                    input = document.createElement('select');
                    field.options?.forEach(option => {
                        const opt = document.createElement('option');
                        opt.value = option;
                        opt.textContent = option;
                        input.appendChild(opt);
                    });
                } else {
                    input = document.createElement('input');
                    input.type = 'text';
                }

                input.id = field.key;
                input.className = 'w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700';
                input.placeholder = field.placeholder || `Enter ${field.label.toLowerCase()}...`;

                inputContainer.appendChild(input);
                row.appendChild(label);
                row.appendChild(inputContainer);
                table.appendChild(row);
            });

            container.appendChild(table);
        }

        function createStructuredTable(config, container) {
            const wrapper = document.createElement('div');
            wrapper.className = 'space-y-6';

            // Existing items section
            const existingSection = document.createElement('div');
            existingSection.id = 'existingItemsSection';
            existingSection.className = 'space-y-4';

            const existingTitle = document.createElement('h4');
            existingTitle.className = 'text-lg font-medium text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-600 pb-2';
            existingTitle.textContent = 'Existing Items';

            const existingContainer = document.createElement('div');
            existingContainer.className = 'space-y-3';
            existingContainer.id = 'existingItems';

            existingSection.appendChild(existingTitle);
            existingSection.appendChild(existingContainer);

            // Add new item section
            const newSection = document.createElement('div');
            newSection.className = 'space-y-4';

            const newTitle = document.createElement('h4');
            newTitle.className = 'text-lg font-medium text-primary border-b border-primary pb-2';
            newTitle.textContent = 'Add New Item';

            const addButton = document.createElement('button');
            addButton.className = 'px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors';
            addButton.textContent = 'Add New Item';
            addButton.onclick = () => addStructuredItem(config, newItemsContainer, true);

            const newItemsContainer = document.createElement('div');
            newItemsContainer.className = 'space-y-4';
            newItemsContainer.id = 'newItems';

            newSection.appendChild(newTitle);
            newSection.appendChild(addButton);
            newSection.appendChild(newItemsContainer);

            wrapper.appendChild(existingSection);
            wrapper.appendChild(newSection);
            container.appendChild(wrapper);

            // Load and display existing data
            displayExistingItems(config);
        }

        function addStructuredItem(config, container, isNew = false) {
            const itemDiv = document.createElement('div');
            itemDiv.className = `border rounded-lg p-4 relative ${isNew ? 
                'border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20' : 
                'border-gray-200 dark:border-gray-600'}`;

            const deleteButton = document.createElement('button');
            deleteButton.className = 'absolute top-2 right-2 text-red-600 hover:text-red-800 text-xl';
            deleteButton.innerHTML = '×';
            deleteButton.onclick = () => {
                itemDiv.remove();
                if (!isNew) {
                    // If deleting an existing item, refresh the display
                    setTimeout(() => displayExistingItems(config), 100);
                }
            };

            const grid = document.createElement('div');
            grid.className = 'grid grid-cols-1 md:grid-cols-2 gap-4 pr-8';

            config.fields.forEach(field => {
                const fieldDiv = document.createElement('div');
                
                const label = document.createElement('label');
                label.className = 'block text-sm font-medium mb-1';
                label.textContent = field.label;

                let input;
                if (field.type === 'textarea') {
                    input = document.createElement('textarea');
                    input.rows = 2;
                } else if (field.type === 'select') {
                    input = document.createElement('select');
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Select...';
                    input.appendChild(defaultOption);
                    field.options?.forEach(option => {
                        const opt = document.createElement('option');
                        opt.value = option;
                        opt.textContent = option;
                        input.appendChild(opt);
                    });
                } else {
                    input = document.createElement('input');
                    input.type = 'text';
                }

                input.className = 'w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700';
                input.placeholder = field.placeholder || `Enter ${field.label.toLowerCase()}...`;
                input.setAttribute('data-field', field.key);

                fieldDiv.appendChild(label);
                fieldDiv.appendChild(input);
                grid.appendChild(fieldDiv);
            });

            if (isNew) {
                // Add a save button for new items
                const saveButton = document.createElement('button');
                saveButton.className = 'absolute top-2 right-8 text-green-600 hover:text-green-800 text-xl';
                saveButton.innerHTML = '✓';
                saveButton.title = 'Save this item';
                saveButton.onclick = () => {
                    saveCurrentSection();
                    // Remove the new item and refresh display
                    itemDiv.remove();
                    displayExistingItems(config);
                };
                itemDiv.appendChild(saveButton);
            }

            itemDiv.appendChild(deleteButton);
            itemDiv.appendChild(grid);
            container.appendChild(itemDiv);
        }

        function displayExistingItems(config) {
            // Get current section from either input or output selector
            const currentSection = document.getElementById('inputSelect').value || document.getElementById('outputSelect').value;
            const existingContainer = document.getElementById('existingItems');
            const existingSection = document.getElementById('existingItemsSection');
            
            if (!existingContainer || !currentSection) return;

            existingContainer.innerHTML = '';

            const data = chatbotData[currentSection];
            if (!data || !data.items || data.items.length === 0) {
                existingSection.style.display = 'none';
                return;
            }

            existingSection.style.display = 'block';

            data.items.forEach((item, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'border border-blue-200 dark:border-blue-600 rounded-lg p-4 relative bg-blue-50 dark:bg-blue-900/20';

                // Item number badge
                const badge = document.createElement('span');
                badge.className = 'absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded';
                badge.textContent = `#${index + 1}`;

                // Edit button
                const editButton = document.createElement('button');
                editButton.className = 'absolute top-2 right-8 text-blue-600 hover:text-blue-800 text-xl';
                editButton.innerHTML = '✏️';
                editButton.title = 'Edit this item';
                editButton.onclick = () => editExistingItem(config, index, item);

                // Delete button
                const deleteButton = document.createElement('button');
                deleteButton.className = 'absolute top-2 right-2 text-red-600 hover:text-red-800 text-xl';
                deleteButton.innerHTML = '×';
                deleteButton.onclick = () => {
                    if (confirm('Are you sure you want to delete this item?')) {
                        deleteExistingItem(index);
                    }
                };

                // Content display
                const contentDiv = document.createElement('div');
                contentDiv.className = 'grid grid-cols-1 md:grid-cols-2 gap-4 pr-16 pl-12';

                config.fields.forEach(field => {
                    if (item[field.key] && item[field.key].trim()) {
                        const fieldDiv = document.createElement('div');
                        
                        const label = document.createElement('div');
                        label.className = 'text-sm font-medium text-gray-600 dark:text-gray-400';
                        label.textContent = field.label;

                        const value = document.createElement('div');
                        value.className = 'text-gray-900 dark:text-gray-100 mt-1';
                        if (field.type === 'textarea') {
                            value.style.whiteSpace = 'pre-wrap';
                        }
                        value.textContent = item[field.key];

                        fieldDiv.appendChild(label);
                        fieldDiv.appendChild(value);
                        contentDiv.appendChild(fieldDiv);
                    }
                });

                itemDiv.appendChild(badge);
                itemDiv.appendChild(editButton);
                itemDiv.appendChild(deleteButton);
                itemDiv.appendChild(contentDiv);
                existingContainer.appendChild(itemDiv);
            });
        }

        function editExistingItem(config, index, item) {
            const newContainer = document.getElementById('newItems');
            
            // Clear any existing new items
            newContainer.innerHTML = '';
            
            // Add an editable version
            addStructuredItem(config, newContainer, true);
            const editableItem = newContainer.lastElementChild;
            
            // Populate with existing data
            const inputs = editableItem.querySelectorAll('[data-field]');
            inputs.forEach(input => {
                const field = input.getAttribute('data-field');
                if (item[field]) {
                    input.value = item[field];
                }
            });

            // Change the save button behavior
            const saveButton = editableItem.querySelector('[title="Save this item"]');
            if (saveButton) {
                saveButton.onclick = () => {
                    // Update the existing item
                    const currentSection = document.getElementById('sectionSelect').value;
                    const updatedItem = {};
                    inputs.forEach(input => {
                        const field = input.getAttribute('data-field');
                        updatedItem[field] = input.value;
                    });
                    
                    // Replace the item in the data
                    if (!chatbotData[currentSection]) chatbotData[currentSection] = { items: [] };
                    if (!chatbotData[currentSection].items) chatbotData[currentSection].items = [];
                    chatbotData[currentSection].items[index] = updatedItem;
                    
                    updateProgress();
                    editableItem.remove();
                    displayExistingItems(config);
                };
            }

            // Scroll to the edit form
            editableItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        function deleteExistingItem(index) {
            const currentSection = document.getElementById('inputSelect').value || document.getElementById('outputSelect').value;
            if (chatbotData[currentSection] && chatbotData[currentSection].items) {
                chatbotData[currentSection].items.splice(index, 1);
                updateProgress();
                
                const config = sectionConfigs[currentSection] || customSections[currentSection];
                displayExistingItems(config);
            }
        }

        function createListTable(config, container) {
            const wrapper = document.createElement('div');
            wrapper.className = 'space-y-4';

            const addButton = document.createElement('button');
            addButton.className = 'px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors';
            addButton.textContent = 'Add Item';
            addButton.onclick = () => addListItem(listContainer);

            const listContainer = document.createElement('div');
            listContainer.className = 'space-y-2';
            listContainer.id = 'listItems';

            wrapper.appendChild(addButton);
            wrapper.appendChild(listContainer);
            container.appendChild(wrapper);
        }

        function addListItem(container) {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'flex gap-2';

            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'flex-1 px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700';
            input.placeholder = 'Enter item...';

            const deleteButton = document.createElement('button');
            deleteButton.className = 'px-3 py-2 text-red-600 hover:text-red-800';
            deleteButton.innerHTML = '×';
            deleteButton.onclick = () => itemDiv.remove();

            itemDiv.appendChild(input);
            itemDiv.appendChild(deleteButton);
            container.appendChild(itemDiv);
        }

        function createTextTable(config, container) {
            const textarea = document.createElement('textarea');
            textarea.className = 'w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700';
            textarea.rows = 8;
            textarea.placeholder = 'Enter your content here...';
            textarea.id = 'textContent';
            container.appendChild(textarea);
        }

        function saveCurrentSection() {
            // Get current section from either input or output selector
            const currentSection = document.getElementById('inputSelect').value || document.getElementById('outputSelect').value;
            if (!currentSection || currentSection === 'wildcard' || currentSection === 'customOutput') return;

            const config = sectionConfigs[currentSection] || customSections[currentSection];
            if (!config) return;

            let data = chatbotData[currentSection] || {};

            if (config.type === 'keyValue') {
                config.fields.forEach(field => {
                    const input = document.getElementById(field.key);
                    if (input) {
                        data[field.key] = input.value;
                    }
                });
            } else if (config.type === 'structured') {
                // Preserve existing items and add new ones
                if (!data.items) data.items = [];
                
                // Add new items from the new items container
                const newItemContainers = document.querySelectorAll('#newItems > div');
                newItemContainers.forEach(container => {
                    const item = {};
                    const inputs = container.querySelectorAll('[data-field]');
                    inputs.forEach(input => {
                        const field = input.getAttribute('data-field');
                        item[field] = input.value;
                    });
                    if (Object.values(item).some(val => val && val.trim())) {
                        data.items.push(item);
                    }
                });
            } else if (config.type === 'list') {
                const items = [];
                const inputs = document.querySelectorAll('#listItems input');
                inputs.forEach(input => {
                    if (input.value.trim()) {
                        items.push(input.value.trim());
                    }
                });
                data.items = items;
            } else if (config.type === 'text') {
                const textarea = document.getElementById('textContent');
                if (textarea) {
                    data.content = textarea.value;
                }
            }

            chatbotData[currentSection] = data;
            updateProgress();

            // Clear new items after saving (for structured type)
            if (config.type === 'structured') {
                const newContainer = document.getElementById('newItems');
                if (newContainer) {
                    newContainer.innerHTML = '';
                }
            }

            // Visual feedback
            const saveBtn = document.getElementById('saveSection');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saved!';
            saveBtn.className = saveBtn.className.replace('bg-primary', 'bg-green-600');
            setTimeout(() => {
                saveBtn.textContent = originalText;
                saveBtn.className = saveBtn.className.replace('bg-green-600', 'bg-primary');
            }, 1000);
        }

        function loadSectionData(sectionId) {
            const data = chatbotData[sectionId];
            const config = sectionConfigs[sectionId] || customSections[sectionId];
            if (!config) return;

            if (config.type === 'keyValue') {
                config.fields.forEach(field => {
                    const input = document.getElementById(field.key);
                    if (input && data && data[field.key]) {
                        input.value = data[field.key];
                    }
                });
            } else if (config.type === 'text') {
                const textarea = document.getElementById('textContent');
                if (textarea && data && data.content) {
                    textarea.value = data.content;
                }
            } else if (config.type === 'structured') {
                // For structured type, just display existing items
                displayExistingItems(config);
            } else if (config.type === 'list' && data && data.items) {
                const container = document.getElementById('listItems');
                container.innerHTML = '';
                data.items.forEach(item => {
                    addListItem(container);
                    const lastInput = container.lastElementChild.querySelector('input');
                    lastInput.value = item;
                });
            }
        }

        function generateMarkdown() {
            let markdown = `# Chatbot Customization Specification

## Overview
This document outlines the configuration and behavior specifications for a custom chatbot designed to assist with educational tasks and student interactions.

`;

            // Generate content for each configured section
            Object.keys(chatbotData).forEach(sectionId => {
                const data = chatbotData[sectionId];
                const config = sectionConfigs[sectionId] || customSections[sectionId];
                if (!config || !data || Object.keys(data).length === 0) return;

                markdown += `## ${config.title}\n\n`;

                if (config.type === 'keyValue') {
                    config.fields.forEach(field => {
                        if (data[field.key] && data[field.key].trim()) {
                            if (field.key === 'inputHandling') {
                                markdown += `### ${field.label}\n${data[field.key]}\n\n`;
                            } else {
                                markdown += `### ${field.label}\n${data[field.key]}\n\n`;
                            }
                        }
                    });
                } else if (config.type === 'structured' && data.items) {
                    data.items.forEach((item, index) => {
                        markdown += `### Item ${index + 1}\n`;
                        config.fields.forEach(field => {
                            if (item[field.key] && item[field.key].trim()) {
                                if (field.key === 'inputHandling' || field.key === 'handlingInstructions' || field.key === 'updateInstructions') {
                                    markdown += `**${field.label}:**\n${item[field.key]}\n\n`;
                                } else {
                                    markdown += `**${field.label}:** ${item[field.key]}\n\n`;
                                }
                            }
                        });
                        markdown += '\n';
                    });
                } else if (config.type === 'list' && data.items) {
                    data.items.forEach(item => {
                        markdown += `- ${item}\n`;
                    });
                    markdown += '\n';
                } else if (config.type === 'text' && data.content) {
                    markdown += `${data.content}\n\n`;
                }
            });

            markdown += `---

*Generated on: ${new Date().toLocaleDateString()}*
*Specification Version: 1.0*`;

            return markdown;
        }

        function downloadMarkdown(content) {
            const blob = new Blob([content], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'chatbot-customization.md';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function handleSectionChange(selectElement, sectionType) {
            const value = selectElement.value;
            const contentArea = document.getElementById('contentArea');
            const customCreator = document.getElementById('customSectionCreator');
            const addCustomInputBtn = document.getElementById('addCustomInputBtn');
            const addCustomOutputBtn = document.getElementById('addCustomOutputBtn');

            // Clear other selector when one is chosen
            if (value) {
                if (sectionType === 'input') {
                    document.getElementById('outputSelect').value = '';
                } else if (sectionType === 'output') {
                    document.getElementById('inputSelect').value = '';
                }
            }

            if (value === 'wildcard' || value === 'customOutput') {
                contentArea.classList.add('hidden');
                customCreator.classList.remove('hidden');
                if (sectionType === 'input') {
                    addCustomInputBtn.classList.remove('hidden');
                    addCustomOutputBtn.classList.add('hidden');
                } else {
                    addCustomOutputBtn.classList.remove('hidden');
                    addCustomInputBtn.classList.add('hidden');
                }
            } else if (value) {
                customCreator.classList.add('hidden');
                addCustomInputBtn.classList.add('hidden');
                addCustomOutputBtn.classList.add('hidden');
                contentArea.classList.remove('hidden');
                
                const config = sectionConfigs[value] || customSections[value];
                document.getElementById('sectionTitle').textContent = config.title;
                
                // Add visual indicator for section type
                const titleElement = document.getElementById('sectionTitle');
                if (sectionType === 'input') {
                    titleElement.className = 'text-xl font-semibold text-blue-600 dark:text-blue-400';
                    titleElement.textContent = '📥 ' + config.title;
                } else if (sectionType === 'output') {
                    titleElement.className = 'text-xl font-semibold text-green-600 dark:text-green-400';
                    titleElement.textContent = '📤 ' + config.title;
                }
                
                createTable(config);
                
                // Load existing data after creating the table
                setTimeout(() => loadSectionData(value), 100);
            } else {
                contentArea.classList.add('hidden');
                customCreator.classList.add('hidden');
                addCustomInputBtn.classList.add('hidden');
                addCustomOutputBtn.classList.add('hidden');
            }
        }

        // Event listeners
        document.getElementById('inputSelect').addEventListener('change', function() {
            handleSectionChange(this, 'input');
        });

        document.getElementById('outputSelect').addEventListener('change', function() {
            handleSectionChange(this, 'output');
        });

        function addCustomSection(sectionType) {
            const name = document.getElementById('customSectionName').value.trim();
            const type = document.getElementById('customSectionType').value;
            
            if (!name) {
                alert('Please enter a section name');
                return;
            }

            const sectionId = name.toLowerCase().replace(/[^a-z0-9]/g, '');
            
            // Create custom section config
            let fields = [];
            if (sectionType === 'input') {
                if (type === 'keyValue') {
                    fields = [
                        { key: 'content1', label: 'Content 1', type: 'textarea' },
                        { key: 'content2', label: 'Content 2', type: 'textarea' },
                        { key: 'inputHandling', label: 'How Chatbot Should Handle This Input', type: 'textarea', placeholder: 'Explain how the chatbot should process this information...' }
                    ];
                } else if (type === 'structured') {
                    fields = [
                        { key: 'name', label: 'Name', type: 'text' },
                        { key: 'description', label: 'Description', type: 'textarea' },
                        { key: 'inputHandling', label: 'How Chatbot Should Use This Input', type: 'textarea', placeholder: 'Explain how the chatbot should utilize this information...' }
                    ];
                }
            } else {
                // Output type
                fields = [
                    { key: 'outputFormat', label: 'Output Format', type: 'textarea', placeholder: 'Define the structure and format of this output...' },
                    { key: 'contentGuidelines', label: 'Content Guidelines', type: 'textarea', placeholder: 'Specify what content should be included...' },
                    { key: 'updateTriggers', label: 'Update Triggers', type: 'textarea', placeholder: 'When should this output be updated...' },
                    { key: 'updateInstructions', label: 'How Chatbot Should Update This Output', type: 'textarea', placeholder: 'Explain how the chatbot should generate and update this output...' }
                ];
            }

            customSections[sectionId] = {
                title: name,
                type: type,
                fields: fields
            };

            // Add to appropriate dropdown
            const option = document.createElement('option');
            option.value = sectionId;
            option.textContent = name;
            
            if (sectionType === 'input') {
                const select = document.getElementById('inputSelect');
                select.insertBefore(option, select.lastElementChild);
                document.getElementById('outputSelect').value = '';
                select.value = sectionId;
                handleSectionChange(select, 'input');
            } else {
                const select = document.getElementById('outputSelect');
                select.insertBefore(option, select.lastElementChild);
                document.getElementById('inputSelect').value = '';
                select.value = sectionId;
                handleSectionChange(select, 'output');
            }

            // Clear inputs
            document.getElementById('customSectionName').value = '';
            document.getElementById('customSectionType').value = 'keyValue';

            updateProgress();
        }

        document.getElementById('addCustomInputBtn').addEventListener('click', function() {
            addCustomSection('input');
        });

        document.getElementById('addCustomOutputBtn').addEventListener('click', function() {
            addCustomSection('output');
        });

        document.getElementById('saveSection').addEventListener('click', saveCurrentSection);

        document.getElementById('previewBtn').addEventListener('click', function() {
            const markdown = generateMarkdown();
            document.getElementById('previewContent').textContent = markdown;
            document.getElementById('previewModal').classList.remove('hidden');
        });

        document.getElementById('generateBtn').addEventListener('click', function() {
            const markdown = generateMarkdown();
            downloadMarkdown(markdown);
        });

        document.getElementById('clearAllBtn').addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
                chatbotData = {};
                customSections = {};
                updateProgress();
                document.getElementById('sectionSelect').value = '';
                document.getElementById('contentArea').classList.add('hidden');
                

                
                // Reset input/output selectors
                document.getElementById('inputSelect').value = '';
                document.getElementById('outputSelect').value = '';
                
                // Remove custom options from both dropdowns
                const inputSelect = document.getElementById('inputSelect');
                const outputSelect = document.getElementById('outputSelect');
                
                [inputSelect, outputSelect].forEach(select => {
                    const options = Array.from(select.options);
                    options.forEach(option => {
                        if (!Object.keys(sectionConfigs).includes(option.value) && 
                            option.value !== '' && option.value !== 'wildcard' && option.value !== 'customOutput') {
                            option.remove();
                        }
                    });
                });
            }
        });

        document.getElementById('downloadFromPreviewBtn').addEventListener('click', function() {
            const markdown = generateMarkdown();
            downloadMarkdown(markdown);
            document.getElementById('previewModal').classList.add('hidden');
        });

        document.getElementById('closePreviewBtn').addEventListener('click', function() {
            document.getElementById('previewModal').classList.add('hidden');
        });

        // Close modal when clicking outside
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });

        // Initialize progress
        updateProgress();
    </script>


</body></html>