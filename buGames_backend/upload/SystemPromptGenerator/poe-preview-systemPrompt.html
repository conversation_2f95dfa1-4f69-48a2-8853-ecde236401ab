<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Customization Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        background: {
                            light: '#FFFFFF',
                            dark: '#181818'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @media (prefers-color-scheme: dark) {
            html { color-scheme: dark; }
        }
    </style>
</head>
<body class="bg-background-light dark:bg-background-dark text-gray-900 dark:text-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <header class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">Chatbot Customization Generator</h1>
            <p class="text-gray-600 dark:text-gray-400">Create detailed markdown specifications for your custom chatbot</p>
        </header>

        <form id="chatbotForm" class="space-y-8">
            <!-- Task Background and Context -->
            <section class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4 text-primary">Task Background and Context</h2>
                <div class="space-y-4">
                    <div>
                        <label for="taskBackground" class="block text-sm font-medium mb-2">Background Description</label>
                        <textarea id="taskBackground" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Describe the context and background for this chatbot..."></textarea>
                    </div>
                    <div>
                        <label for="taskContext" class="block text-sm font-medium mb-2">Additional Context</label>
                        <textarea id="taskContext" rows="3" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Any additional context or constraints..."></textarea>
                    </div>
                </div>
            </section>

            <!-- Techniques and Examples -->
            <section class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4 text-primary">Techniques and Examples</h2>
                <div class="space-y-4">
                    <div>
                        <label for="techniques" class="block text-sm font-medium mb-2">Techniques for Students to Apply</label>
                        <textarea id="techniques" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="List specific techniques students should learn and apply..."></textarea>
                    </div>
                    <div>
                        <label for="examples" class="block text-sm font-medium mb-2">Examples of Techniques Applied</label>
                        <textarea id="examples" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Provide concrete examples of how these techniques are used..."></textarea>
                    </div>
                </div>
            </section>

            <!-- Objectives and Tasks -->
            <section class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4 text-primary">Objectives and Tasks</h2>
                <div class="space-y-4">
                    <div>
                        <label for="objectives" class="block text-sm font-medium mb-2">Objective Tasks</label>
                        <textarea id="objectives" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Define the specific tasks and objectives for students..."></textarea>
                    </div>
                    <div>
                        <label for="otherItems" class="block text-sm font-medium mb-2">Other Items to Fill In</label>
                        <textarea id="otherItems" rows="3" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Any additional requirements or considerations..."></textarea>
                    </div>
                </div>
            </section>

            <!-- Chatbot Configuration -->
            <section class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4 text-primary">Chatbot Configuration</h2>
                <div class="space-y-4">
                    <div>
                        <label for="chatbotRole" class="block text-sm font-medium mb-2">Chatbot Role</label>
                        <input type="text" id="chatbotRole" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="e.g., Teaching Assistant, Writing Coach, Study Guide...">
                    </div>
                    <div>
                        <label for="menuItems" class="block text-sm font-medium mb-2">Menu Items and Chatbot Responses</label>
                        <textarea id="menuItems" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Define menu options and corresponding chatbot responses..."></textarea>
                    </div>
                    <div>
                        <label for="workflow" class="block text-sm font-medium mb-2">Workflow</label>
                        <textarea id="workflow" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Describe the step-by-step workflow for interactions..."></textarea>
                    </div>
                </div>
            </section>

            <!-- Documentation -->
            <section class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4 text-primary">Documentation</h2>
                <div class="space-y-4">
                    <div>
                        <label for="contextDoc" class="block text-sm font-medium mb-2">Context Document</label>
                        <textarea id="contextDoc" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Initial context content and update instructions..."></textarea>
                    </div>
                    <div>
                        <label for="outputDoc" class="block text-sm font-medium mb-2">Output Document Structure</label>
                        <textarea id="outputDoc" rows="4" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700" placeholder="Define output structure and how chatbot should update it..."></textarea>
                    </div>
                </div>
            </section>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button type="button" id="previewBtn" class="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors">
                    Preview Markdown
                </button>
                <button type="button" id="generateBtn" class="px-6 py-3 bg-primary hover:bg-purple-700 text-white font-medium rounded-lg transition-colors">
                    Generate &amp; Download
                </button>
            </div>
        </form>

        <!-- Preview Modal -->
        <div id="previewModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[80vh] overflow-hidden">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-xl font-semibold">Markdown Preview</h3>
                </div>
                <div class="p-6 overflow-y-auto max-h-[60vh]">
                    <pre id="previewContent" class="text-sm bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-x-auto whitespace-pre-wrap"></pre>
                </div>
                <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
                    <button id="closePreviewBtn" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                        Close
                    </button>
                    <button id="downloadFromPreviewBtn" class="px-4 py-2 bg-primary hover:bg-purple-700 text-white rounded">
                        Download
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode handling
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        function generateMarkdown() {
            const formData = {
                taskBackground: document.getElementById('taskBackground').value,
                taskContext: document.getElementById('taskContext').value,
                techniques: document.getElementById('techniques').value,
                examples: document.getElementById('examples').value,
                objectives: document.getElementById('objectives').value,
                otherItems: document.getElementById('otherItems').value,
                chatbotRole: document.getElementById('chatbotRole').value,
                menuItems: document.getElementById('menuItems').value,
                workflow: document.getElementById('workflow').value,
                contextDoc: document.getElementById('contextDoc').value,
                outputDoc: document.getElementById('outputDoc').value
            };

            const markdown = `# Chatbot Customization Specification

## Overview
This document outlines the configuration and behavior specifications for a custom chatbot designed to assist with educational tasks and student interactions.

## Task Background and Context

### Background
${formData.taskBackground || 'No background information provided.'}

### Additional Context
${formData.taskContext || 'No additional context provided.'}

## Learning Framework

### Techniques for Students to Apply
${formData.techniques || 'No techniques specified.'}

### Examples of Technique Application
${formData.examples || 'No examples provided.'}

## Objectives and Tasks

### Primary Objectives
${formData.objectives || 'No objectives specified.'}

### Additional Requirements
${formData.otherItems || 'No additional requirements specified.'}

## Chatbot Configuration

### Role Definition
**Primary Role:** ${formData.chatbotRole || 'Not specified'}

The chatbot should embody this role consistently throughout all interactions, maintaining appropriate tone, expertise level, and response style.

### Menu System and Responses
${formData.menuItems || 'No menu items or responses defined.'}

### Interaction Workflow
${formData.workflow || 'No workflow defined.'}

## Documentation Framework

### Context Document Management
${formData.contextDoc || 'No context document specifications provided.'}

### Output Document Structure
${formData.outputDoc || 'No output document structure defined.'}

## Implementation Guidelines

### Response Formatting
- Maintain consistent markdown formatting
- Use appropriate headers and structure
- Include relevant examples when helpful
- Provide clear, actionable guidance

### Interaction Principles
- Be encouraging and supportive
- Provide specific, actionable feedback
- Adapt responses to student skill level
- Maintain educational focus

### Quality Assurance
- Verify all responses align with learning objectives
- Ensure consistency with defined role and workflow
- Maintain appropriate academic standards
- Regular updates based on student feedback

---

*Generated on: ${new Date().toLocaleDateString()}*
*Specification Version: 1.0*`;

            return markdown;
        }

        function downloadMarkdown(content) {
            const blob = new Blob([content], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'chatbot-customization.md';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Event listeners
        document.getElementById('previewBtn').addEventListener('click', function() {
            const markdown = generateMarkdown();
            document.getElementById('previewContent').textContent = markdown;
            document.getElementById('previewModal').classList.remove('hidden');
        });

        document.getElementById('generateBtn').addEventListener('click', function() {
            const markdown = generateMarkdown();
            downloadMarkdown(markdown);
        });

        document.getElementById('downloadFromPreviewBtn').addEventListener('click', function() {
            const markdown = generateMarkdown();
            downloadMarkdown(markdown);
            document.getElementById('previewModal').classList.add('hidden');
        });

        document.getElementById('closePreviewBtn').addEventListener('click', function() {
            document.getElementById('previewModal').classList.remove('hidden');
        });

        // Close modal when clicking outside
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });

        // Form validation feedback
        document.getElementById('chatbotForm').addEventListener('input', function(e) {
            if (e.target.tagName === 'TEXTAREA' || e.target.tagName === 'INPUT') {
                if (e.target.value.trim()) {
                    e.target.classList.remove('border-red-300');
                    e.target.classList.add('border-gray-300', 'dark:border-gray-600');
                }
            }
        });
    </script>


</body></html>