<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ByteWise - Customizable AI Chatbots for Education</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer=""></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0EA5E9',
                        'primary-dark': '#0284C7',
                        'primary-light': '#38BDF8',
                        'tech-dark': '#0283dd',
                        'tech-electric': '#0EA5E9',
                        'tech-cyan': '#06e9c1',
                        'tech-blue': '#0F172A',
                        'tech-accent': '#00D4FF',
                        'bg-light': '#FFFFFF',
                        'bg-dark': '#0F172A',
                        'text-light': '#1F2937',
                        'text-dark': '#F9FAFB'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700&family=Work+Sans:wght@100;200;300;400;500;600;700&family=Montserrat:wght@100;200;300;400;500;600;700&family=Raleway:wght@100;200;300;400;500;600;700&family=Lato:wght@100;300;400;700&family=Source+Sans+Pro:wght@200;300;400;600;700&family=Nunito+Sans:wght@200;300;400;600;700&family=Playfair+Display:wght@300;400;500;600;700&family=Cormorant+Garamond:wght@300;400;500;600;700&family=Crimson+Text:wght@300;400;600&family=Libre+Baskerville:wght@300;400;700&family=Lora:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&family=Georgia&family=Cardo:wght@400;700&display=swap');
        body { font-family: 'Work Sans', sans-serif; }
        
        .font-poppins { font-family: 'Poppins', sans-serif; font-weight: 200; letter-spacing: -0.02em; }
        .font-montserrat { font-family: 'Montserrat', sans-serif; font-weight: 200; letter-spacing: -0.01em; }
        .font-raleway { font-family: 'Raleway', sans-serif; font-weight: 200; letter-spacing: 0.01em; }
        .font-lato { font-family: 'Lato', sans-serif; font-weight: 300; letter-spacing: -0.01em; }
        .font-source { font-family: 'Source Sans Pro', sans-serif; font-weight: 200; letter-spacing: 0.01em; }
        .font-nunito { font-family: 'Nunito Sans', sans-serif; font-weight: 200; letter-spacing: -0.01em; }
        
        .elegant-font { font-family: 'Playfair Display', serif; font-weight: 400; letter-spacing: -0.01em; }
        .gradient-bg { background: linear-gradient(135deg, #0EA5E9 0%, #06B6D4 50%, #00D4FF 100%); }
        .tech-gradient-bg { background: linear-gradient(135deg, #0F172A 0%, #1E40AF 50%, #0EA5E9 100%); }
        .smooth-tech-gradient {
            background: linear-gradient(90deg, #1E40AF 0%, #3B82F6 20%, #6366F1 40%, #8B5CF6 60%, #A855F7 80%, #EC4899 100%);
            background-clip: text; -webkit-background-clip: text; -webkit-text-fill-color: transparent;
        }
        
        .gradient-border { position: relative; background: white; border: 2px solid transparent; background-clip: padding-box; }
        .gradient-border::before {
            content: ''; position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: -1; margin: -2px;
            border-radius: inherit; background: linear-gradient(90deg, #1E40AF 0%, #3B82F6 20%, #6366F1 40%, #8B5CF6 60%, #A855F7 80%, #EC4899 100%);
        }
        
        .dark .gradient-border { background: #1F2937; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 20px 40px rgba(14, 165, 233, 0.15); }
        .animate-float { animation: float 6s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-20px); } }
        .dark .bg-light { background-color: #181818; }
        .dark .text-light { color: #F9FAFB; }
        
        .editable-section { position: relative; }
        .editable-section:hover .edit-controls { opacity: 1; }
        .edit-controls { 
            opacity: 0; 
            transition: opacity 0.3s ease;
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        .editing { outline: 2px dashed #0EA5E9; outline-offset: 4px; }
        [contenteditable="true"] { min-height: 20px; }
        [contenteditable="true"]:focus { outline: 2px solid #0EA5E9; outline-offset: 2px; background: rgba(14, 165, 233, 0.05); }
        
        .download-btn {
            position: fixed;
            top: 80px;
            left: 110px;
            z-index: 1000;
            background: #0EA5E9;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            background: #0284C7;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
        }
    </style>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <div x-data="{ 
        darkMode: false, 
        mobileMenu: false,
        editMode: false,
        savedSections: {},
        init() {
            this.darkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.$watch('darkMode', value => {
                if (value) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
            });
            // Enable double-click editing
            this.enableDoubleClickEdit();
        },
        enableDoubleClickEdit() {
            // Add double-click listeners to all editable elements
            document.addEventListener('dblclick', (e) => {
                const target = e.target.closest('[data-editable]');
                if (target) {
                    e.preventDefault(); // Prevent default behavior
                    
                    // Don't edit if already editing
                    if (target.querySelector('input') || target.contentEditable === 'true') return;
                    
                    // Store the original content and styles
                    const originalTextContent = target.textContent.trim();
                    const targetRect = target.getBoundingClientRect();
                    const computedStyle = window.getComputedStyle(target);
                    
                    // Store original styles for gradient text
                    const originalBackground = target.style.background;
                    const originalWebkitBackground = target.style.webkitBackgroundClip;
                    const originalBackgroundClip = target.style.backgroundClip;
                    const originalWebkitTextFill = target.style.webkitTextFillColor;
                    const originalColor = target.style.color;
                    
                    // Store original href if it's a link
                    const isLink = target.tagName.toLowerCase() === 'a';
                    const originalHref = isLink ? target.getAttribute('href') : null;
                    
                    // Check if element has gradient text class
                    const hasGradient = target.classList.contains('smooth-tech-gradient');
                    
                    // Create an input element for editing
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.value = originalTextContent;
                    input.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        border: 2px solid #0EA5E9;
                        border-radius: 4px;
                        background: rgba(14, 165, 233, 0.05);
                        color: ${hasGradient ? '#6366F1' : computedStyle.color};
                        font-family: ${computedStyle.fontFamily};
                        font-size: ${computedStyle.fontSize};
                        font-weight: ${computedStyle.fontWeight};
                        text-align: ${computedStyle.textAlign};
                        padding: 4px 8px;
                        margin: 0;
                        outline: none;
                        z-index: 1000;
                        box-sizing: border-box;
                    `;
                    
                    // Position the target relatively and hide text temporarily
                    const originalPosition = target.style.position;
                    const originalVisibility = target.style.visibility;
                    target.style.position = 'relative';
                    target.style.color = 'transparent';
                    
                    // Temporarily remove gradient effects
                    if (hasGradient) {
                        target.style.background = 'none';
                        target.style.webkitBackgroundClip = 'initial';
                        target.style.backgroundClip = 'initial';
                        target.style.webkitTextFillColor = 'initial';
                    }
                    
                    // For links, temporarily remove href
                    if (isLink) {
                        target.removeAttribute('href');
                    }
                    
                    // Insert input and focus
                    target.appendChild(input);
                    input.focus();
                    input.select();
                    
                    // Function to exit edit mode
                    const exitEdit = (save = true) => {
                        if (save) {
                            target.textContent = input.value;
                        }
                        
                        // Remove input
                        if (input.parentNode) {
                            input.parentNode.removeChild(input);
                        }
                        
                        // Restore original styles and position
                        target.style.position = originalPosition;
                        target.style.visibility = originalVisibility;
                        target.style.color = originalColor;
                        
                        // Restore gradient styles if it was a gradient element
                        if (hasGradient) {
                            target.style.background = originalBackground;
                            target.style.webkitBackgroundClip = originalWebkitBackground;
                            target.style.backgroundClip = originalBackgroundClip;
                            target.style.webkitTextFillColor = originalWebkitTextFill;
                        }
                        
                        // Restore href for links
                        if (isLink &amp;&amp; originalHref) {
                            target.href = originalHref;
                        }
                        
                        // Remove event listeners
                        input.removeEventListener('blur', handleBlur);
                        input.removeEventListener('keydown', handleKeydown);
                    };
                    
                    // Handle blur event
                    const handleBlur = () => {
                        exitEdit(true);
                    };
                    
                    // Handle keydown events
                    const handleKeydown = (e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            exitEdit(true);
                        } else if (e.key === 'Escape') {
                            e.preventDefault();
                            exitEdit(false);
                        }
                        // All other keys including space work normally
                    };
                    
                    // Add event listeners
                    input.addEventListener('blur', handleBlur);
                    input.addEventListener('keydown', handleKeydown);
                }
            });
        },
        saveSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                this.savedSections[sectionId] = section.innerHTML;
                // Show success feedback
                const saveBtn = section.querySelector('.save-btn');
                if (saveBtn) {
                    const originalText = saveBtn.innerHTML;
                    saveBtn.innerHTML = '<i class=&quot;fas fa-check&quot;></i> Saved!';
                    saveBtn.style.background = '#10B981';
                    setTimeout(() => {
                        saveBtn.innerHTML = originalText;
                        saveBtn.style.background = '#0EA5E9';
                    }, 2000);
                }
            }
        },
        downloadHTML() {
            // Create a copy of the document
            const docClone = document.documentElement.cloneNode(true);
            
            // Remove edit controls and reset contenteditable
            const editControls = docClone.querySelectorAll('.edit-controls, .download-btn, .edit-mode-toggle');
            editControls.forEach(el => el.remove());
            
            const editableSections = docClone.querySelectorAll('.editable-section');
            editableSections.forEach(section => {
                section.classList.remove('editing');
                const editableElements = section.querySelectorAll('[data-editable]');
                editableElements.forEach(el => {
                    el.removeAttribute('contenteditable');
                });
            });
            
            // Create download
            const htmlContent = '<!DOCTYPE html>\n' + docClone.outerHTML;
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'bytewise-edited.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }" class="min-h-screen">

        <!-- Fixed Controls -->
        <button @click="downloadHTML()" class="download-btn">
            <i class="fas fa-download"></i> Download HTML
        </button>

        <!-- Header -->
        <header id="header-section" class="editable-section sticky top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
            <div class="edit-controls">
                <button @click="saveSection('header-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <nav class="container mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <!-- Logo -->
                    <div class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                            <i class="fas fa-robot text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold smooth-tech-gradient" data-editable="">ByteWise</span>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="#home" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Home</a>
                        <a href="#students" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Students</a>
                        <a href="#teachers" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Teachers</a>
                        <a href="#community" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Community</a>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="hidden md:flex items-center space-x-4">
                        <button class="px-4 py-2 text-primary gradient-border rounded-lg hover:bg-primary hover:text-white transition-all" data-editable="">
                            Login
                        </button>
                        <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-all" data-editable="">
                            Register
                        </button>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button @click="mobileMenu = !mobileMenu" class="md:hidden p-2">
                        <i class="fas fa-bars text-gray-600 dark:text-gray-300"></i>
                    </button>
                </div>

                <!-- Mobile Menu -->
                <div x-show="mobileMenu" x-transition="" class="md:hidden mt-4 pb-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                    <div class="flex flex-col space-y-4">
                        <a href="#home" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Home</a>
                        <a href="#students" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Students</a>
                        <a href="#teachers" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Teachers</a>
                        <a href="#community" class="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors" data-editable="" data-editable-link="">Community</a>
                        <div class="flex flex-col space-y-2 pt-4">
                            <button class="px-4 py-2 text-primary border border-primary rounded-lg hover:bg-primary hover:text-white transition-all" data-editable="">
                                Login
                            </button>
                            <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-all" data-editable="">
                                Register
                            </button>
                        </div>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Hero Section -->
        <section id="hero-section" class="editable-section py-20 lg:py-32 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-slate-900 dark:to-blue-900">
            <div class="edit-controls">
                <button @click="saveSection('hero-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="space-y-8">
                        <div class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium" data-editable="">
                            AI for Educators
                        </div>
                        <h1 class="text-5xl lg:text-6xl leading-tight elegant-font">
                            <span class="smooth-tech-gradient" data-editable="">
                                Customizable AI Chatbots for Education
                            </span>
                        </h1>
                        <p class="text-xl text-gray-600 dark:text-gray-300 leading-relaxed" data-editable="">
                            Empower students and educators with intelligent, personalized learning experiences powered by cutting-edge AI technology. Our adaptive solutions analyze performance data to adjust lesson plans, ensuring each learner is challenged and supported at the optimal level for maximum engagement and retention.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button class="px-8 py-4 bg-primary text-white rounded-xl hover:bg-primary-dark transition-all transform hover:scale-105 font-medium text-lg" data-editable="">
                                Explore ByteWise Now
                                <i class="fas fa-arrow-right ml-2"></i>
                            </button>
                            <button class="px-8 py-4 text-gray-700 dark:text-gray-300 hover:text-primary transition-all font-medium text-lg" data-editable="">
                                Learn more
                            </button>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 animate-float">
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-robot text-white text-sm"></i>
                                    </div>
                                    <span class="font-medium" data-editable="">AI Assistant</span>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <p class="text-sm" data-editable="">Hi! I'm your personalized learning assistant. How can I help you with your studies today?</p>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm" data-editable="">Explain Concepts</button>
                                    <button class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm" data-editable="">Practice Quiz</button>
                                    <button class="px-3 py-2 bg-primary/10 text-primary rounded-lg text-sm" data-editable="">Study Plan</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Overview -->
        <section id="features-section" class="editable-section py-20 bg-white dark:bg-gray-900">
            <div class="edit-controls">
                <button @click="saveSection('features-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-4 gap-8">
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-graduation-cap text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Personalized Learning</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">AI adapts to each student's learning style and pace</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-chalkboard-teacher text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Teacher Tools</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Comprehensive dashboard for course management</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-comments text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Interactive Chat</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Natural conversations with AI tutors</p>
                    </div>
                    
                    <div class="text-center space-y-4 card-hover p-6 rounded-xl">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                            <i class="fas fa-chart-line text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-lg elegant-font" data-editable="">Analytics</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">Track progress and identify learning gaps</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- For Students Section -->
        <section id="students-section" class="editable-section py-20 bg-gray-50 dark:bg-gray-800">
            <div class="edit-controls">
                <button @click="saveSection('students-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">AI for Student Success</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Get personalized help, instant feedback, and adaptive learning experiences that grow with you.
                    </p>
                </div>

                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="space-y-8">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">1</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2 elegant-font" data-editable="">Ask Questions Anytime</h3>
                                <p class="text-gray-600 dark:text-gray-300" data-editable="">Get instant answers to your questions, whether it's homework help or concept clarification.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">2</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2 elegant-font" data-editable="">Personalized Study Plans</h3>
                                <p class="text-gray-600 dark:text-gray-300" data-editable="">AI creates custom study schedules based on your learning goals and availability.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">3</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2 elegant-font" data-editable="">Track Your Progress</h3>
                                <p class="text-gray-600 dark:text-gray-300" data-editable="">Monitor your learning journey with detailed analytics and achievement badges.</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8">
                        <div class="space-y-6">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold" data-editable="">Study Session</h3>
                                <span class="text-sm text-gray-500" data-editable="">2:30 PM</span>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                                    <p class="text-sm font-medium text-blue-800 dark:text-blue-200" data-editable="">Student</p>
                                    <p class="text-sm mt-1" data-editable="">Can you explain photosynthesis in simple terms?</p>
                                </div>
                                
                                <div class="bg-primary/10 rounded-lg p-4">
                                    <p class="text-sm font-medium text-primary" data-editable="">AI Tutor</p>
                                    <p class="text-sm mt-1" data-editable="">Photosynthesis is like a plant's way of making food using sunlight, water, and carbon dioxide. Think of it as nature's solar panel! Would you like me to break it down into steps?</p>
                                </div>
                                
                                <div class="flex space-x-2">
                                    <button class="px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm" data-editable="">Yes, show steps</button>
                                    <button class="px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm" data-editable="">Give example</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- For Teachers Section -->
        <section id="teachers-section" class="editable-section py-20 bg-white dark:bg-gray-900">
            <div class="edit-controls">
                <button @click="saveSection('teachers-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">Empower Teachers with AI</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Create custom chatbots, automate administrative tasks, and gain insights into student performance.
                    </p>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-robot text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Custom Chatbots</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Design AI assistants tailored to your curriculum and teaching style.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Subject-specific knowledge</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Custom personality</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Integration with materials</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-chart-bar text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Student Analytics</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Get detailed insights into student engagement and learning patterns.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Performance tracking</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Engagement metrics</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Learning gap identification</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 card-hover">
                        <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mb-6">
                            <i class="fas fa-cogs text-primary text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-xl mb-4 elegant-font" data-editable="">Automation Tools</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6" data-editable="">Automate repetitive tasks and focus on what matters most - teaching.</p>
                        <ul class="space-y-2 text-sm">
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Auto-grading</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">FAQ responses</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500 text-xs"></i>
                                <span data-editable="">Progress reports</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Community Section -->
        <section id="community-section" class="editable-section py-20 bg-primary/5 dark:bg-gray-800">
            <div class="edit-controls">
                <button @click="saveSection('community-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl lg:text-5xl mb-6 elegant-font">
                        <span class="smooth-tech-gradient" data-editable="">Join the Community</span>
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto" data-editable="">
                        Connect with educators and students from Hong Kong and beyond who are transforming education with AI.
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-8 mb-16">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary mb-2" data-editable="">10,000+</div>
                        <p class="text-gray-600 dark:text-gray-300" data-editable="">Active Users</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary mb-2" data-editable="">500+</div>
                        <p class="text-gray-600 dark:text-gray-300" data-editable="">Educational Institutions</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary mb-2" data-editable="">50+</div>
                        <p class="text-gray-600 dark:text-gray-300" data-editable="">Subject Areas</p>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-xl">
                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-university text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">University of Hong Kong</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"ByteWise has revolutionized our computer science curriculum with personalized AI tutoring."</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-school text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">HKUST</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"Our students love the 24/7 availability of AI tutors for engineering problem-solving."</p>
                        </div>
                        
                        <div class="text-center">
                            <div class="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-primary text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-lg mb-2" data-editable="">CUHK</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm" data-editable="">"The analytics help us identify struggling students early and provide targeted support."</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section id="cta-section" class="editable-section py-20 gradient-bg">
            <div class="edit-controls">
                <button @click="saveSection('cta-section')" class="save-btn bg-white text-primary px-3 py-1 rounded text-sm hover:bg-gray-100 transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl lg:text-5xl text-white mb-6 elegant-font" data-editable="">
                    Ready to Transform Education?
                </h2>
                <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto" data-editable="">
                    Join thousands of educators and students who are already using ByteWise to create better learning experiences.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="px-8 py-4 bg-white text-primary rounded-xl hover:bg-gray-100 transition-all font-medium text-lg" data-editable="">
                        Start Free Trial
                    </button>
                    <button class="px-8 py-4 border-2 border-white text-white rounded-xl hover:bg-white hover:text-primary transition-all font-medium text-lg" data-editable="">
                        Schedule Demo
                    </button>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer id="footer-section" class="editable-section py-16 bg-gray-900 text-gray-300">
            <div class="edit-controls">
                <button @click="saveSection('footer-section')" class="save-btn bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors">
                    <i class="fas fa-save"></i> Save
                </button>
            </div>
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-4 gap-8 mb-8">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                <i class="fas fa-robot text-white"></i>
                            </div>
                            <span class="text-xl font-bold text-white" data-editable="">ByteWise</span>
                        </div>
                        <p class="text-sm" data-editable="">Transforming education with AI-powered chatbots for personalized learning experiences.</p>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-white mb-4 elegant-font" data-editable="">Product</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Features</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Pricing</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">API</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Integrations</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-white mb-4 elegant-font" data-editable="">Resources</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Documentation</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Blog</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Help Center</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Community</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-white mb-4 elegant-font" data-editable="">Company</h4>
                        <ul class="space-y-2 text-sm">
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">About</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Careers</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Privacy</a></li>
                            <li><a href="#" class="hover:text-primary transition-colors" data-editable="" data-editable-link="">Terms</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <p class="text-sm" data-editable="">© 2024 ByteWise. All rights reserved.</p>
                    <div class="flex space-x-4 mt-4 md:mt-0">
                        <a href="#" class="hover:text-primary transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="hover:text-primary transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="hover:text-primary transition-colors">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const href = this.getAttribute('href');
                
                // Check if href is valid and not just '#'
                if (href && href.length > 1 && href !== '#') {
                    try {
                        const target = document.querySelector(href);
                        if (target) {
                            target.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    } catch (error) {
                        console.warn('Invalid selector:', href);
                        // If selector is invalid, just ignore the error
                    }
                }
            });
        });
    </script>



</body></html>