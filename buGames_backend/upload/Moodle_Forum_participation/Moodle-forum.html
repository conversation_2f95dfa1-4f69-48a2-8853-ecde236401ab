<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moodle Forum Integration System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'primary-hover': '#4F4EC4',
                        moodle: '#f7931e',
                        'moodle-hover': '#e8851a',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen transition-colors duration-300">
    <div class="container mx-auto px-4 py-8 max-w-5xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">
                <i class="fas fa-comments mr-2"></i>
                Moodle Forum Integration
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Submit assignments and receive feedback through Moodle forums</p>
        </div>

        <!-- Role Selection -->
        <div id="roleSelection" class="mb-8">
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold mb-4 text-center">Select Your Role</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <button onclick="selectRole('student')" class="p-6 bg-blue-50 dark:bg-blue-900/20 border-2 border-blue-200 dark:border-blue-700 rounded-lg hover:border-blue-400 dark:hover:border-blue-500 transition-colors duration-200 text-center group">
                        <div class="text-blue-600 dark:text-blue-400 text-4xl mb-3 group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-300">Student</h3>
                        <p class="text-sm text-blue-600 dark:text-blue-400 mt-2">Submit your assignment to the forum</p>
                    </button>
                    <button onclick="selectRole('teacher')" class="p-6 bg-green-50 dark:bg-green-900/20 border-2 border-green-200 dark:border-green-700 rounded-lg hover:border-green-400 dark:hover:border-green-500 transition-colors duration-200 text-center group">
                        <div class="text-green-600 dark:text-green-400 text-4xl mb-3 group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-green-800 dark:text-green-300">Teacher</h3>
                        <p class="text-sm text-green-600 dark:text-green-400 mt-2">Review and comment on submissions</p>
                    </button>
                </div>
            </div>
        </div>

        <!-- Student Interface -->
        <div id="studentInterface" class="hidden">
            <!-- Course Information -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-info-circle mr-2 text-primary"></i>
                    Course Information
                </h2>
                <div class="grid md:grid-cols-3 gap-4 text-sm">
                    <div><span class="font-medium">Course:</span> <span class="text-primary">CS301 - Advanced Programming</span></div>
                    <div><span class="font-medium">Section:</span> <span class="text-primary">A1</span></div>
                    <div><span class="font-medium">Assignment:</span> <span class="text-primary">Project Reflection</span></div>
                </div>
            </div>

            <!-- Student Information -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-user mr-2 text-primary"></i>
                    Student Information
                </h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">Student Name</label>
                        <input type="text" id="studentName" value="Sarah Johnson" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Student ID</label>
                        <input type="text" id="studentId" value="CS2024001" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                    </div>
                </div>
            </div>

            <!-- Assignment Task -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700 mb-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center text-blue-800 dark:text-blue-300">
                    <i class="fas fa-tasks mr-2"></i>
                    Assignment Task
                </h2>
                <div class="text-blue-700 dark:text-blue-300">
                    <p class="mb-3"><strong>Task:</strong> Reflect on your recent programming project and discuss the following:</p>
                    <ul class="list-disc pl-6 space-y-1">
                        <li>What were the main challenges you faced during development?</li>
                        <li>How did you overcome these challenges?</li>
                        <li>What would you do differently if you started the project again?</li>
                        <li>What new skills or technologies did you learn?</li>
                    </ul>
                    <p class="mt-3 text-sm"><strong>Note:</strong> Your response will be posted to the course forum for peer discussion and instructor feedback.</p>
                </div>
            </div>

            <!-- Submission Form -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-edit mr-2 text-primary"></i>
                    Your Response
                </h2>
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Assignment Response</label>
                    <textarea id="studentResponse" rows="12" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" placeholder="Write your reflection here..."></textarea>
                    <div class="text-sm text-gray-500 mt-2" id="wordCount">Word count: 0 (aim for 300+ words for a complete reflection)</div>
                </div>
                
                <!-- What happens next explanation -->
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 mb-4">
                    <h4 class="font-semibold text-yellow-800 dark:text-yellow-300 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>What happens when you submit:
                    </h4>
                    <ol class="list-decimal pl-6 text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                        <li>Your response will be automatically posted to the <strong>"CS301 - Assignment Submissions"</strong> forum in Moodle</li>
                        <li>The post title will be: <strong>"Project Reflection - [Your Name] ([Student ID])"</strong></li>
                        <li>Your instructor and classmates will be notified of the new forum post</li>
                        <li>You'll receive a confirmation email with a link to view your post in Moodle</li>
                        <li>Your instructor can provide feedback directly as a reply to your forum post</li>
                    </ol>
                </div>

                <div class="flex justify-between items-center">
                    <button onclick="selectRole('')" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Role Selection
                    </button>
                    <button onclick="submitAssignment()" class="px-6 py-3 bg-moodle hover:bg-moodle-hover text-white rounded-md transition-colors duration-200 flex items-center font-semibold">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Submit to Moodle Forum
                    </button>
                </div>
            </div>
        </div>

        <!-- Teacher Interface -->
        <div id="teacherInterface" class="hidden">
            <!-- Course Information -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-info-circle mr-2 text-primary"></i>
                    Course Dashboard
                </h2>
                <div class="grid md:grid-cols-4 gap-4 text-sm">
                    <div><span class="font-medium">Course:</span> <span class="text-primary">CS301 - Advanced Programming</span></div>
                    <div><span class="font-medium">Section:</span> <span class="text-primary">A1</span></div>
                    <div><span class="font-medium">Assignment:</span> <span class="text-primary">Project Reflection</span></div>
                    <div><span class="font-medium">Submissions:</span> <span class="text-green-600 font-semibold">12 new</span></div>
                </div>
            </div>

            <!-- Student Submissions -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-list mr-2 text-primary"></i>
                    Recent Forum Submissions
                </h2>
                
                <!-- Sample submission -->
                <div class="space-y-4">
                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow duration-200">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-semibold text-lg">Project Reflection - Sarah Johnson (CS2024001)</h4>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-clock mr-1"></i>Posted 2 hours ago
                                    <i class="fas fa-eye ml-3 mr-1"></i>15 views
                                    <i class="fas fa-comments ml-3 mr-1"></i>0 replies
                                </div>
                            </div>
                            <span class="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium">
                                New
                            </span>
                        </div>
                        <div class="text-gray-700 dark:text-gray-300 mb-4">
                            <p class="line-clamp-3">During my recent web application project, I encountered several significant challenges that pushed me to grow as a developer. The main challenge was implementing real-time data synchronization between the frontend and backend...</p>
                        </div>
                        <button onclick="openFeedbackModal('Sarah Johnson', 'CS2024001')" class="px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200 flex items-center">
                            <i class="fas fa-comment mr-2"></i>
                            Provide Feedback
                        </button>
                    </div>

                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start mb-3">
                            <div>
                                <h4 class="font-semibold text-lg">Project Reflection - Michael Chen (CS2024002)</h4>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-clock mr-1"></i>Posted 4 hours ago
                                    <i class="fas fa-eye ml-3 mr-1"></i>8 views
                                    <i class="fas fa-comments ml-3 mr-1"></i>1 reply
                                </div>
                            </div>
                            <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium">
                                Responded
                            </span>
                        </div>
                        <div class="text-gray-700 dark:text-gray-300 mb-4">
                            <p class="line-clamp-3">Looking back at my machine learning project, I realize there were many lessons learned throughout the development process. Initially, I underestimated the complexity of data preprocessing...</p>
                        </div>
                        <button onclick="openFeedbackModal('Michael Chen', 'CS2024002')" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200 flex items-center">
                            <i class="fas fa-eye mr-2"></i>
                            View &amp; Add Feedback
                        </button>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <button class="px-4 py-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 rounded-md transition-colors duration-200">
                        Load More Submissions
                    </button>
                </div>
            </div>

            <div class="flex justify-start">
                <button onclick="selectRole('')" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Role Selection
                </button>
            </div>
        </div>

        <!-- Submission Success Modal -->
        <div id="submissionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-8 max-w-lg mx-4">
                <div class="text-center">
                    <div class="text-green-500 text-6xl mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Assignment Submitted Successfully!</h3>
                    <div class="text-left mb-6 space-y-3">
                        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-300 mb-2">
                                <i class="fas fa-check mr-2"></i>What just happened:
                            </h4>
                            <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                                <li>✓ Your response was posted to the "CS301 - Assignment Submissions" forum</li>
                                <li>✓ Post title: "Project Reflection - <span id="submittedName"></span> (<span id="submittedId"></span>)"</li>
                                <li>✓ Forum notification sent to instructor and classmates</li>
                                <li>✓ Confirmation email sent to your student email</li>
                            </ul>
                        </div>
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-300 mb-2">
                                <i class="fas fa-info-circle mr-2"></i>Next steps:
                            </h4>
                            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                <li>• Your instructor will review and provide feedback</li>
                                <li>• Feedback will appear as a reply to your forum post</li>
                                <li>• You'll receive email notifications for any replies</li>
                                <li>• You can respond to feedback in the forum</li>
                            </ul>
                        </div>
                    </div>
                    <div class="bg-moodle/10 border-2 border-moodle/30 rounded-lg p-4 mb-6">
                        <div class="flex items-center mb-3">
                            <i class="fas fa-external-link-alt text-moodle mr-2"></i>
                            <h4 class="font-semibold text-moodle">Your Forum Post is Live!</h4>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Click the link below to view your submission in the Moodle forum:</p>
                        <a href="#" id="forumLink" class="inline-flex items-center px-4 py-2 bg-moodle hover:bg-moodle-hover text-white rounded-md transition-colors duration-200 text-sm font-medium break-all" target="_blank">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            <!-- Link will be generated -->
                        </a>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                            <i class="fas fa-info-circle mr-1"></i>
                            Bookmark this link to easily return to your post and check for instructor feedback
                        </p>
                    </div>
                    <button onclick="closeSubmissionModal()" class="px-6 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200">
                        Continue
                    </button>
                </div>
            </div>
        </div>

        <!-- Teacher Feedback Modal -->
        <div id="feedbackModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-8 max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold">Provide Feedback</h3>
                    <button onclick="closeFeedbackModal()" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <!-- Student Info -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                    <div class="grid md:grid-cols-2 gap-4 text-sm">
                        <div><span class="font-medium">Student:</span> <span id="feedbackStudentName"></span></div>
                        <div><span class="font-medium">Student ID:</span> <span id="feedbackStudentId"></span></div>
                    </div>
                </div>

                <!-- Sample student submission preview -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold mb-3">Student Submission (Preview)</h4>
                    <div class="bg-white dark:bg-gray-800 rounded p-4 text-sm max-h-40 overflow-y-auto">
                        <p class="mb-3">During my recent web application project, I encountered several significant challenges that pushed me to grow as a developer. The main challenge was implementing real-time data synchronization between the frontend and backend.</p>
                        <p class="mb-3">Initially, I tried using simple HTTP polling, but this approach was inefficient and caused performance issues. After researching alternatives, I decided to implement WebSocket connections, which was completely new to me.</p>
                        <p>The learning curve was steep, but through online tutorials and documentation, I managed to successfully implement real-time features. If I were to start again, I would begin with WebSockets from the beginning and spend more time on the initial architecture planning...</p>
                    </div>
                </div>

                <!-- Feedback Form -->
                <div class="mb-6">
                    <label class="block text-sm font-medium mb-2">Your Feedback</label>
                    <textarea id="teacherFeedback" rows="8" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" placeholder="Provide constructive feedback to the student..."></textarea>
                </div>

                <!-- Grade Section -->
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">Grade (Optional)</label>
                        <select id="assignmentGrade" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                            <option value="">No grade assigned</option>
                            <option value="A+">A+ (Excellent)</option>
                            <option value="A">A (Very Good)</option>
                            <option value="B+">B+ (Good)</option>
                            <option value="B">B (Satisfactory)</option>
                            <option value="C+">C+ (Acceptable)</option>
                            <option value="C">C (Needs Improvement)</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Feedback Type</label>
                        <select id="feedbackType" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                            <option value="public">Public Reply (visible to all students)</option>
                            <option value="private">Private Message (only student can see)</option>
                        </select>
                    </div>
                </div>

                <!-- What happens next explanation -->
                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 mb-6">
                    <h4 class="font-semibold text-yellow-800 dark:text-yellow-300 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>What happens when you submit feedback:
                    </h4>
                    <ol class="list-decimal pl-6 text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                        <li>Your feedback will be posted as a <strong>reply</strong> to the student's forum post in Moodle</li>
                        <li>The student will receive an <strong>email notification</strong> about your response</li>
                        <li>If you assigned a grade, it will be updated in the Moodle gradebook</li>
                        <li>Other students can see public replies (great for learning from feedback)</li>
                        <li>Private messages are sent directly to the student's Moodle inbox</li>
                    </ol>
                </div>

                <div class="flex justify-end space-x-4">
                    <button onclick="closeFeedbackModal()" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200">
                        Cancel
                    </button>
                    <button onclick="submitFeedback()" class="px-6 py-3 bg-moodle hover:bg-moodle-hover text-white rounded-md transition-colors duration-200 flex items-center font-semibold">
                        <i class="fas fa-reply mr-2"></i>
                        Send Feedback to Moodle
                    </button>
                </div>
            </div>
        </div>

        <!-- Feedback Success Modal -->
        <div id="feedbackSuccessModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-8 max-w-lg mx-4">
                <div class="text-center">
                    <div class="text-green-500 text-6xl mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Feedback Sent Successfully!</h3>
                    <div class="text-left mb-6 space-y-3">
                        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 dark:text-green-300 mb-2">
                                <i class="fas fa-check mr-2"></i>Your feedback has been:
                            </h4>
                            <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                                <li>✓ Posted as a reply to the student's forum post</li>
                                <li>✓ Email notification sent to <span id="notifiedStudent"></span></li>
                                <li>✓ Grade updated in Moodle gradebook (if assigned)</li>
                                <li>✓ Forum discussion updated with your feedback</li>
                            </ul>
                        </div>
                        <div class="bg-moodle/10 border-2 border-moodle/30 rounded-lg p-4">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-external-link-alt text-moodle mr-2"></i>
                                <h4 class="font-semibold text-moodle">View Forum Discussion</h4>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Click to view the updated forum post with your feedback:</p>
                            <a href="#" id="feedbackForumLink" class="inline-flex items-center px-4 py-2 bg-moodle hover:bg-moodle-hover text-white rounded-md transition-colors duration-200 text-sm font-medium" target="_blank">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                Open Forum Discussion
                            </a>
                        </div>
                    </div>
                    <button onclick="closeFeedbackSuccessModal()" class="px-6 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200">
                        Continue
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode handling
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Word count for student response
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('studentResponse');
            const wordCountEl = document.getElementById('wordCount');
            
            if (textarea && wordCountEl) {
                textarea.addEventListener('input', function() {
                    const words = this.value.trim().split(/\s+/).filter(word => word.length > 0);
                    const count = this.value.trim() === '' ? 0 : words.length;
                    
                    if (count < 300) {
                        wordCountEl.textContent = `Word count: ${count} (aim for 300+ words for a complete reflection)`;
                        wordCountEl.className = 'text-sm text-orange-500 mt-2';
                    } else {
                        wordCountEl.textContent = `Word count: ${count} ✓ Good length for reflection`;
                        wordCountEl.className = 'text-sm text-green-500 mt-2';
                    }
                });
            }
        });

        function selectRole(role) {
            // Hide all interfaces
            document.getElementById('roleSelection').style.display = role ? 'none' : 'block';
            document.getElementById('studentInterface').classList.toggle('hidden', role !== 'student');
            document.getElementById('teacherInterface').classList.toggle('hidden', role !== 'teacher');
        }

        function submitAssignment() {
            const studentName = document.getElementById('studentName').value;
            const studentId = document.getElementById('studentId').value;
            const response = document.getElementById('studentResponse').value;

            if (!response.trim()) {
                alert('Please write your response before submitting.');
                return;
            }



            // Generate mock forum link
            const postId = Math.floor(Math.random() * 10000) + 1000;
            const forumLink = `https://moodle.university.edu/mod/forum/discuss.php?d=${postId}&course=cs301_a1`;
            
            document.getElementById('submittedName').textContent = studentName;
            document.getElementById('submittedId').textContent = studentId;
            document.getElementById('forumLink').href = forumLink;
            document.getElementById('forumLink').textContent = forumLink;
            
            // Show success modal
            document.getElementById('submissionModal').classList.remove('hidden');
            document.getElementById('submissionModal').classList.add('flex');
        }

        function closeSubmissionModal() {
            document.getElementById('submissionModal').classList.add('hidden');
            document.getElementById('submissionModal').classList.remove('flex');
            
            // Reset form
            document.getElementById('studentResponse').value = '';
            document.getElementById('wordCount').textContent = 'Word count: 0';
            document.getElementById('wordCount').className = 'text-sm text-gray-500 mt-2';
        }

        function openFeedbackModal(studentName, studentId) {
            document.getElementById('feedbackStudentName').textContent = studentName;
            document.getElementById('feedbackStudentId').textContent = studentId;
            document.getElementById('feedbackModal').classList.remove('hidden');
            document.getElementById('feedbackModal').classList.add('flex');
        }

        function closeFeedbackModal() {
            document.getElementById('feedbackModal').classList.add('hidden');
            document.getElementById('feedbackModal').classList.remove('flex');
            
            // Reset form
            document.getElementById('teacherFeedback').value = '';
            document.getElementById('assignmentGrade').value = '';
            document.getElementById('feedbackType').value = 'public';
        }

        function submitFeedback() {
            const feedback = document.getElementById('teacherFeedback').value;
            const studentName = document.getElementById('feedbackStudentName').textContent;

            if (!feedback.trim()) {
                alert('Please write your feedback before submitting.');
                return;
            }

            // Generate mock forum link for the updated discussion
            const postId = Math.floor(Math.random() * 10000) + 1000;
            const feedbackForumLink = `https://moodle.university.edu/mod/forum/discuss.php?d=${postId}&course=cs301_a1#reply${Math.floor(Math.random() * 100) + 1}`;
            
            document.getElementById('notifiedStudent').textContent = studentName;
            document.getElementById('feedbackForumLink').href = feedbackForumLink;
            document.getElementById('feedbackForumLink').textContent = 'Open Forum Discussion';
            
            // Close feedback modal and show success
            closeFeedbackModal();
            document.getElementById('feedbackSuccessModal').classList.remove('hidden');
            document.getElementById('feedbackSuccessModal').classList.add('flex');
        }

        function closeFeedbackSuccessModal() {
            document.getElementById('feedbackSuccessModal').classList.add('hidden');
            document.getElementById('feedbackSuccessModal').classList.remove('flex');
        }
    </script>


</body></html>