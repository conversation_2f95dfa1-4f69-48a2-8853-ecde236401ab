<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Academic Writing Workshop - Interactive Learning Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        secondary: '#8B7CF8',
                        accent: '#C084FC'
                    }
                }
            }
        }
    </script>
    <style>
        .fade-in { opacity: 0; animation: fadeIn 0.5s ease-in forwards; }
        @keyframes fadeIn { to { opacity: 1; } }
        .slide-in { transform: translateX(-20px); opacity: 0; animation: slideIn 0.6s ease-out forwards; }
        @keyframes slideIn { to { transform: translateX(0); opacity: 1; } }
        .progress-bar { transition: width 0.3s ease; }
        .question-card { transition: all 0.3s ease; }
        .question-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <div id="app" class="min-h-screen">
        <!-- Header -->
        <header class="bg-primary text-white py-6 px-4 shadow-lg">
            <div class="max-w-6xl mx-auto">
                <h1 class="text-2xl md:text-3xl font-bold mb-2">Writing for Successful International Publication</h1>
                <p class="text-primary-100 text-sm md:text-base">Interactive Learning Platform - SCNU Workshops</p>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
            <div class="max-w-6xl mx-auto px-4">
                <div class="flex space-x-1 overflow-x-auto py-4">
                    <button onclick="showSection('overview')" class="nav-btn whitespace-nowrap px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-home mr-2"></i>Overview
                    </button>
                    <button onclick="showSection('session1')" class="nav-btn whitespace-nowrap px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-file-alt mr-2"></i>Session 1: AIMRaDC
                    </button>
                    <button onclick="showSection('session2')" class="nav-btn whitespace-nowrap px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-comments mr-2"></i>Session 2: Peer Review
                    </button>
                    <button onclick="showSection('session3')" class="nav-btn whitespace-nowrap px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-robot mr-2"></i>Session 3: AI Writing
                    </button>
                    <button onclick="showSection('practice')" class="nav-btn whitespace-nowrap px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-edit mr-2"></i>Practice
                    </button>
                    <button onclick="showSection('results')" class="nav-btn whitespace-nowrap px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>Results
                    </button>
                </div>
            </div>
        </nav>

        <!-- Progress Bar -->
        <div class="bg-gray-200 dark:bg-gray-700 h-2">
            <div id="progressBar" class="bg-primary h-2 progress-bar" style="width: 0%"></div>
        </div>

        <!-- Main Content -->
        <main class="max-w-6xl mx-auto px-4 py-8">
            <!-- Overview Section -->
            <section id="overview" class="section">
                <div class="text-center mb-12 fade-in">
                    <h2 class="text-3xl font-bold mb-4">Welcome to the Academic Writing Workshop</h2>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">Master the art of international academic publication through interactive learning</p>
                </div>

                <div class="grid md:grid-cols-3 gap-8 mb-12">
                    <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg slide-in">
                        <div class="text-primary text-3xl mb-4"><i class="fas fa-file-alt"></i></div>
                        <h3 class="text-xl font-semibold mb-3">Session 1: AIMRaDC Structure</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">Learn the essential structure of academic papers: Abstract, Introduction, Methods, Results, and Discussion &amp; Conclusion</p>
                        <ul class="text-sm space-y-1">
                            <li>• Three-element structure of results</li>
                            <li>• Five-part figure legends</li>
                            <li>• Introduction argument stages</li>
                            <li>• Discussion elements</li>
                        </ul>
                    </div>

                    <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg slide-in" style="animation-delay: 0.2s">
                        <div class="text-primary text-3xl mb-4"><i class="fas fa-comments"></i></div>
                        <h3 class="text-xl font-semibold mb-3">Session 2: Peer Review &amp; Ethics</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">Navigate the peer review process and understand publishing ethics</p>
                        <ul class="text-sm space-y-1">
                            <li>• Responding to reviewer comments</li>
                            <li>• Avoiding plagiarism</li>
                            <li>• Acceptable language re-use</li>
                            <li>• Duplicate publication issues</li>
                        </ul>
                    </div>

                    <div class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg slide-in" style="animation-delay: 0.4s">
                        <div class="text-primary text-3xl mb-4"><i class="fas fa-robot"></i></div>
                        <h3 class="text-xl font-semibold mb-3">Session 3: AI-Assisted Writing</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">Leverage AI tools for effective academic writing</p>
                        <ul class="text-sm space-y-1">
                            <li>• Understanding LLMs</li>
                            <li>• Prompt engineering skills</li>
                            <li>• Customizing AI chatbots</li>
                            <li>• Human-AI collaboration</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center">
                    <button onclick="showSection('session1')" class="bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                        Start Learning <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </section>

            <!-- Session 1: AIMRaDC Structure -->
            <section id="session1" class="section hidden">
                <h2 class="text-3xl font-bold mb-8 text-center">Session 1: The AIMRaDC Structure</h2>
                
                <!-- Background Reading Material -->
                <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl shadow-lg mb-8">
                    <h3 class="text-xl font-semibold mb-4 text-primary">📚 Background Reading</h3>
                    <div class="prose dark:prose-invert max-w-none">
                        
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">The Results Section - A Three-Element Structure</h4>
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-400">
                                <ol class="list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300">
                                    <li><strong>Locating the Figure(s)/Table(s)</strong> for results</li>
                                    <li><strong>Highlighting important results</strong></li>
                                    <li><strong>Commenting on the results</strong></li>
                                </ol>
                                <p class="mt-3 text-sm italic text-gray-600 dark:text-gray-400">Elements 1 &amp; 2 are often combined. (Adapted from Cargill &amp; O'Connor, 2021, p. 36)</p>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Figure Legends</h4>
                            <p class="mb-3 text-gray-700 dark:text-gray-300">A Professor of Life Sciences spoke of writing Figure Legends: <em>"Principle: Describe the figure clear enough so as to make the readers understand the meaning without reference to the main text."</em></p>
                            
                            <h5 class="font-semibold mb-2 text-gray-800 dark:text-gray-200">A Five-Part Structure of Figure Legends:</h5>
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border-l-4 border-green-400">
                                <ol class="list-decimal list-inside space-y-2 text-gray-700 dark:text-gray-300">
                                    <li>A <strong>title</strong> which summarises what the figure is about.</li>
                                    <li><strong>Details of results or models</strong> shown in the figure or supplementary to the figure.</li>
                                    <li><strong>Additional explanation</strong> of the components of the figure, methods used, or essential details of the figure's contribution to the results story.</li>
                                    <li><strong>Description of the units</strong> or statistical notation included.</li>
                                    <li><strong>Explanation of any other symbols</strong> or notation used.</li>
                                </ol>
                                <p class="mt-3 text-sm italic text-gray-600 dark:text-gray-400">(Reproduced from Cargill &amp; O'Connor, 2021, pp. 31-32)</p>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">The Introduction Section - Argument Stages</h4>
                            <p class="mb-3 text-gray-700 dark:text-gray-300"><em>(From general to specific)</em></p>
                            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border-l-4 border-purple-400">
                                <div class="space-y-3 text-gray-700 dark:text-gray-300">
                                    <div><strong>Stage 1.</strong> Statements about the field of research to provide the reader with a setting or context for the problem to be investigated and to claim its centrality or importance.</div>
                                    <div><strong>Stage 2.</strong> More specific statements about the aspects of the problem already studied by other researchers, laying a foundation of information already known.</div>
                                    <div><strong>Stage 3.</strong> Statements that indicate the need for more investigation, creating a gap, a need for extension, or a research niche for the present study to fill.</div>
                                    <div><strong>Stage 4.</strong> Very specific statements giving the purpose/objectives of the writer's study or outlining its main activity or findings.</div>
                                    <div><strong>Stage 5.</strong> Statement(s) that give a positive value or benefit for carrying out the study (optional).</div>
                                    <div><strong>Stage 6.</strong> A "map": statements telling how the rest of the article is presented (only in some research fields).</div>
                                </div>
                                <p class="mt-3 text-sm italic text-gray-600 dark:text-gray-400">(Adapted from Cargill &amp; O'Connor, 2021, p. 48)</p>
                            </div>
                            <p class="mt-3 text-gray-700 dark:text-gray-300"><strong>Key linguistic signals:</strong> "However" and "We/we" are prominent signals in Stage 3 and Stage 4 of the Introduction.</p>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">The Abstract</h4>
                            <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border-l-4 border-orange-400">
                                <p class="text-gray-700 dark:text-gray-300">A commonly used move structure of Abstracts: <strong>B-P-M-R-C</strong></p>
                                <ul class="list-disc list-inside mt-2 space-y-1 text-gray-700 dark:text-gray-300">
                                    <li><strong>B</strong>ackground</li>
                                    <li><strong>P</strong>urpose</li>
                                    <li><strong>M</strong>ethods</li>
                                    <li><strong>R</strong>esults</li>
                                    <li><strong>C</strong>onclusion</li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
                
                <div id="session1-quiz" class="space-y-8">
                    <!-- Quiz questions will be populated here -->
                </div>

                <div class="flex justify-between mt-8">
                    <button onclick="showSection('overview')" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Back
                    </button>
                    <button onclick="completeSession(1)" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                        Complete Session 1 <i class="fas fa-check ml-2"></i>
                    </button>
                </div>
            </section>

            <!-- Session 2: Peer Review & Ethics -->
            <section id="session2" class="section hidden">
                <h2 class="text-3xl font-bold mb-8 text-center">Session 2: Peer Review and Publishing Ethics</h2>
                
                <!-- Background Reading Material -->
                <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl shadow-lg mb-8">
                    <h3 class="text-xl font-semibold mb-4 text-primary">📚 Background Reading</h3>
                    <div class="prose dark:prose-invert max-w-none">
                        
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Peer Review: The Other Side of the Story</h4>
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-400">
                                <p class="text-gray-700 dark:text-gray-300 mb-3"><strong>Y. Li's Question:</strong> "How many papers do you review for journals every year? How many hours do you spend on reviewing a paper?"</p>
                                <p class="text-gray-700 dark:text-gray-300"><strong>Response from a Professor in Life Sciences:</strong> "Review <strong>20+ every year</strong>; have to turn down most of the invitations; <strong>3-4 h or more</strong> for each manuscript."</p>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">What Editors Want from Reviewers</h4>
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border-l-4 border-green-400">
                                <p class="text-gray-700 dark:text-gray-300 italic">"How grateful am I, when receiving an intelligent report full of good advice and references, where the author is encouraged and challenged, pushed to spell out assumptions, encouraged to cast off tangential concerns or weak analyses, with suggestions for strengthening the analyses and interpretations in order to bring out the best in the paper."</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">(Dewaele, 2020, p. 160)</p>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Textual Plagiarism (文字表述剽窃)</h4>
                            <p class="mb-3 text-gray-700 dark:text-gray-300">Now officially recognized as a form of academic misconduct, aligning with international standards:</p>
                            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border-l-4 border-red-400">
                                <p class="text-gray-700 dark:text-gray-300 mb-2"><strong>Key forms include:</strong></p>
                                <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300">
                                    <li>Using others' text without citation</li>
                                    <li>Using large text blocks with citation but without quotation marks</li>
                                    <li>Partial citation (citing only some of multiple used sources)</li>
                                    <li>Paraphrasing without citation (including summarizing, reducing, or synonym replacement)</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Paraphrasing vs. Patchwriting</h4>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border-l-4 border-green-400">
                                    <p class="font-semibold text-green-800 dark:text-green-200 mb-2">✅ Proper Paraphrasing</p>
                                    <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                        <li>Use your own words and sentence structure</li>
                                        <li>Convey original meaning</li>
                                        <li>Recontextualize for your writing</li>
                                        <li>Always cite the source</li>
                                    </ul>
                                </div>
                                <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border-l-4 border-red-400">
                                    <p class="font-semibold text-red-800 dark:text-red-200 mb-2">❌ Patchwriting (Plagiarism)</p>
                                    <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                        <li>Mixing source words with your own</li>
                                        <li>Keeping original sentence structure</li>
                                        <li>Simple word substitution</li>
                                        <li>Considered plagiarism</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Secondary Publication Guidelines (ICMJE)</h4>
                            <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border-l-4 border-orange-400">
                                <p class="text-gray-700 dark:text-gray-300 mb-2"><strong>Secondary publication can be acceptable if:</strong></p>
                                <ol class="list-decimal list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                    <li>Approval from editors of both journals</li>
                                    <li>Priority of primary publication is respected</li>
                                    <li>Intended for different group of readers</li>
                                    <li>Secondary version reflects primary faithfully</li>
                                    <li>Clear acknowledgment of prior publication</li>
                                    <li>Title indicates secondary publication status</li>
                                </ol>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Responding to Reviewer Comments</h4>
                            <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border-l-4 border-purple-400">
                                <p class="text-gray-700 dark:text-gray-300 mb-2"><strong>Key strategies:</strong></p>
                                <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                    <li><strong>Always thank the reviewer first</strong></li>
                                    <li><strong>Address each concern specifically</strong></li>
                                    <li><strong>Provide evidence or justification</strong></li>
                                    <li><strong>Acknowledge limitations when appropriate</strong></li>
                                    <li><strong>Indicate specific changes made</strong></li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
                
                <div id="session2-quiz" class="space-y-8">
                    <!-- Quiz questions will be populated here -->
                </div>

                <div class="flex justify-between mt-8">
                    <button onclick="showSection('session1')" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Previous
                    </button>
                    <button onclick="completeSession(2)" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                        Complete Session 2 <i class="fas fa-check ml-2"></i>
                    </button>
                </div>
            </section>

            <!-- Session 3: AI-Assisted Writing -->
            <section id="session3" class="section hidden">
                <h2 class="text-3xl font-bold mb-8 text-center">Session 3: AI-Assisted Academic Writing</h2>
                
                <!-- Background Reading Material -->
                <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-xl shadow-lg mb-8">
                    <h3 class="text-xl font-semibold mb-4 text-primary">📚 Background Reading</h3>
                    <div class="prose dark:prose-invert max-w-none">
                        
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Understanding Large Language Models (LLMs)</h4>
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-400">
                                <p class="text-gray-700 dark:text-gray-300 mb-2"><strong>The Concept of Simulation:</strong></p>
                                <p class="text-gray-700 dark:text-gray-300">Large Language Models work through <strong>simulation</strong> - generating text based on patterns and probabilities learned from training data, not through actual understanding or consciousness.</p>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Prompt Engineering for Academic Writing</h4>
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border-l-4 border-green-400">
                                <p class="text-gray-700 dark:text-gray-300 mb-2"><strong>Key skill needed:</strong> Prompt engineering skills for simulating human intelligence</p>
                                <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                    <li>Be specific about the AI's role and expertise level</li>
                                    <li>Provide clear, numbered tasks</li>
                                    <li>Specify the format you want for the output</li>
                                    <li>Include context about your academic level</li>
                                    <li>Ask for feasible and specific suggestions</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">AI Use Cases in Academic Writing</h4>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border-l-4 border-purple-400">
                                    <p class="font-semibold text-purple-800 dark:text-purple-200 mb-2">Research &amp; Analysis</p>
                                    <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                        <li>Analyzing published authors' introduction techniques</li>
                                        <li>Identifying research gaps</li>
                                        <li>Highlighting research novelty</li>
                                        <li>Selecting and citing articles based on titles/abstracts</li>
                                    </ul>
                                </div>
                                <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border-l-4 border-orange-400">
                                    <p class="font-semibold text-orange-800 dark:text-orange-200 mb-2">Writing &amp; Collaboration</p>
                                    <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                        <li>Drafting manuscripts at paragraph level</li>
                                        <li>Human-AI collaboration approach</li>
                                        <li>Customizing chatbots for specific challenges</li>
                                        <li>Testing outputs for academic accuracy and tone</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Human-AI Collaboration Principles</h4>
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border-l-4 border-yellow-400">
                                <p class="text-gray-700 dark:text-gray-300 mb-2"><strong>Key principle:</strong> AI assists but does NOT replace human input</p>
                                <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                    <li>Human oversight and input remain essential</li>
                                    <li>Testing and refining AI outputs is crucial</li>
                                    <li>Focus on collaboration, not replacement</li>
                                    <li>Maintain academic integrity and accuracy</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h4 class="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Developing AI Platforms for Research Students</h4>
                            <div class="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg border-l-4 border-teal-400">
                                <p class="text-gray-700 dark:text-gray-300 mb-2"><strong>Goals for collaborative development:</strong></p>
                                <ul class="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 text-sm">
                                    <li>Gather participants' feedback on challenges and needs in ERPP (English for Research Publication Purposes)</li>
                                    <li>Brainstorm ideas for improving and expanding AI platforms</li>
                                    <li>Discuss opportunities for collaboration and co-creation</li>
                                    <li>Address specific writing challenges faced by research students</li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
                
                <div id="session3-quiz" class="space-y-8">
                    <!-- Quiz questions will be populated here -->
                </div>

                <div class="flex justify-between mt-8">
                    <button onclick="showSection('session2')" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Previous
                    </button>
                    <button onclick="completeSession(3)" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                        Complete Session 3 <i class="fas fa-check ml-2"></i>
                    </button>
                </div>
            </section>

            <!-- Practice Section -->
            <section id="practice" class="section hidden">
                <h2 class="text-3xl font-bold mb-8 text-center">Practice Section: Open-Ended Questions</h2>
                
                <div id="practice-questions" class="space-y-8">
                    <!-- Practice questions will be populated here -->
                </div>

                <div class="flex justify-between mt-8">
                    <button onclick="showSection('session3')" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Previous
                    </button>
                    <button onclick="showSection('results')" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                        View Results <i class="fas fa-chart-bar ml-2"></i>
                    </button>
                </div>
            </section>

            <!-- Results Section -->
            <section id="results" class="section hidden">
                <h2 class="text-3xl font-bold mb-8 text-center">Your Learning Results</h2>
                
                <div id="performance-summary" class="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg mb-8">
                    <!-- Performance summary will be populated here -->
                </div>

                <div class="text-center mb-8">
                    <button onclick="generatePDFReport()" class="bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
                        <i class="fas fa-download mr-2"></i>Download PDF Report
                    </button>
                </div>

                <div class="flex justify-center">
                    <button onclick="resetProgress()" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-redo mr-2"></i>Restart Learning
                    </button>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Initialize dark mode
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Global state
        let currentSection = 'overview';
        let userAnswers = {};
        let completedSessions = [];
        let practiceAnswers = {};

        // Quiz data structure
        const quizData = {
            session1: [
                {
                    type: 'multiple-choice',
                    question: 'According to the workshop materials, what are the three elements of the Results section structure?',
                    options: [
                        'A) Introduction, Methods, Discussion',
                        'B) Locating Figures/Tables, Highlighting results, Commenting on results',
                        'C) Background, Purpose, Conclusion',
                        'D) Hypothesis, Data, Analysis'
                    ],
                    correct: 1,
                    explanation: 'The three-element structure includes: 1) Locating the Figure(s)/Table(s), 2) Highlighting important results, 3) Commenting on the results. Elements 1 & 2 are often combined.'
                },
                {
                    type: 'true-false',
                    question: 'Figure legends should be written to make readers understand the meaning without reference to the main text.',
                    correct: true,
                    explanation: 'A Professor of Life Sciences stated: "Describe the figure clear enough so as to make the readers understand the meaning without reference to the main text."'
                },
                {
                    type: 'multiple-choice',
                    question: 'How many parts does the five-part structure of figure legends include?',
                    options: [
                        'A) Title, Methods, Results, Discussion, Conclusion',
                        'B) Title, Details of results, Additional explanation, Units/statistical notation, Symbol explanation',
                        'C) Background, Purpose, Methods, Results, Conclusion',
                        'D) Introduction, Hypothesis, Data, Analysis, Conclusion'
                    ],
                    correct: 1,
                    explanation: 'The five parts are: 1) Title summarizing the figure, 2) Details of results/models, 3) Additional explanation of components/methods, 4) Units/statistical notation, 5) Explanation of symbols/notation.'
                },
                {
                    type: 'matching',
                    question: 'Match the Introduction stages with their descriptions:',
                    pairs: [
                        ['Stage 1', 'Statements about the field of research to provide context'],
                        ['Stage 3', 'Statements indicating need for more investigation'],
                        ['Stage 4', 'Very specific statements giving the purpose of the study'],
                        ['Stage 6', 'A "map" telling how the rest of the article is presented']
                    ],
                    explanation: 'The Introduction follows a general to specific pattern with 6 stages, moving from broad context to specific research objectives.'
                },
                {
                    type: 'true-false',
                    question: 'The words "However" and "We/we" are prominent signals in Stage 3 and Stage 4 of the Introduction.',
                    correct: true,
                    explanation: 'These are key linguistic signals that help identify the transition from identifying research gaps (Stage 3) to stating research objectives (Stage 4).'
                },
                {
                    type: 'multiple-choice',
                    question: 'What is the commonly used move structure of Abstracts according to the workshop?',
                    options: [
                        'A) AIMRaDC',
                        'B) B-P-M-R-C (Background-Purpose-Methods-Results-Conclusion)',
                        'C) I-M-R-D',
                        'D) B-H-M-R-D'
                    ],
                    correct: 1,
                    explanation: 'The B-P-M-R-C structure (Background-Purpose-Methods-Results-Conclusion) is a commonly used move structure for abstracts in academic writing.'
                }
            ],
            session2: [
                {
                    type: 'multiple-choice',
                    question: 'According to the workshop materials, how many hours does a Life Sciences professor typically spend reviewing each manuscript?',
                    options: [
                        'A) 1-2 hours',
                        'B) 3-4 hours or more',
                        'C) 30 minutes',
                        'D) 6-8 hours'
                    ],
                    correct: 1,
                    explanation: 'A Professor in Life Sciences reported spending "3-4 h or more for each manuscript" and reviewing "20+ every year."'
                },
                {
                    type: 'true-false',
                    question: '"Textual plagiarism" is now officially recognized as a form of plagiarism in Chinese academic standards, aligning with international standards.',
                    correct: true,
                    explanation: 'The workshop notes that practices traditionally called "inappropriate source attribution" are now officially referred to as "textual plagiarism" in line with international standards.'
                },
                {
                    type: 'matching',
                    question: 'Match the reviewer comment types with appropriate response strategies:',
                    pairs: [
                        ['Aims are not clear', 'Rewrite aims clearly and ensure consistency with design'],
                        ['Supply additional data', 'Supply data if possible or explain why it\'s outside scope'],
                        ['Conclusions too strong', 'Ensure statements are justified and include constraints'],
                        ['Remove information', 'Remove if it doesn\'t change the story or move to supplementary']
                    ],
                    explanation: 'Different types of reviewer comments require specific response strategies to address the underlying concerns effectively.'
                },
                {
                    type: 'multiple-choice',
                    question: 'What is the main difference between paraphrasing and "patchwriting"?',
                    options: [
                        'A) Paraphrasing uses different sentence structure; patchwriting mixes source words with own words',
                        'B) They are the same thing',
                        'C) Patchwriting is acceptable; paraphrasing is not',
                        'D) Paraphrasing requires citations; patchwriting does not'
                    ],
                    correct: 0,
                    explanation: 'Paraphrasing uses your own words and sentence structure to convey original meaning, while patchwriting (mixing source words with your own at sentence level) is considered plagiarism.'
                },
                {
                    type: 'true-false',
                    question: 'According to ICMJE guidelines, secondary publication can be acceptable if certain conditions are met.',
                    correct: true,
                    explanation: 'Secondary publication can be justifiable if conditions are met including approval from both editors, respecting primary publication priority, targeting different readers, and proper acknowledgment.'
                },
                {
                    type: 'multiple-choice',
                    question: 'In the Methods section case study, what was the reviewer\'s main concern about citation-only descriptions?',
                    options: [
                        'A) They were too long',
                        'B) They provided insufficient elaboration and detail for readers',
                        'C) They were plagiarized',
                        'D) They used wrong citation format'
                    ],
                    correct: 1,
                    explanation: 'The reviewer wanted more elaboration: "As a reader I think it would be interesting to see some elaboration in the paper" and specific methodological details.'
                }
            ],
            session3: [
                {
                    type: 'multiple-choice',
                    question: 'What is the key concept behind how Large Language Models work?',
                    options: [
                        'A) They have consciousness and understanding',
                        'B) Simulation - generating text based on patterns and probabilities',
                        'C) They copy text from databases',
                        'D) They translate between languages'
                    ],
                    correct: 1,
                    explanation: 'LLMs work through simulation, generating text based on patterns and probabilities learned from training data, not through actual understanding or consciousness.'
                },
                {
                    type: 'true-false',
                    question: 'AI can help analyze and imitate how published authors introduce research topics and identify research gaps.',
                    correct: true,
                    explanation: 'The workshop specifically mentions "Analyzing and imitating how published authors introduce research topics, identify research gaps and highlight research novelty" as a use case for AI in academic writing.'
                },
                {
                    type: 'multiple-choice',
                    question: 'According to the workshop, what is a key skill needed for effective AI assistance in academic writing?',
                    options: [
                        'A) Programming skills',
                        'B) Prompt engineering skills for simulating human intelligence',
                        'C) Database management',
                        'D) Statistical analysis'
                    ],
                    correct: 1,
                    explanation: 'The workshop emphasizes "Prompt engineering skills for simulating human intelligence" as essential for effective AI assistance in academic writing.'
                },
                {
                    type: 'matching',
                    question: 'Match AI writing applications with their purposes:',
                    pairs: [
                        ['Selecting and citing articles', 'Based on titles and abstracts'],
                        ['Drafting manuscripts', 'Through human-AI collaboration at paragraph level'],
                        ['Customizing chatbots', 'To address specific writing challenges'],
                        ['Testing outputs', 'For academic accuracy and tone']
                    ],
                    explanation: 'Different AI applications serve specific purposes in the academic writing process, from research to drafting to refinement.'
                },
                {
                    type: 'true-false',
                    question: 'The workshop suggests that AI can completely replace human input in academic writing.',
                    correct: false,
                    explanation: 'The workshop emphasizes "human-AI collaboration" and testing/refining AI outputs, indicating that human oversight and input remain essential.'
                },
                {
                    type: 'multiple-choice',
                    question: 'What is one of the goals for collaboratively developing an AI platform for research students?',
                    options: [
                        'A) To eliminate the need for supervisors',
                        'B) To gather participants\' feedback on challenges and needs in ERPP',
                        'C) To replace peer review',
                        'D) To automate citation generation'
                    ],
                    correct: 1,
                    explanation: 'The workshop aims to gather "Participants\' feedback on challenges and needs in ERPP" (English for Research Publication Purposes) to improve AI platforms for students.'
                }
            ]
        };

        // Practice questions
        const practiceQuestions = [
            {
                question: 'Analyze the following introduction excerpt and identify which stage it represents. Explain your reasoning and suggest improvements:\n\n"Asteroid impacts are among the many natural hazards facing civilization. Although most asteroids bypass the Earth or cause little damage, the largest collisions have led to regional devastation..."',
                sampleAnswer: 'This excerpt represents Stage 1 of the Introduction structure - providing context and establishing the importance of the research field. The text establishes the general topic of asteroid impacts and their significance as a natural hazard.\n\nImprovements could include:\n1. More specific statistics about asteroid impact frequency\n2. Recent examples beyond historical events\n3. Clearer transition to current research gaps\n4. More precise language about the scale of the problem',
                tips: [
                    'Stage 1 should establish the broad research context',
                    'Use present tense for established facts',
                    'Include citations to establish credibility',
                    'Move from general to more specific within the stage'
                ]
            },
            {
                question: 'You receive a reviewer comment: "The experimental design is flawed because the sample size is too small and the control group is inadequate." Draft a professional response that addresses these concerns.',
                sampleAnswer: 'We thank the reviewer for their valuable feedback regarding our experimental design. We acknowledge the concerns about sample size and control group adequacy.\n\nRegarding sample size: Our power analysis (now included in the Methods section) indicated that n=30 per group provides 80% power to detect the expected effect size. However, we acknowledge this may limit generalizability and have added this limitation to our Discussion.\n\nRegarding the control group: We have clarified our control group selection criteria in the revised Methods section. The control group was matched for [specific parameters] to ensure validity. We have also added a paragraph discussing potential confounding variables and how they were addressed.',
                tips: [
                    'Always thank the reviewer first',
                    'Address each concern specifically',
                    'Provide evidence or justification for your choices',
                    'Acknowledge limitations when appropriate',
                    'Indicate specific changes made to the manuscript'
                ]
            },
            {
                question: 'Create a proper paraphrase of this sentence: "The owners of international scientific English should be international scientists not Englishmen or Americans." Include proper citation.',
                sampleAnswer: 'International scientific English belongs to the global scientific community rather than being limited to native English speakers (Author, Year).\n\nAlternative: Scientific English used in international contexts should be considered the property of all researchers worldwide, not exclusively of those from English-speaking countries (Author, Year).',
                tips: [
                    'Use completely different sentence structure',
                    'Replace key terms with synonyms where appropriate',
                    'Maintain the original meaning',
                    'Always include proper citation',
                    'Avoid "patchwriting" (mixing source words with your own)'
                ]
            },
            {
                question: 'Design a prompt for an AI assistant to help identify research gaps in a literature review about renewable energy storage. What specific instructions would you include?',
                sampleAnswer: 'Prompt: "You are an expert academic writing assistant. Please analyze the following literature review section about renewable energy storage and help me identify potential research gaps. \n\nSpecific tasks:\n1. Identify areas where current research is insufficient or conflicting\n2. Look for methodological limitations mentioned by authors\n3. Note any calls for future research\n4. Suggest 3-5 specific research questions that could address these gaps\n5. Format your response with clear headings and bullet points\n\nPlease focus on: [insert specific focus area] and ensure suggestions are feasible for a [PhD/Masters] level project."',
                tips: [
                    'Be specific about the AI\'s role and expertise level',
                    'Provide clear, numbered tasks',
                    'Specify the format you want for the output',
                    'Include context about your academic level',
                    'Ask for feasible and specific suggestions',
                    'Consider follow-up prompts for refinement'
                ]
            }
        ];

        // Navigation and UI functions
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // Show target section
            document.getElementById(sectionId).classList.remove('hidden');
            currentSection = sectionId;
            
            // Update navigation
            updateNavigation();
            
            // Update progress
            updateProgress();
            
            // Load content if needed
            if (sectionId === 'session1' && document.getElementById('session1-quiz').children.length === 0) {
                loadQuiz('session1');
            } else if (sectionId === 'session2' && document.getElementById('session2-quiz').children.length === 0) {
                loadQuiz('session2');
            } else if (sectionId === 'session3' && document.getElementById('session3-quiz').children.length === 0) {
                loadQuiz('session3');
            } else if (sectionId === 'practice' && document.getElementById('practice-questions').children.length === 0) {
                loadPracticeQuestions();
            } else if (sectionId === 'results') {
                updateResultsSection();
            }
        }

        function updateNavigation() {
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('bg-primary', 'text-white');
                btn.classList.add('text-gray-600', 'dark:text-gray-400', 'hover:bg-gray-100', 'dark:hover:bg-gray-700');
            });
            
            const activeBtn = document.querySelector(`[onclick="showSection('${currentSection}')"]`);
            if (activeBtn) {
                activeBtn.classList.remove('text-gray-600', 'dark:text-gray-400', 'hover:bg-gray-100', 'dark:hover:bg-gray-700');
                activeBtn.classList.add('bg-primary', 'text-white');
            }
        }

        function updateProgress() {
            const totalSections = 3; // Sessions 1, 2, 3
            const progress = (completedSessions.length / totalSections) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        // Quiz loading and handling functions
        function loadQuiz(sessionId) {
            const container = document.getElementById(`${sessionId}-quiz`);
            const questions = quizData[sessionId];
            
            questions.forEach((question, index) => {
                const questionDiv = createQuestionElement(question, sessionId, index);
                container.appendChild(questionDiv);
            });
        }

        function createQuestionElement(question, sessionId, index) {
            const div = document.createElement('div');
            div.className = 'question-card bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg fade-in';
            
            let questionHtml = `
                <div class="mb-4">
                    <h3 class="text-lg font-semibold mb-3">Question ${index + 1}</h3>
                    <p class="text-gray-700 dark:text-gray-300 mb-4">${question.question}</p>
                </div>
            `;
            
            if (question.type === 'multiple-choice') {
                questionHtml += '<div class="space-y-2">';
                question.options.forEach((option, optIndex) => {
                    questionHtml += `
                        <label class="flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="${sessionId}_q${index}" value="${optIndex}" class="mr-3" onchange="saveAnswer('${sessionId}', ${index}, ${optIndex})">
                            <span class="text-base">${option}</span>
                        </label>
                    `;
                });
                questionHtml += '</div>';
            } else if (question.type === 'true-false') {
                questionHtml += `
                    <div class="space-y-2">
                        <label class="flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="${sessionId}_q${index}" value="true" class="mr-3" onchange="saveAnswer('${sessionId}', ${index}, true)">
                            <span class="text-base">True</span>
                        </label>
                        <label class="flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="${sessionId}_q${index}" value="false" class="mr-3" onchange="saveAnswer('${sessionId}', ${index}, false)">
                            <span class="text-base">False</span>
                        </label>
                    </div>
                `;
            } else if (question.type === 'matching') {
                questionHtml += `
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="space-y-3">
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-3">Drag items to match:</h4>
                            ${question.pairs.map((pair, pairIndex) => `
                                <div class="draggable-item bg-blue-100 dark:bg-blue-800 p-3 rounded-lg cursor-move border-2 border-transparent hover:border-blue-300 dark:hover:border-blue-600 transition-all" 
                                     draggable="true" 
                                     data-session="${sessionId}" 
                                     data-question="${index}" 
                                     data-pair="${pairIndex}"
                                     id="drag_${sessionId}_${index}_${pairIndex}">
                                    <span class="font-semibold text-blue-800 dark:text-blue-200">${pair[0]}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="space-y-3">
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-3">Drop zones:</h4>
                            ${question.pairs.map((pair, pairIndex) => `
                                <div class="drop-zone border-2 border-dashed border-gray-300 dark:border-gray-600 p-4 rounded-lg min-h-16 bg-gray-50 dark:bg-gray-700 transition-all hover:border-gray-400 dark:hover:border-gray-500" 
                                     data-session="${sessionId}" 
                                     data-question="${index}" 
                                     data-correct-pair="${pairIndex}"
                                     id="drop_${sessionId}_${index}_${pairIndex}">
                                    <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">Drop here:</div>
                                    <div class="font-medium">${pair[1]}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
            
            questionHtml += `
                <div id="${sessionId}_feedback_${index}" class="mt-4 hidden">
                    <div class="p-4 rounded-lg">
                        <p class="font-semibold mb-2">Explanation:</p>
                        <p class="text-gray-700 dark:text-gray-300">${question.explanation}</p>
                    </div>
                </div>
                <button onclick="showFeedback('${sessionId}', ${index})" class="mt-4 bg-secondary text-white px-4 py-2 rounded-lg hover:bg-secondary/90 transition-colors">
                    Show Explanation
                </button>
            `;
            
            div.innerHTML = questionHtml;
            
            // Add drag and drop event listeners for matching questions
            if (question.type === 'matching') {
                setTimeout(() => {
                    setupDragAndDrop(sessionId, index);
                }, 100);
            }
            
            return div;
        }

        function saveAnswer(sessionId, questionIndex, answer) {
            if (!userAnswers[sessionId]) {
                userAnswers[sessionId] = {};
            }
            userAnswers[sessionId][questionIndex] = answer;
            
            // Provide immediate feedback for MC and T/F questions
            showImmediateFeedback(sessionId, questionIndex, answer);
        }

        function saveMatchingAnswer(sessionId, questionIndex, pairIndex, answer) {
            if (!userAnswers[sessionId]) {
                userAnswers[sessionId] = {};
            }
            if (!userAnswers[sessionId][questionIndex]) {
                userAnswers[sessionId][questionIndex] = {};
            }
            userAnswers[sessionId][questionIndex][pairIndex] = answer;
        }

        function showImmediateFeedback(sessionId, questionIndex, answer) {
            const question = quizData[sessionId][questionIndex];
            const feedback = document.getElementById(`${sessionId}_feedback_${questionIndex}`);
            
            // Only show immediate feedback for MC and T/F questions
            if (question.type !== 'matching') {
                let isCorrect = false;
                if (question.type === 'multiple-choice') {
                    isCorrect = answer === question.correct;
                } else if (question.type === 'true-false') {
                    isCorrect = answer === question.correct;
                }
                
                // Show the feedback immediately
                feedback.classList.remove('hidden');
                feedback.className = `mt-4 p-4 rounded-lg ${isCorrect ? 'bg-green-100 dark:bg-green-900/20 border border-green-300 dark:border-green-700' : 'bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700'}`;
                
                // Add immediate feedback message
                const existingContent = feedback.innerHTML;
                const feedbackIcon = isCorrect ? '✅' : '❌';
                const feedbackText = isCorrect ? 'Correct!' : 'Incorrect';
                
                feedback.innerHTML = `
                    <div class="flex items-center mb-3">
                        <span class="text-2xl mr-2">${feedbackIcon}</span>
                        <span class="font-bold text-lg">${feedbackText}</span>
                    </div>
                    ${existingContent}
                `;
                
                // Disable all radio buttons for this question to prevent changes
                const radioButtons = document.querySelectorAll(`input[name="${sessionId}_q${questionIndex}"]`);
                radioButtons.forEach(radio => {
                    radio.disabled = true;
                    // Style the selected option
                    if (radio.checked) {
                        const label = radio.closest('label');
                        if (isCorrect) {
                            label.classList.add('bg-green-100', 'dark:bg-green-900/20', 'border-green-300', 'dark:border-green-700');
                        } else {
                            label.classList.add('bg-red-100', 'dark:bg-red-900/20', 'border-red-300', 'dark:border-red-700');
                        }
                        
                        // Show correct answer if wrong
                        if (!isCorrect && question.type === 'multiple-choice') {
                            const correctLabel = document.querySelector(`input[name="${sessionId}_q${questionIndex}"][value="${question.correct}"]`).closest('label');
                            correctLabel.classList.add('bg-green-100', 'dark:bg-green-900/20', 'border-green-300', 'dark:border-green-700');
                            correctLabel.innerHTML += ' <span class="ml-2 text-green-600 font-semibold">(Correct Answer)</span>';
                        }
                    }
                });
                
                // Hide the "Show Explanation" button since feedback is already shown
                const showButton = feedback.parentElement.querySelector('button[onclick*="showFeedback"]');
                if (showButton) {
                    showButton.style.display = 'none';
                }
            }
        }
        
        function showFeedback(sessionId, questionIndex) {
            const feedback = document.getElementById(`${sessionId}_feedback_${questionIndex}`);
            feedback.classList.remove('hidden');
            
            const question = quizData[sessionId][questionIndex];
            const userAnswer = userAnswers[sessionId] && userAnswers[sessionId][questionIndex];
            
            let isCorrect = false;
            if (question.type === 'multiple-choice') {
                isCorrect = userAnswer === question.correct;
            } else if (question.type === 'true-false') {
                isCorrect = userAnswer === question.correct;
            } else if (question.type === 'matching') {
                // For matching, check if all pairs are correctly matched
                isCorrect = question.pairs.every((pair, index) => {
                    return userAnswer && userAnswer[index] && 
                           userAnswer[index].toLowerCase().includes(pair[1].toLowerCase().substring(0, 10));
                });
            }
            
            feedback.className = `mt-4 p-4 rounded-lg ${isCorrect ? 'bg-green-100 dark:bg-green-900/20 border border-green-300 dark:border-green-700' : 'bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700'}`;
        }

        function completeSession(sessionNumber) {
            if (!completedSessions.includes(sessionNumber)) {
                completedSessions.push(sessionNumber);
            }
            updateProgress();
            
            if (sessionNumber < 3) {
                showSection(`session${sessionNumber + 1}`);
            } else {
                showSection('practice');
            }
        }

        // Practice questions
        function loadPracticeQuestions() {
            const container = document.getElementById('practice-questions');
            
            practiceQuestions.forEach((question, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg fade-in';
                
                questionDiv.innerHTML = `
                    <h3 class="text-lg font-semibold mb-4">Practice Question ${index + 1}</h3>
                    <div class="mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">${question.question}</p>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Your Answer:</label>
                        <textarea id="practice_${index}" rows="6" 
                                  class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-base resize-vertical"
                                  placeholder="Type your answer here..."
                                  onchange="savePracticeAnswer(${index}, this.value)"></textarea>
                    </div>
                    <button onclick="showPracticeFeedback(${index})" 
                            class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors mb-4">
                        Show Sample Answer & Tips
                    </button>
                    <div id="practice_feedback_${index}" class="hidden mt-4 space-y-4">
                        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <h4 class="font-semibold mb-2">Sample Answer:</h4>
                            <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">${question.sampleAnswer}</p>
                        </div>
                        <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <h4 class="font-semibold mb-2">Tips:</h4>
                            <ul class="list-disc list-inside space-y-1">
                                ${question.tips.map(tip => `<li class="text-gray-700 dark:text-gray-300">${tip}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `;
                
                container.appendChild(questionDiv);
            });
        }

        function savePracticeAnswer(index, answer) {
            practiceAnswers[index] = answer;
        }

        function showPracticeFeedback(index) {
            const feedback = document.getElementById(`practice_feedback_${index}`);
            feedback.classList.remove('hidden');
        }

        // Results and reporting
        function updateResultsSection() {
            const container = document.getElementById('performance-summary');
            
            let totalQuestions = 0;
            let correctAnswers = 0;
            let sessionScores = {};
            
            Object.keys(quizData).forEach(sessionId => {
                const questions = quizData[sessionId];
                let sessionCorrect = 0;
                
                questions.forEach((question, index) => {
                    totalQuestions++;
                    const userAnswer = userAnswers[sessionId] && userAnswers[sessionId][index];
                    
                    let isCorrect = false;
                    if (question.type === 'multiple-choice') {
                        isCorrect = userAnswer === question.correct;
                    } else if (question.type === 'true-false') {
                        isCorrect = userAnswer === question.correct;
                    } else if (question.type === 'matching') {
                        isCorrect = question.pairs.every((pair, pairIndex) => {
                            return userAnswer && userAnswer[pairIndex] && 
                                   userAnswer[pairIndex].toLowerCase().includes(pair[1].toLowerCase().substring(0, 10));
                        });
                    }
                    
                    if (isCorrect) {
                        correctAnswers++;
                        sessionCorrect++;
                    }
                });
                
                sessionScores[sessionId] = {
                    correct: sessionCorrect,
                    total: questions.length,
                    percentage: Math.round((sessionCorrect / questions.length) * 100)
                };
            });
            
            const overallPercentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
            
            container.innerHTML = `
                <h3 class="text-xl font-semibold mb-6">Performance Summary</h3>
                
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary mb-2">${overallPercentage}%</div>
                        <p class="text-gray-600 dark:text-gray-400">Overall Score</p>
                        <p class="text-sm text-gray-500">${correctAnswers}/${totalQuestions} correct</p>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-green-600 mb-2">${completedSessions.length}/3</div>
                        <p class="text-gray-600 dark:text-gray-400">Sessions Completed</p>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold">Session Breakdown:</h4>
                    ${Object.entries(sessionScores).map(([sessionId, score]) => `
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <span class="font-medium capitalize">${sessionId.replace('session', 'Session ')}</span>
                            <div class="text-right">
                                <span class="text-lg font-semibold ${score.percentage >= 70 ? 'text-green-600' : score.percentage >= 50 ? 'text-yellow-600' : 'text-red-600'}">${score.percentage}%</span>
                                <div class="text-sm text-gray-500">${score.correct}/${score.total}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                
                <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h4 class="font-semibold mb-2">Practice Questions Attempted:</h4>
                    <p class="text-gray-700 dark:text-gray-300">${Object.keys(practiceAnswers).length} out of ${practiceQuestions.length} practice questions completed</p>
                </div>
                
                <div class="mt-6 p-4 ${overallPercentage >= 70 ? 'bg-green-50 dark:bg-green-900/20' : 'bg-yellow-50 dark:bg-yellow-900/20'} rounded-lg">
                    <h4 class="font-semibold mb-2">Recommendations:</h4>
                    ${overallPercentage >= 80 ? 
                        '<p>Excellent work! You have a strong understanding of academic writing principles. Consider mentoring others or exploring advanced topics.</p>' :
                        overallPercentage >= 70 ?
                        '<p>Good performance! Review the areas where you scored lower and practice the open-ended questions to strengthen your skills.</p>' :
                        overallPercentage >= 50 ?
                        '<p>You have a basic understanding. We recommend reviewing the workshop materials and retaking the quizzes to improve your grasp of key concepts.</p>' :
                        '<p>Consider reviewing the workshop materials thoroughly and seeking additional support. Focus on understanding the fundamental concepts before proceeding.</p>'
                    }
                </div>
            `;
        }

        function generatePDFReport() {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF();
            
            // Title
            pdf.setFontSize(20);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Academic Writing Workshop - Performance Report', 20, 30);
            
            // Date
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'normal');
            pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45);
            
            // Overall performance
            let totalQuestions = 0;
            let correctAnswers = 0;
            
            Object.keys(quizData).forEach(sessionId => {
                quizData[sessionId].forEach((question, index) => {
                    totalQuestions++;
                    const userAnswer = userAnswers[sessionId] && userAnswers[sessionId][index];
                    
                    let isCorrect = false;
                    if (question.type === 'multiple-choice') {
                        isCorrect = userAnswer === question.correct;
                    } else if (question.type === 'true-false') {
                        isCorrect = userAnswer === question.correct;
                    } else if (question.type === 'matching') {
                        isCorrect = question.pairs.every((pair, pairIndex) => {
                            return userAnswer && userAnswer[pairIndex] && 
                                   userAnswer[pairIndex].toLowerCase().includes(pair[1].toLowerCase().substring(0, 10));
                        });
                    }
                    
                    if (isCorrect) correctAnswers++;
                });
            });
            
            const overallPercentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
            
            pdf.setFontSize(16);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Overall Performance', 20, 65);
            
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'normal');
            pdf.text(`Score: ${overallPercentage}% (${correctAnswers}/${totalQuestions} correct)`, 20, 80);
            pdf.text(`Sessions Completed: ${completedSessions.length}/3`, 20, 95);
            
            // Session breakdown
            let yPosition = 115;
            pdf.setFontSize(14);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Session Breakdown:', 20, yPosition);
            
            yPosition += 15;
            Object.keys(quizData).forEach(sessionId => {
                const questions = quizData[sessionId];
                let sessionCorrect = 0;
                
                questions.forEach((question, index) => {
                    const userAnswer = userAnswers[sessionId] && userAnswers[sessionId][index];
                    let isCorrect = false;
                    
                    if (question.type === 'multiple-choice') {
                        isCorrect = userAnswer === question.correct;
                    } else if (question.type === 'true-false') {
                        isCorrect = userAnswer === question.correct;
                    } else if (question.type === 'matching') {
                        isCorrect = question.pairs.every((pair, pairIndex) => {
                            return userAnswer && userAnswer[pairIndex] && 
                                   userAnswer[pairIndex].toLowerCase().includes(pair[1].toLowerCase().substring(0, 10));
                        });
                    }
                    
                    if (isCorrect) sessionCorrect++;
                });
                
                const sessionPercentage = Math.round((sessionCorrect / questions.length) * 100);
                
                pdf.setFontSize(12);
                pdf.setFont('helvetica', 'normal');
                pdf.text(`${sessionId.replace('session', 'Session ')}: ${sessionPercentage}% (${sessionCorrect}/${questions.length})`, 30, yPosition);
                yPosition += 15;
            });
            
            // Practice questions
            yPosition += 10;
            pdf.setFontSize(14);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Practice Questions:', 20, yPosition);
            
            yPosition += 15;
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'normal');
            pdf.text(`Completed: ${Object.keys(practiceAnswers).length}/${practiceQuestions.length}`, 30, yPosition);
            
            // Recommendations
            yPosition += 25;
            pdf.setFontSize(14);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Recommendations:', 20, yPosition);
            
            yPosition += 15;
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'normal');
            
            let recommendation = '';
            if (overallPercentage >= 80) {
                recommendation = 'Excellent work! You have a strong understanding of academic writing principles.';
            } else if (overallPercentage >= 70) {
                recommendation = 'Good performance! Review areas where you scored lower and practice more.';
            } else if (overallPercentage >= 50) {
                recommendation = 'Basic understanding achieved. Review materials and retake quizzes to improve.';
            } else {
                recommendation = 'Consider reviewing materials thoroughly and seeking additional support.';
            }
            
            const splitRecommendation = pdf.splitTextToSize(recommendation, 170);
            pdf.text(splitRecommendation, 20, yPosition);
            
            // Save the PDF
            pdf.save('Academic_Writing_Workshop_Report.pdf');
        }

        function resetProgress() {
            userAnswers = {};
            completedSessions = [];
            practiceAnswers = {};
            
            // Clear all quiz containers
            document.getElementById('session1-quiz').innerHTML = '';
            document.getElementById('session2-quiz').innerHTML = '';
            document.getElementById('session3-quiz').innerHTML = '';
            document.getElementById('practice-questions').innerHTML = '';
            
            updateProgress();
            showSection('overview');
        }

        // Drag and Drop functionality for matching questions
        function setupDragAndDrop(sessionId, questionIndex) {
            const draggableItems = document.querySelectorAll(`[data-session="${sessionId}"][data-question="${questionIndex}"].draggable-item`);
            const dropZones = document.querySelectorAll(`[data-session="${sessionId}"][data-question="${questionIndex}"].drop-zone`);
            
            let draggedElement = null;
            
            draggableItems.forEach(item => {
                item.addEventListener('dragstart', (e) => {
                    draggedElement = e.target;
                    e.target.style.opacity = '0.5';
                    e.dataTransfer.effectAllowed = 'move';
                });
                
                item.addEventListener('dragend', (e) => {
                    e.target.style.opacity = '1';
                    draggedElement = null;
                });
            });
            
            dropZones.forEach(zone => {
                zone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    zone.classList.add('border-blue-400', 'dark:border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                });
                
                zone.addEventListener('dragleave', (e) => {
                    zone.classList.remove('border-blue-400', 'dark:border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                });
                
                zone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    zone.classList.remove('border-blue-400', 'dark:border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                    
                    if (draggedElement) {
                        const draggedPairIndex = parseInt(draggedElement.dataset.pair);
                        const correctPairIndex = parseInt(zone.dataset.correctPair);
                        
                        // Check if already occupied
                        const existingMatch = zone.querySelector('.matched-item');
                        if (existingMatch) {
                            // Return existing item to original position
                            const originalIndex = existingMatch.dataset.originalPair;
                            const originalContainer = document.querySelector(`[data-session="${sessionId}"][data-question="${questionIndex}"] .space-y-3:first-child`);
                            originalContainer.appendChild(existingMatch);
                            existingMatch.classList.remove('matched-item', 'cursor-default');
                            existingMatch.classList.add('cursor-move');
                            existingMatch.draggable = true;
                        }
                        
                        // Move dragged item to drop zone
                        const clonedItem = draggedElement.cloneNode(true);
                        clonedItem.classList.remove('cursor-move', 'hover:border-blue-300', 'dark:hover:border-blue-600');
                        clonedItem.classList.add('matched-item', 'cursor-default');
                        clonedItem.draggable = false;
                        clonedItem.dataset.originalPair = draggedPairIndex;
                        
                        // Clear drop zone and add the item
                        const dropText = zone.querySelector('div:first-child');
                        if (dropText && dropText.textContent.includes('Drop here:')) {
                            dropText.style.display = 'none';
                        }
                        zone.appendChild(clonedItem);
                        
                        // Remove original draggable item
                        draggedElement.remove();
                        
                        // Provide immediate feedback
                        const isCorrect = draggedPairIndex === correctPairIndex;
                        if (isCorrect) {
                            zone.classList.add('bg-green-100', 'dark:bg-green-900/20', 'border-green-300', 'dark:border-green-700');
                            clonedItem.classList.add('bg-green-100', 'dark:bg-green-800');
                            
                            // Add success icon
                            clonedItem.innerHTML += ' <span class="ml-2 text-green-600 font-bold">✅</span>';
                        } else {
                            zone.classList.add('bg-red-100', 'dark:bg-red-900/20', 'border-red-300', 'dark:border-red-700');
                            clonedItem.classList.add('bg-red-100', 'dark:bg-red-800');
                            
                            // Add error icon
                            clonedItem.innerHTML += ' <span class="ml-2 text-red-600 font-bold">❌</span>';
                        }
                        
                        // Save the answer
                        saveMatchingAnswer(sessionId, questionIndex, draggedPairIndex, correctPairIndex);
                        
                        // Check if all items are matched
                        const question = quizData[sessionId][questionIndex];
                        const remainingItems = document.querySelectorAll(`[data-session="${sessionId}"][data-question="${questionIndex}"].draggable-item`);
                        
                        if (remainingItems.length === 0) {
                            // All items matched, show complete feedback
                            setTimeout(() => {
                                showMatchingComplete(sessionId, questionIndex);
                            }, 500);
                        }
                    }
                });
            });
        }
        
        function showMatchingComplete(sessionId, questionIndex) {
            const question = quizData[sessionId][questionIndex];
            const userAnswers = getUserMatchingAnswers(sessionId, questionIndex);
            
            let correctCount = 0;
            question.pairs.forEach((pair, index) => {
                if (userAnswers[index] === index) {
                    correctCount++;
                }
            });
            
            const isAllCorrect = correctCount === question.pairs.length;
            const feedback = document.getElementById(`${sessionId}_feedback_${questionIndex}`);
            
            feedback.classList.remove('hidden');
            feedback.className = `mt-4 p-4 rounded-lg ${isAllCorrect ? 'bg-green-100 dark:bg-green-900/20 border border-green-300 dark:border-green-700' : 'bg-orange-100 dark:bg-orange-900/20 border border-orange-300 dark:border-orange-700'}`;
            
            const feedbackIcon = isAllCorrect ? '✅' : '⚠️';
            const feedbackText = isAllCorrect ? 'Perfect Match!' : `${correctCount}/${question.pairs.length} Correct`;
            
            feedback.innerHTML = `
                <div class="flex items-center mb-3">
                    <span class="text-2xl mr-2">${feedbackIcon}</span>
                    <span class="font-bold text-lg">${feedbackText}</span>
                </div>
                <div class="p-4 rounded-lg">
                    <p class="font-semibold mb-2">Explanation:</p>
                    <p class="text-gray-700 dark:text-gray-300">${question.explanation}</p>
                </div>
            `;
            
            // Hide the show explanation button
            const showButton = feedback.parentElement.querySelector('button[onclick*="showFeedback"]');
            if (showButton) {
                showButton.style.display = 'none';
            }
        }
        
        function getUserMatchingAnswers(sessionId, questionIndex) {
            if (!userAnswers[sessionId] || !userAnswers[sessionId][questionIndex]) {
                return {};
            }
            return userAnswers[sessionId][questionIndex];
        }

        // Initialize the app
        window.addEventListener('load', function() {
            showSection('overview');
        });
    </script>


</body></html>