<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moodle Quiz Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'primary-hover': '#4F4EC4',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen transition-colors duration-300">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">
                <i class="fas fa-graduation-cap mr-2"></i>
                Moodle Quiz Generator
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Create and deploy quizzes to your Moodle course</p>
        </div>

        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-primary" id="step-text">Step 1: Quiz Structure</span>
                <span class="text-sm text-gray-500" id="progress-text">1 of 5</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="bg-primary h-2 rounded-full transition-all duration-300 ease-out" id="progress-bar" style="width: 20%"></div>
            </div>
        </div>

        <!-- Step 1: Quiz Structure -->
        <div id="step1" class="step-content">
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-cogs mr-2 text-primary"></i>
                    Define Quiz Structure
                </h2>
                <div class="grid md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">Multiple Choice Questions</label>
                        <input type="number" id="mcCount" min="0" max="50" value="0" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Fill in the Blank Questions</label>
                        <input type="number" id="fibCount" min="0" max="50" value="0" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Matching Questions</label>
                        <input type="number" id="matchCount" min="0" max="50" value="0" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                    </div>
                </div>
                <div class="mt-6 flex justify-end">
                    <button onclick="generateQuestionForms()" class="px-6 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-right mr-2"></i>
                        Next: Create Questions
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 2: Question Entry -->
        <div id="step2" class="step-content hidden">
            <div class="space-y-6" id="questionForms">
                <!-- Questions will be generated here -->
            </div>
            <div class="mt-6 flex justify-between">
                <button onclick="showStep(1)" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back
                </button>
                <button onclick="generatePreview()" class="px-6 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200 flex items-center">
                    <i class="fas fa-eye mr-2"></i>
                    Preview Quiz
                </button>
            </div>
        </div>

        <!-- Step 3: Quiz Preview -->
        <div id="step3" class="step-content hidden">
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-eye mr-2 text-primary"></i>
                    Quiz Preview
                </h2>
                <div class="bg-white dark:bg-gray-700 rounded-lg p-6 border-2 border-dashed border-gray-300 dark:border-gray-600">
                    <div class="text-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-300">Student View</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">This is how your quiz will appear to students</p>
                    </div>
                    <div id="quizPreview" class="space-y-6">
                        <!-- Preview content will be generated here -->
                    </div>
                </div>
                <div class="mt-6 flex justify-between">
                    <button onclick="showStep(2)" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Edit Questions
                    </button>
                    <button onclick="showStep(4)" class="px-6 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-right mr-2"></i>
                        Next: Course Details
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 4: Course Details -->
        <div id="step4" class="step-content hidden">
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-school mr-2 text-primary"></i>
                    Moodle Course Details
                </h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">Course Code</label>
                        <input type="text" id="courseCode" placeholder="e.g., CS101, MATH205" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Section Code</label>
                        <input type="text" id="sectionCode" placeholder="e.g., A1, B2, EVE" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                    </div>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium mb-2">Quiz Title</label>
                    <input type="text" id="quizTitle" placeholder="Enter quiz title" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700">
                </div>
                <div class="mt-6 flex justify-between">
                    <button onclick="showStep(3)" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Preview
                    </button>
                    <button onclick="showStep(5)" class="px-6 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-right mr-2"></i>
                        Review &amp; Submit
                    </button>
                </div>
            </div>
        </div>

        <!-- Step 5: Review & Submit -->
        <div id="step5" class="step-content hidden">
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i class="fas fa-check-circle mr-2 text-primary"></i>
                    Review Quiz
                </h2>
                <div id="quizSummary" class="mb-6">
                    <!-- Summary will be generated here -->
                </div>
                <div class="mt-6 flex justify-between">
                    <button onclick="showStep(4)" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors duration-200 flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back
                    </button>
                    <button onclick="deployQuiz()" class="px-8 py-3 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-200 flex items-center text-lg font-semibold">
                        <i class="fas fa-rocket mr-2"></i>
                        Deploy to Moodle
                    </button>
                </div>
            </div>
        </div>

        <!-- Success Modal -->
        <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-8 max-w-md mx-4 text-center">
                <div class="text-green-500 text-6xl mb-4">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="text-2xl font-bold mb-4">Quiz Deployed Successfully!</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">Your quiz has been successfully deployed to Moodle.</p>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-md p-4 mb-6">
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Access your quiz at:</p>
                    <a href="#" id="moodleLink" class="text-primary underline break-all" target="_blank">
                        <!-- Link will be generated -->
                    </a>
                </div>
                <button onclick="resetQuiz()" class="px-6 py-2 bg-primary hover:bg-primary-hover text-white rounded-md transition-colors duration-200">
                    Create Another Quiz
                </button>
            </div>
        </div>
    </div>

    <script>
        // Dark mode handling
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        let currentStep = 1;
        const stepTitles = [
            'Step 1: Quiz Structure',
            'Step 2: Create Questions',
            'Step 3: Preview Quiz',
            'Step 4: Course Details',
            'Step 5: Review & Submit'
        ];

        function showStep(step) {
            // Hide all steps
            for (let i = 1; i <= 5; i++) {
                document.getElementById(`step${i}`).classList.add('hidden');
            }
            
            // Show current step
            document.getElementById(`step${step}`).classList.remove('hidden');
            currentStep = step;
            
            // Generate summary for step 5
            if (step === 5) {
                generateQuizSummary();
            }
            
            // Update progress
            updateProgress();
        }

        function updateProgress() {
            const progressBar = document.getElementById('progress-bar');
            const stepText = document.getElementById('step-text');
            const progressText = document.getElementById('progress-text');
            
            const percentage = (currentStep / 5) * 100;
            progressBar.style.width = `${percentage}%`;
            stepText.textContent = stepTitles[currentStep - 1];
            progressText.textContent = `${currentStep} of 5`;
        }

        function generateQuestionForms() {
            const mcCount = parseInt(document.getElementById('mcCount').value) || 0;
            const fibCount = parseInt(document.getElementById('fibCount').value) || 0;
            const matchCount = parseInt(document.getElementById('matchCount').value) || 0;
            
            if (mcCount + fibCount + matchCount === 0) {
                alert('Please specify at least one question.');
                return;
            }
            
            const container = document.getElementById('questionForms');
            container.innerHTML = '';
            
            let questionNumber = 1;
            
            // Generate MC questions
            for (let i = 0; i < mcCount; i++) {
                container.appendChild(createMCQuestion(questionNumber++));
            }
            
            // Generate Fill in the Blank questions
            for (let i = 0; i < fibCount; i++) {
                container.appendChild(createFIBQuestion(questionNumber++));
            }
            
            // Generate Matching questions
            for (let i = 0; i < matchCount; i++) {
                container.appendChild(createMatchingQuestion(questionNumber++));
            }
            
            showStep(2);
        }

        function generatePreview() {
            const previewContainer = document.getElementById('quizPreview');
            previewContainer.innerHTML = '';
            
            let questionNumber = 1;
            
            // Get all question forms and generate preview
            const questionForms = document.getElementById('questionForms').children;
            
            for (let form of questionForms) {
                const previewElement = createQuestionPreview(form, questionNumber++);
                if (previewElement) {
                    previewContainer.appendChild(previewElement);
                }
            }
            
            if (previewContainer.children.length === 0) {
                previewContainer.innerHTML = '<p class="text-gray-500 text-center">No questions to preview. Please add questions first.</p>';
            }
            
            showStep(3);
        }

        function createQuestionPreview(form, questionNum) {
            const div = document.createElement('div');
            div.className = 'bg-gray-50 dark:bg-gray-600 rounded-lg p-4 border border-gray-200 dark:border-gray-500';
            
            const title = form.querySelector('h3').textContent;
            const questionType = title.includes('Multiple Choice') ? 'mc' : 
                                title.includes('Fill in the Blank') ? 'fib' : 'matching';
            
            if (questionType === 'mc') {
                const questionText = form.querySelector('textarea').value || `Sample multiple choice question ${questionNum}`;
                const options = Array.from(form.querySelectorAll('input[type="text"]')).map(input => input.value || `Option ${['A', 'B', 'C', 'D'][Array.from(form.querySelectorAll('input[type="text"]')).indexOf(input)]}`);
                
                div.innerHTML = `
                    <div class="mb-3">
                        <span class="text-sm font-medium text-primary">Question ${questionNum}</span>
                        <h4 class="font-medium">${questionText}</h4>
                    </div>
                    <div class="space-y-2">
                        ${options.map((option, index) => `
                            <label class="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 p-2 rounded">
                                <input type="radio" name="preview_q${questionNum}" value="${['A', 'B', 'C', 'D'][index]}" class="text-primary">
                                <span>${['A', 'B', 'C', 'D'][index]}. ${option}</span>
                            </label>
                        `).join('')}
                    </div>
                `;
            } else if (questionType === 'fib') {
                const questionText = form.querySelector('textarea').value || `Sample fill in the blank question ${questionNum}`;
                
                div.innerHTML = `
                    <div class="mb-3">
                        <span class="text-sm font-medium text-primary">Question ${questionNum}</span>
                        <h4 class="font-medium">${questionText}</h4>
                    </div>
                    <input type="text" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" placeholder="Type your answer here...">
                `;
            } else if (questionType === 'matching') {
                const instructions = form.querySelector('textarea').value || `Match the following items for question ${questionNum}`;
                const columnA = Array.from(form.querySelectorAll('textarea'))[1]?.value.split('\n').filter(item => item.trim()) || [`Item 1`, `Item 2`, `Item 3`];
                const columnB = Array.from(form.querySelectorAll('textarea'))[2]?.value.split('\n').filter(item => item.trim()) || [`Option 1`, `Option 2`, `Option 3`];
                
                div.innerHTML = `
                    <div class="mb-3">
                        <span class="text-sm font-medium text-primary">Question ${questionNum}</span>
                        <h4 class="font-medium">${instructions}</h4>
                    </div>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <h5 class="font-medium mb-2">Items to Match:</h5>
                            <div class="space-y-2">
                                ${columnA.map((item, index) => `
                                    <div class="flex items-center space-x-2">
                                        <span class="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm">${index + 1}</span>
                                        <span>${item}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div>
                            <h5 class="font-medium mb-2">Match with:</h5>
                            <div class="space-y-2">
                                ${columnB.map((item, index) => `
                                    <label class="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-500 p-2 rounded">
                                        <select class="border border-gray-300 dark:border-gray-500 rounded px-2 py-1 text-sm bg-white dark:bg-gray-700">
                                            <option value="">Select...</option>
                                            ${columnA.map((_, i) => `<option value="${i + 1}">${i + 1}</option>`).join('')}
                                        </select>
                                        <span>${item}</span>
                                    </label>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            return div;
        }

        function createMCQuestion(num) {
            const div = document.createElement('div');
            div.className = 'bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700';
            div.innerHTML = `
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-list mr-2 text-primary"></i>
                    Multiple Choice Question ${num}
                </h3>
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Question</label>
                    <textarea class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                              rows="3" placeholder="Enter your question here..."></textarea>
                </div>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">
                            <input type="radio" name="mc${num}_correct" value="A" class="mr-2">
                            Option A
                        </label>
                        <input type="text" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                               placeholder="Option A">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">
                            <input type="radio" name="mc${num}_correct" value="B" class="mr-2">
                            Option B
                        </label>
                        <input type="text" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                               placeholder="Option B">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">
                            <input type="radio" name="mc${num}_correct" value="C" class="mr-2">
                            Option C
                        </label>
                        <input type="text" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                               placeholder="Option C">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">
                            <input type="radio" name="mc${num}_correct" value="D" class="mr-2">
                            Option D
                        </label>
                        <input type="text" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                               placeholder="Option D">
                    </div>
                </div>
            `;
            return div;
        }

        function createFIBQuestion(num) {
            const div = document.createElement('div');
            div.className = 'bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700';
            div.innerHTML = `
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-edit mr-2 text-primary"></i>
                    Fill in the Blank Question ${num}
                </h3>
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Question (use _____ for blanks)</label>
                    <textarea class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                              rows="3" placeholder="The capital of France is _____."></textarea>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Correct Answer(s)</label>
                    <input type="text" class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                           placeholder="Paris">
                </div>
            `;
            return div;
        }

        function createMatchingQuestion(num) {
            const div = document.createElement('div');
            div.className = 'bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700';
            div.innerHTML = `
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-link mr-2 text-primary"></i>
                    Matching Question ${num}
                </h3>
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Instructions</label>
                    <textarea class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                              rows="2" placeholder="Match the following items..."></textarea>
                </div>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">Column A (Items to match)</label>
                        <textarea class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                                  rows="4" placeholder="Item 1&#10;Item 2&#10;Item 3&#10;Item 4"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Column B (Matching options)</label>
                        <textarea class="w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-gray-700" 
                                  rows="4" placeholder="Option 1&#10;Option 2&#10;Option 3&#10;Option 4"></textarea>
                    </div>
                </div>
            `;
            return div;
        }

        function generateQuizSummary() {
            const mcCount = parseInt(document.getElementById('mcCount').value) || 0;
            const fibCount = parseInt(document.getElementById('fibCount').value) || 0;
            const matchCount = parseInt(document.getElementById('matchCount').value) || 0;
            const courseCode = document.getElementById('courseCode').value;
            const sectionCode = document.getElementById('sectionCode').value;
            const quizTitle = document.getElementById('quizTitle').value;
            
            const totalQuestions = mcCount + fibCount + matchCount;
            
            const summaryContainer = document.getElementById('quizSummary');
            summaryContainer.innerHTML = `
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold mb-3">Quiz Details</h4>
                        <div class="space-y-2 text-sm">
                            <div><span class="font-medium">Title:</span> ${quizTitle || 'Untitled Quiz'}</div>
                            <div><span class="font-medium">Course:</span> ${courseCode || 'Not specified'}</div>
                            <div><span class="font-medium">Section:</span> ${sectionCode || 'Not specified'}</div>
                            <div><span class="font-medium">Total Questions:</span> ${totalQuestions}</div>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-3">Question Breakdown</h4>
                        <div class="space-y-2 text-sm">
                            <div><span class="font-medium">Multiple Choice:</span> ${mcCount}</div>
                            <div><span class="font-medium">Fill in the Blank:</span> ${fibCount}</div>
                            <div><span class="font-medium">Matching:</span> ${matchCount}</div>
                        </div>
                    </div>
                </div>
            `;
        }

        function deployQuiz() {
            const courseCode = document.getElementById('courseCode').value || 'DEMO101';
            const sectionCode = document.getElementById('sectionCode').value || 'A1';
            const quizTitle = document.getElementById('quizTitle').value || 'Quiz';
            
            // Generate mock Moodle link
            const quizId = Math.floor(Math.random() * 10000) + 1000;
            const moodleLink = `https://moodle.university.edu/mod/quiz/view.php?id=${quizId}&course=${courseCode.toLowerCase()}_${sectionCode.toLowerCase()}`;
            
            document.getElementById('moodleLink').href = moodleLink;
            document.getElementById('moodleLink').textContent = moodleLink;
            
            // Show success modal
            document.getElementById('successModal').classList.remove('hidden');
            document.getElementById('successModal').classList.add('flex');
        }

        function resetQuiz() {
            // Reset all forms
            document.getElementById('mcCount').value = '0';
            document.getElementById('fibCount').value = '0';
            document.getElementById('matchCount').value = '0';
            document.getElementById('courseCode').value = '';
            document.getElementById('sectionCode').value = '';
            document.getElementById('quizTitle').value = '';
            document.getElementById('questionForms').innerHTML = '';
            document.getElementById('quizPreview').innerHTML = '';
            
            // Hide modal and return to step 1
            document.getElementById('successModal').classList.add('hidden');
            document.getElementById('successModal').classList.remove('flex');
            showStep(1);
        }
    </script>


</body></html>