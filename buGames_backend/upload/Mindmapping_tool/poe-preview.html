<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mind Map Creator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                    }
                }
            },
            darkMode: 'class'
        }
    </script>
    <style>
        .mindmap-canvas {
            background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
            background-size: 20px 20px;
        }
        .dark .mindmap-canvas {
            background-image: radial-gradient(circle, #374151 1px, transparent 1px);
        }
        .node {
            cursor: move;
            transition: transform 0.2s ease;
        }
        .node:hover {
            transform: scale(1.05);
        }
        .connecting-line {
            pointer-events: none;
        }
        .template-preview {
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    </style>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <div class="flex items-center justify-between">
            <h1 class="text-2xl font-bold text-primary">Mind Map Creator</h1>
            <div class="flex gap-2">
                <button id="templateBtn" class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                    Templates
                </button>
                <button id="exportSvgBtn" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-purple-600 transition-colors">
                    Export SVG
                </button>
                <button id="exportPngBtn" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    Export PNG
                </button>
            </div>
        </div>
    </header>

    <div class="flex h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-4 overflow-y-auto">
            <h3 class="font-semibold mb-4">Components</h3>
            
            <!-- Shapes -->
            <div class="space-y-3">
                <h4 class="font-medium text-sm text-gray-600 dark:text-gray-400">Basic Shapes</h4>
                
                <div class="draggable-component" data-type="rectangle">
                    <div class="p-3 bg-primary text-white rounded-lg text-center font-semibold cursor-move">
                        Rectangle
                    </div>
                </div>
                
                <div class="draggable-component" data-type="circle">
                    <div class="w-16 h-16 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs cursor-move mx-auto">
                        Circle
                    </div>
                </div>
                
                <div class="draggable-component" data-type="diamond">
                    <div class="w-16 h-16 bg-red-500 text-white transform rotate-45 flex items-center justify-center text-xs cursor-move mx-auto">
                        <span class="transform -rotate-45">Diamond</span>
                    </div>
                </div>
                
                <div class="draggable-component" data-type="ellipse">
                    <div class="w-20 h-12 bg-green-500 text-white rounded-full flex items-center justify-center text-xs cursor-move mx-auto">
                        Ellipse
                    </div>
                </div>
                
                <div class="draggable-component" data-type="rounded-rect">
                    <div class="p-2 bg-purple-500 text-white rounded-xl text-center text-sm cursor-move">
                        Rounded
                    </div>
                </div>
                
                <h4 class="font-medium text-sm text-gray-600 dark:text-gray-400 mt-4">Predefined</h4>
                
                <div class="draggable-component" data-type="main-topic">
                    <div class="p-3 bg-indigo-600 text-white rounded-lg text-center font-semibold cursor-move">
                        Main Topic
                    </div>
                </div>
                
                <div class="draggable-component" data-type="subtopic">
                    <div class="p-2 bg-cyan-500 text-white rounded-lg text-center cursor-move">
                        Subtopic
                    </div>
                </div>
                
                <div class="draggable-component" data-type="note">
                    <div class="p-2 bg-yellow-400 text-gray-800 rounded-lg text-center text-sm cursor-move">
                        Note
                    </div>
                </div>
            </div>
            
            <div class="mt-6">
                <h4 class="font-medium text-sm text-gray-600 dark:text-gray-400 mb-2">Connections</h4>
                <div class="space-y-2">
                    <button id="connectionMode" class="w-full p-2 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors text-sm">
                        🔗 Connect Mode
                    </button>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        Click Connect Mode, then click two nodes to connect them
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="block text-sm font-medium mb-1">Arrow Style:</label>
                    <select id="arrowStyle" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-sm">
                        <option value="none">No Arrow</option>
                        <option value="end">Arrow at End</option>
                        <option value="both">Arrows at Both Ends</option>
                        <option value="start">Arrow at Start</option>
                    </select>
                </div>
                
                <div class="mt-3">
                    <label class="block text-sm font-medium mb-1">Line Style:</label>
                    <select id="lineStyle" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-sm">
                        <option value="solid">Solid</option>
                        <option value="dashed">Dashed</option>
                        <option value="dotted">Dotted</option>
                    </select>
                </div>
            </div>
            
            <div class="mt-6">
                <h4 class="font-semibold mb-2">Actions</h4>
                <button id="clearCanvas" class="w-full p-2 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors">
                    Clear Canvas
                </button>
            </div>
        </aside>

        <!-- Main Canvas Area -->
        <main class="flex-1 relative overflow-hidden">
            <div id="canvas" class="w-full h-full mindmap-canvas relative cursor-crosshair">
                <svg id="connections" class="absolute inset-0 w-full h-full pointer-events-none z-10">
                </svg>
                <!-- Instruction overlay -->
                <div id="instructionText" class="absolute top-4 left-4 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-4 py-2 rounded-lg shadow-lg z-30 hidden">
                    Click anywhere on the canvas to place the shape
                </div>
                <!-- Credit statement -->
                <div class="absolute bottom-4 right-4 bg-white dark:bg-gray-800 bg-opacity-90 dark:bg-opacity-90 text-gray-600 dark:text-gray-400 text-xs px-3 py-2 rounded-lg shadow-lg z-30 max-w-sm">
                    <div class="font-medium">Created by Dr Simon Wang</div>
                    <div>Lecturer in English and Innovation Officer</div>
                    <div>Language Centre, HKBU</div>
                    <div class="mt-1">
                        <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Template Modal -->
    <div id="templateModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-96 overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">Choose a Template</h2>
                    <button id="closeTemplateModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="templateGrid">
                    <!-- Templates will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Node Modal -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full">
            <div class="p-6">
                <h2 class="text-xl font-bold mb-4">Edit Node</h2>
                <input type="text" id="nodeText" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-base" placeholder="Enter text...">
                <div class="flex gap-2 mt-4">
                    <button id="saveNode" class="flex-1 p-2 bg-primary text-white rounded-lg hover:bg-purple-600 transition-colors">
                        Save
                    </button>
                    <button id="deleteNode" class="flex-1 p-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        Delete
                    </button>
                    <button id="cancelEdit" class="flex-1 p-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode setup
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        class MindMapApp {
            constructor() {
                this.canvas = document.getElementById('canvas');
                this.connectionsLayer = document.getElementById('connections');
                this.nodes = [];
                this.connections = [];
                this.selectedNode = null;
                this.dragOffset = { x: 0, y: 0 };
                this.nodeCounter = 0;
                this.isConnectionMode = false;
                this.firstConnectionNode = null;
                this.isDragging = false;
                
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupDragAndDrop();
                this.createTemplates();
                this.setupSVGMarkers();
            }

            setupEventListeners() {
                // Template modal
                document.getElementById('templateBtn').addEventListener('click', () => {
                    document.getElementById('templateModal').classList.remove('hidden');
                });
                
                document.getElementById('closeTemplateModal').addEventListener('click', () => {
                    document.getElementById('templateModal').classList.add('hidden');
                });

                // Export buttons
                document.getElementById('exportSvgBtn').addEventListener('click', () => this.exportSVG());
                document.getElementById('exportPngBtn').addEventListener('click', () => this.exportPNG());
                
                // Clear canvas
                document.getElementById('clearCanvas').addEventListener('click', () => this.clearCanvas());

                // Edit modal
                document.getElementById('saveNode').addEventListener('click', () => this.saveNodeEdit());
                document.getElementById('deleteNode').addEventListener('click', () => this.deleteNode());
                document.getElementById('cancelEdit').addEventListener('click', () => this.closeEditModal());

                // Connection mode
                document.getElementById('connectionMode').addEventListener('click', () => this.toggleConnectionMode());

                // Arrow and line style changes
                document.getElementById('arrowStyle').addEventListener('change', () => this.updateConnections());
                document.getElementById('lineStyle').addEventListener('change', () => this.updateConnections());

                // Canvas click for creating connections
                this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
            }

            setupDragAndDrop() {
                let selectedComponentType = null;
                
                // Add click-to-select functionality as primary method
                document.querySelectorAll('.draggable-component').forEach(component => {
                    // Add click handler for selection
                    component.addEventListener('click', (e) => {
                        e.preventDefault();
                        
                        // Remove previous selection
                        document.querySelectorAll('.draggable-component').forEach(c => {
                            c.style.outline = '';
                        });
                        
                        // Select this component
                        selectedComponentType = component.dataset.type;
                        component.style.outline = '3px solid #10B981';
                        
                        // Update instructions
                        document.getElementById('instructionText').textContent = 'Click anywhere on the canvas to place the shape';
                        document.getElementById('instructionText').style.display = 'block';
                    });
                    
                    // Also keep drag and drop as secondary method
                    component.draggable = true;
                    component.addEventListener('dragstart', (e) => {
                        console.log('Drag started:', component.dataset.type);
                        e.dataTransfer.setData('application/x-shape-type', component.dataset.type);
                        e.dataTransfer.effectAllowed = 'copy';
                        selectedComponentType = component.dataset.type;
                    });
                });

                // Canvas click handler for placing selected shapes
                this.canvas.addEventListener('click', (e) => {
                    if (selectedComponentType && !this.isConnectionMode) {
                        const rect = this.canvas.getBoundingClientRect();
                        const x = e.clientX - rect.left - 50;
                        const y = e.clientY - rect.top - 25;
                        
                        console.log('Placing shape:', selectedComponentType, 'at', x, y);
                        this.createNode(selectedComponentType, x, y);
                        
                        // Clear selection
                        selectedComponentType = null;
                        document.querySelectorAll('.draggable-component').forEach(c => {
                            c.style.outline = '';
                        });
                        document.getElementById('instructionText').style.display = 'none';
                    }
                });

                // Simplified drag and drop
                this.canvas.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'copy';
                    this.canvas.style.backgroundColor = 'rgba(93, 92, 222, 0.1)';
                });

                this.canvas.addEventListener('dragleave', (e) => {
                    this.canvas.style.backgroundColor = '';
                });

                this.canvas.addEventListener('drop', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    this.canvas.style.backgroundColor = '';
                    
                    const nodeType = e.dataTransfer.getData('application/x-shape-type') || 
                                   e.dataTransfer.getData('text/plain');
                    
                    console.log('Drop event fired, nodeType:', nodeType);
                    
                    if (nodeType) {
                        const rect = this.canvas.getBoundingClientRect();
                        const x = e.clientX - rect.left - 50;
                        const y = e.clientY - rect.top - 25;
                        
                        console.log('Creating node from drop at:', x, y);
                        this.createNode(nodeType, x, y);
                    } else {
                        console.log('No nodeType found in drop event');
                    }
                });
            }

            createNode(type, x, y, text = null) {
                const nodeId = `node-${++this.nodeCounter}`;
                const node = document.createElement('div');
                node.id = nodeId;
                node.className = 'node absolute z-20';
                node.style.left = `${x}px`;
                node.style.top = `${y}px`;

                let nodeText = text || this.getDefaultText(type);
                let shapeHtml = this.getShapeHtml(type, nodeText);
                
                node.innerHTML = shapeHtml;

                // Add event listeners
                node.addEventListener('mousedown', (e) => this.startDrag(e, node));
                node.addEventListener('dblclick', (e) => {
                    e.stopPropagation();
                    this.editNode(node);
                });
                node.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.handleNodeClick(node);
                });
                
                this.canvas.appendChild(node);
                this.nodes.push({
                    id: nodeId,
                    element: node,
                    type: type,
                    text: nodeText,
                    x: x,
                    y: y
                });

                return node;
            }

            getDefaultText(type) {
                const defaults = {
                    'rectangle': 'Rectangle',
                    'circle': 'Circle',
                    'diamond': 'Diamond',
                    'ellipse': 'Ellipse',
                    'rounded-rect': 'Rounded',
                    'main-topic': 'Main Topic',
                    'subtopic': 'Subtopic',
                    'note': 'Note'
                };
                return defaults[type] || 'Text';
            }

            getShapeHtml(type, text) {
                const shapes = {
                    'rectangle': `<div class="p-3 bg-primary text-white rounded-lg shadow-lg min-w-24 text-center cursor-move select-none">${text}</div>`,
                    'circle': `<div class="w-20 h-20 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs cursor-move select-none shadow-lg">${text}</div>`,
                    'diamond': `<div class="w-20 h-20 bg-red-500 text-white transform rotate-45 flex items-center justify-center cursor-move select-none shadow-lg"><span class="transform -rotate-45 text-xs">${text}</span></div>`,
                    'ellipse': `<div class="w-24 h-16 bg-green-500 text-white rounded-full flex items-center justify-center text-xs cursor-move select-none shadow-lg">${text}</div>`,
                    'rounded-rect': `<div class="p-3 bg-purple-500 text-white rounded-xl shadow-lg min-w-24 text-center cursor-move select-none text-sm">${text}</div>`,
                    'main-topic': `<div class="p-3 bg-indigo-600 text-white rounded-lg shadow-lg min-w-24 text-center cursor-move select-none font-semibold">${text}</div>`,
                    'subtopic': `<div class="p-2 bg-cyan-500 text-white rounded-lg shadow-lg min-w-20 text-center cursor-move select-none">${text}</div>`,
                    'note': `<div class="p-2 bg-yellow-400 text-gray-800 rounded-lg shadow-lg min-w-20 text-center cursor-move select-none text-sm">${text}</div>`
                };
                return shapes[type] || `<div class="p-3 bg-gray-500 text-white rounded-lg shadow-lg min-w-24 text-center cursor-move select-none">${text}</div>`;
            }

            setupSVGMarkers() {
                const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
                
                // End arrow marker
                const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
                marker.setAttribute('id', 'arrowhead');
                marker.setAttribute('markerWidth', '10');
                marker.setAttribute('markerHeight', '7');
                marker.setAttribute('refX', '10');
                marker.setAttribute('refY', '3.5');
                marker.setAttribute('orient', 'auto');
                
                const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
                polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
                polygon.setAttribute('fill', '#5D5CDE');
                
                marker.appendChild(polygon);
                defs.appendChild(marker);
                
                // Start arrow marker
                const markerStart = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
                markerStart.setAttribute('id', 'arrowhead-start');
                markerStart.setAttribute('markerWidth', '10');
                markerStart.setAttribute('markerHeight', '7');
                markerStart.setAttribute('refX', '0');
                markerStart.setAttribute('refY', '3.5');
                markerStart.setAttribute('orient', 'auto');
                
                const polygonStart = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
                polygonStart.setAttribute('points', '10 0, 0 3.5, 10 7');
                polygonStart.setAttribute('fill', '#5D5CDE');
                
                markerStart.appendChild(polygonStart);
                defs.appendChild(markerStart);
                
                this.connectionsLayer.appendChild(defs);
            }

            toggleConnectionMode() {
                this.isConnectionMode = !this.isConnectionMode;
                const btn = document.getElementById('connectionMode');
                
                if (this.isConnectionMode) {
                    btn.textContent = '🔗 Exit Connect Mode';
                    btn.classList.remove('bg-blue-100', 'dark:bg-blue-900', 'text-blue-700', 'dark:text-blue-300');
                    btn.classList.add('bg-green-100', 'dark:bg-green-900', 'text-green-700', 'dark:text-green-300');
                    this.canvas.style.cursor = 'crosshair';
                    this.firstConnectionNode = null;
                } else {
                    btn.textContent = '🔗 Connect Mode';
                    btn.classList.remove('bg-green-100', 'dark:bg-green-900', 'text-green-700', 'dark:text-green-300');
                    btn.classList.add('bg-blue-100', 'dark:bg-blue-900', 'text-blue-700', 'dark:text-blue-300');
                    this.canvas.style.cursor = 'default';
                    this.firstConnectionNode = null;
                }
            }

            handleNodeClick(node) {
                if (!this.isConnectionMode) return;
                
                if (!this.firstConnectionNode) {
                    this.firstConnectionNode = node;
                    node.style.outline = '3px solid #10B981';
                } else if (this.firstConnectionNode !== node) {
                    // Create connection
                    this.createConnection(this.firstConnectionNode.id, node.id);
                    
                    // Reset
                    this.firstConnectionNode.style.outline = '';
                    this.firstConnectionNode = null;
                } else {
                    // Same node clicked, deselect
                    this.firstConnectionNode.style.outline = '';
                    this.firstConnectionNode = null;
                }
            }

            startDrag(e, node) {
                e.preventDefault();
                this.selectedNode = node;
                const rect = node.getBoundingClientRect();
                const canvasRect = this.canvas.getBoundingClientRect();
                this.dragOffset.x = e.clientX - rect.left;
                this.dragOffset.y = e.clientY - rect.top;

                const handleMouseMove = (e) => {
                    const x = e.clientX - canvasRect.left - this.dragOffset.x;
                    const y = e.clientY - canvasRect.top - this.dragOffset.y;
                    
                    node.style.left = `${x}px`;
                    node.style.top = `${y}px`;
                    
                    // Update node position in data
                    const nodeData = this.nodes.find(n => n.id === node.id);
                    if (nodeData) {
                        nodeData.x = x;
                        nodeData.y = y;
                    }
                    
                    this.updateConnections();
                };

                const handleMouseUp = () => {
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                    this.selectedNode = null;
                };

                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
            }

            editNode(node) {
                const nodeData = this.nodes.find(n => n.id === node.id);
                if (!nodeData) return;

                document.getElementById('nodeText').value = nodeData.text;
                document.getElementById('editModal').classList.remove('hidden');
                document.getElementById('nodeText').focus();
                this.selectedNode = node;
            }

            saveNodeEdit() {
                if (!this.selectedNode) return;
                
                const newText = document.getElementById('nodeText').value.trim();
                if (newText) {
                    const nodeData = this.nodes.find(n => n.id === this.selectedNode.id);
                    if (nodeData) {
                        nodeData.text = newText;
                        const textElement = this.selectedNode.querySelector('div');
                        textElement.textContent = newText;
                    }
                }
                this.closeEditModal();
            }

            deleteNode() {
                if (!this.selectedNode) return;
                
                // Remove connections
                this.connections = this.connections.filter(conn => 
                    conn.from !== this.selectedNode.id && conn.to !== this.selectedNode.id
                );
                
                // Remove from nodes array
                this.nodes = this.nodes.filter(n => n.id !== this.selectedNode.id);
                
                // Remove from DOM
                this.selectedNode.remove();
                this.updateConnections();
                this.closeEditModal();
            }

            closeEditModal() {
                document.getElementById('editModal').classList.add('hidden');
                this.selectedNode = null;
            }

            handleCanvasClick(e) {
                if (e.target === this.canvas) {
                    // Clicked on empty canvas
                    this.selectedNode = null;
                }
            }

            createConnection(fromNodeId, toNodeId) {
                const connection = { from: fromNodeId, to: toNodeId };
                this.connections.push(connection);
                this.updateConnections();
            }

            updateConnections() {
                // Re-create defs with markers
                this.connectionsLayer.innerHTML = '';
                this.setupSVGMarkers();
                
                this.connections.forEach(conn => {
                    const fromNode = this.nodes.find(n => n.id === conn.from);
                    const toNode = this.nodes.find(n => n.id === conn.to);
                    
                    if (fromNode && toNode) {
                        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                        line.setAttribute('x1', fromNode.x + 50);
                        line.setAttribute('y1', fromNode.y + 25);
                        line.setAttribute('x2', toNode.x + 50);
                        line.setAttribute('y2', toNode.y + 25);
                        line.setAttribute('stroke', '#5D5CDE');
                        line.setAttribute('stroke-width', '2');
                        line.setAttribute('class', 'connecting-line');
                        
                        // Apply arrow style
                        const arrowStyle = document.getElementById('arrowStyle').value;
                        switch(arrowStyle) {
                            case 'end':
                                line.setAttribute('marker-end', 'url(#arrowhead)');
                                break;
                            case 'start':
                                line.setAttribute('marker-start', 'url(#arrowhead-start)');
                                break;
                            case 'both':
                                line.setAttribute('marker-end', 'url(#arrowhead)');
                                line.setAttribute('marker-start', 'url(#arrowhead-start)');
                                break;
                        }
                        
                        // Apply line style
                        const lineStyle = document.getElementById('lineStyle').value;
                        switch(lineStyle) {
                            case 'dashed':
                                line.setAttribute('stroke-dasharray', '5,5');
                                break;
                            case 'dotted':
                                line.setAttribute('stroke-dasharray', '2,2');
                                break;
                        }
                        
                        this.connectionsLayer.appendChild(line);
                    }
                });
            }

            createTemplates() {
                const templates = [
                    {
                        name: 'Project Planning',
                        nodes: [
                            { type: 'main-topic', x: 400, y: 200, text: 'Project Plan' },
                            { type: 'subtopic', x: 200, y: 100, text: 'Research' },
                            { type: 'subtopic', x: 600, y: 100, text: 'Development' },
                            { type: 'subtopic', x: 200, y: 300, text: 'Testing' },
                            { type: 'subtopic', x: 600, y: 300, text: 'Launch' }
                        ]
                    },
                    {
                        name: 'Business Strategy',
                        nodes: [
                            { type: 'main-topic', x: 400, y: 200, text: 'Business Strategy' },
                            { type: 'subtopic', x: 200, y: 100, text: 'Market Analysis' },
                            { type: 'subtopic', x: 600, y: 100, text: 'Competitors' },
                            { type: 'idea', x: 100, y: 50, text: 'Target Audience' },
                            { type: 'idea', x: 300, y: 50, text: 'Market Size' }
                        ]
                    },
                    {
                        name: 'Learning Path',
                        nodes: [
                            { type: 'main-topic', x: 400, y: 200, text: 'Web Development' },
                            { type: 'subtopic', x: 200, y: 100, text: 'Frontend' },
                            { type: 'subtopic', x: 600, y: 100, text: 'Backend' },
                            { type: 'idea', x: 100, y: 50, text: 'HTML/CSS' },
                            { type: 'idea', x: 300, y: 50, text: 'JavaScript' },
                            { type: 'idea', x: 500, y: 50, text: 'Node.js' },
                            { type: 'idea', x: 700, y: 50, text: 'Database' }
                        ]
                    }
                ];

                const templateGrid = document.getElementById('templateGrid');
                templates.forEach(template => {
                    const templateDiv = document.createElement('div');
                    templateDiv.className = 'p-4 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors';
                    templateDiv.innerHTML = `
                        <h3 class="font-semibold mb-2">${template.name}</h3>
                        <div class="text-sm text-gray-600 dark:text-gray-400">${template.nodes.length} nodes</div>
                    `;
                    
                    templateDiv.addEventListener('click', () => {
                        this.loadTemplate(template);
                        document.getElementById('templateModal').classList.add('hidden');
                    });
                    
                    templateGrid.appendChild(templateDiv);
                });
            }

            loadTemplate(template) {
                this.clearCanvas();
                template.nodes.forEach(nodeData => {
                    this.createNode(nodeData.type, nodeData.x, nodeData.y, nodeData.text);
                });
            }

            clearCanvas() {
                this.nodes = [];
                this.connections = [];
                this.canvas.querySelectorAll('.node').forEach(node => node.remove());
                this.connectionsLayer.innerHTML = '';
            }

            exportSVG() {
                const svgContent = this.generateSVG();
                const blob = new Blob([svgContent], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'mindmap.svg';
                a.click();
                URL.revokeObjectURL(url);
            }

            generateSVG() {
                const canvasRect = this.canvas.getBoundingClientRect();
                let svgContent = `<svg xmlns="http://www.w3.org/2000/svg" width="${canvasRect.width}" height="${canvasRect.height}" viewBox="0 0 ${canvasRect.width} ${canvasRect.height}">`;
                
                // Add connections
                this.connections.forEach(conn => {
                    const fromNode = this.nodes.find(n => n.id === conn.from);
                    const toNode = this.nodes.find(n => n.id === conn.to);
                    
                    if (fromNode && toNode) {
                        svgContent += `<line x1="${fromNode.x + 50}" y1="${fromNode.y + 25}" x2="${toNode.x + 50}" y2="${toNode.y + 25}" stroke="#5D5CDE" stroke-width="2"/>`;
                    }
                });
                
                // Add nodes
                this.nodes.forEach(node => {
                    const rect = node.element.getBoundingClientRect();
                    const canvasRect = this.canvas.getBoundingClientRect();
                    
                    svgContent += `<rect x="${node.x}" y="${node.y}" width="100" height="50" fill="#5D5CDE" rx="8"/>`;
                    svgContent += `<text x="${node.x + 50}" y="${node.y + 30}" text-anchor="middle" fill="white" font-family="Arial" font-size="14">${node.text}</text>`;
                });
                
                svgContent += '</svg>';
                return svgContent;
            }

            exportPNG() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const canvasRect = this.canvas.getBoundingClientRect();
                
                canvas.width = canvasRect.width;
                canvas.height = canvasRect.height;
                
                // White background
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // Draw connections
                ctx.strokeStyle = '#5D5CDE';
                ctx.lineWidth = 2;
                this.connections.forEach(conn => {
                    const fromNode = this.nodes.find(n => n.id === conn.from);
                    const toNode = this.nodes.find(n => n.id === conn.to);
                    
                    if (fromNode && toNode) {
                        ctx.beginPath();
                        ctx.moveTo(fromNode.x + 50, fromNode.y + 25);
                        ctx.lineTo(toNode.x + 50, toNode.y + 25);
                        ctx.stroke();
                    }
                });
                
                // Draw nodes
                ctx.fillStyle = '#5D5CDE';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                this.nodes.forEach(node => {
                    // Draw rectangle
                    ctx.fillStyle = '#5D5CDE';
                    ctx.fillRect(node.x, node.y, 100, 50);
                    
                    // Draw text
                    ctx.fillStyle = 'white';
                    ctx.fillText(node.text, node.x + 50, node.y + 25);
                });
                
                // Download
                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'mindmap.png';
                    a.click();
                    URL.revokeObjectURL(url);
                });
            }
        }

        // Initialize the app
        const mindMapApp = new MindMapApp();
    </script>


</body></html>