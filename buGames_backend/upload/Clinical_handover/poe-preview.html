<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Handover Training Simulator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'primary-dark': '#4A49C7'
                    }
                }
            }
        }
    </script>
    <style>
        .isbar-annotation {
            border-left: 4px solid;
            padding-left: 12px;
            margin: 8px 0;
        }
        .introduction { border-color: #ef4444; background: #fef2f2; }
        .situation { border-color: #f97316; background: #fff7ed; }
        .background { border-color: #eab308; background: #fefce8; }
        .assessment { border-color: #22c55e; background: #f0fdf4; }
        .recommendation { border-color: #3b82f6; background: #eff6ff; }
        .dark .introduction { background: #7f1d1d; }
        .dark .situation { background: #9a3412; }
        .dark .background { background: #854d0e; }
        .dark .assessment { background: #166534; }
        .dark .recommendation { background: #1e40af; }
    </style>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-6 max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">Clinical Handover Training Simulator</h1>
            <p class="text-gray-600 dark:text-gray-400">Practice structured communication using the ISBAR framework</p>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Panel: Case Selection & Guidelines -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Case Selection -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">Select a Case</h2>
                    <div id="caseList" class="space-y-2">
                        <!-- Cases will be populated here -->
                    </div>
                </div>

                <!-- ISBAR Guidelines -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">ISBAR Framework</h2>
                    <div class="space-y-3 text-sm">
                        <div class="isbar-annotation introduction">
                            <strong>Introduction:</strong> Name, unit, role, patient name, purpose
                        </div>
                        <div class="isbar-annotation situation">
                            <strong>Situation:</strong> Concise statement of the problem
                        </div>
                        <div class="isbar-annotation background">
                            <strong>Background:</strong> Pertinent information about the situation
                        </div>
                        <div class="isbar-annotation assessment">
                            <strong>Assessment:</strong> Analysis and considerations
                        </div>
                        <div class="isbar-annotation recommendation">
                            <strong>Recommendation:</strong> Request or recommended action
                        </div>
                    </div>
                </div>

                <!-- Role Selection -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">Your Role</h2>
                    <select id="roleSelect" class="w-full p-3 border rounded-lg text-base bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                        <option value="resident">Resident Doctor</option>
                        <option value="nurse">Nurse</option>
                        <option value="emergency_doctor">Emergency Doctor</option>
                    </select>
                </div>
            </div>

            <!-- Center Panel: Chat Interface -->
            <div class="lg:col-span-2">
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 h-full flex flex-col">
                    <!-- Case Info -->
                    <div id="caseInfo" class="mb-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg hidden">
                        <h3 class="font-semibold mb-2">Case Details</h3>
                        <div id="caseDetails" class="text-sm"></div>
                    </div>

                    <!-- Chat Messages -->
                    <div id="chatContainer" class="flex-1 overflow-y-auto mb-4 min-h-96">
                        <div id="chatMessages" class="space-y-4">
                            <div class="text-center text-gray-500 py-8">
                                Select a case to begin the simulation
                            </div>
                        </div>
                    </div>

                    <!-- Control Area -->
                    <div class="space-y-3">
                        <div class="flex gap-2 flex-wrap">
                            <button id="playConversationBtn" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors text-sm disabled:opacity-50" disabled="">
                                ▶ Play Conversation
                            </button>
                            <button id="pauseBtn" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm hidden">
                                ⏸ Pause
                            </button>
                            <button id="resetBtn" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm disabled:opacity-50" disabled="">
                                🔄 Reset
                            </button>
                            <select id="studentSelect" class="px-3 py-2 border rounded-lg text-sm bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 disabled:opacity-50" disabled="">
                                <option value="good">Good Student Example</option>
                                <option value="needs_improvement">Needs Improvement</option>
                                <option value="poor">Poor Communication</option>
                            </select>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            Select a case and student example to view simulated avatar conversation with pedagogical analysis
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ISBAR Analysis Panel -->
        <div id="analysisPanel" class="mt-6 bg-gray-50 dark:bg-gray-800 rounded-lg p-6 hidden">
            <h2 class="text-xl font-semibold mb-4">ISBAR Analysis</h2>
            <div id="analysisContent" class="space-y-4">
                <!-- Analysis will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Clinical cases data
        const clinicalCases = {
            case1: {
                title: "70-year-old Male with Chest Pain",
                description: "History of coronary artery disease, currently on medication for hypertension and angina. ECG shows ST-segment depression, elevated troponin levels. Family history of heart disease. Needs immediate transfer to cardiac catheterisation lab.",
                rolePlay: "Emergency department doctor to cardiologist",
                recipient: "Cardiologist",
                urgency: "Urgent",
                details: {
                    vitals: "BP 140/90, HR 92, Temp 37.2°C, O2 Sat 96%",
                    medications: "Metoprolol, Aspirin, Atorvastatin",
                    allergies: "NKDA"
                }
            },
            case2: {
                title: "25-year-old Female with Multiple Trauma",
                description: "Motor vehicle accident, currently unconscious with GCS score of 3. CT scan shows multiple fractures and internal bleeding. Hypotensive and tachycardic. Open fracture in left leg. Needs urgent transfer to operating room.",
                rolePlay: "Emergency department nurse to surgeon",
                recipient: "Surgeon",
                urgency: "Critical",
                details: {
                    vitals: "BP 80/45, HR 130, Temp 36.5°C, O2 Sat 88%",
                    injuries: "Multiple rib fractures, splenic laceration, open tibia fracture",
                    treatments: "IV fluids, pain management, blood transfusion ordered"
                }
            },
            case3: {
                title: "68-year-old Male with COPD Exacerbation",
                description: "History of chronic obstructive pulmonary disease. Currently on inhaled bronchodilators and corticosteroids. Experiencing severe dyspnea and hypoxia. Requires supplemental oxygen. Needs transfer to intensive care unit.",
                rolePlay: "Respiratory therapist to emergency department doctor",
                recipient: "ICU Doctor",
                urgency: "Urgent",
                details: {
                    vitals: "BP 150/85, HR 110, Temp 38.1°C, O2 Sat 85% on room air",
                    medications: "Salbutamol, Prednisolone",
                    history: "Smoker for 40 years, previous ICU admissions"
                }
            }
        };

        let currentCase = null;
        let conversationHistory = [];
        let currentRole = 'resident';

        // Initialize the application
        function init() {
            populateCaseList();
            setupEventListeners();
        }

        function populateCaseList() {
            const caseList = document.getElementById('caseList');
            caseList.innerHTML = '';

            Object.keys(clinicalCases).forEach(caseId => {
                const caseData = clinicalCases[caseId];
                const caseItem = document.createElement('div');
                caseItem.className = 'p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors';
                caseItem.innerHTML = `
                    <h3 class="font-medium text-sm mb-1">${caseData.title}</h3>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">${caseData.description.substring(0, 80)}...</p>
                    <span class="inline-block px-2 py-1 text-xs rounded-full ${
                        caseData.urgency === 'Critical' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                        caseData.urgency === 'Urgent' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    }">${caseData.urgency}</span>
                `;
                caseItem.onclick = () => selectCase(caseId);
                caseList.appendChild(caseItem);
            });
        }

        function selectCase(caseId) {
            currentCase = caseId;
            const caseData = clinicalCases[caseId];
            
            // Update case info
            const caseInfo = document.getElementById('caseInfo');
            const caseDetails = document.getElementById('caseDetails');
            caseInfo.classList.remove('hidden');
            caseDetails.innerHTML = `
                <strong>${caseData.title}</strong><br>
                <em>Role Play:</em> ${caseData.rolePlay}<br>
                <em>Urgency:</em> ${caseData.urgency}<br>
                <em>Description:</em> ${caseData.description}
            `;

            // Enable controls
            document.getElementById('playConversationBtn').disabled = false;
            document.getElementById('resetBtn').disabled = false;
            document.getElementById('studentSelect').disabled = false;

            // Reset conversation
            resetConversation();
            
            // Show instructions
            addSystemMessage(`Avatar Training Simulation: ${caseData.title}. Select a student performance level and click "Play Conversation" to view recorded interaction.`);
        }

        function setupEventListeners() {
            const roleSelect = document.getElementById('roleSelect');
            const playBtn = document.getElementById('playConversationBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const resetBtn = document.getElementById('resetBtn');
            const studentSelect = document.getElementById('studentSelect');

            roleSelect.onchange = (e) => {
                currentRole = e.target.value;
            };

            playBtn.onclick = playSimulatedConversation;
            pauseBtn.onclick = pauseConversation;
            resetBtn.onclick = resetSimulation;
            studentSelect.onchange = resetSimulation;
        }

        function addMessage(content, isUser = false, annotation = null) {
            const chatMessages = document.getElementById('chatMessages');
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded-lg ${isUser ? 'bg-primary text-white ml-8' : 'bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 mr-8'}`;
            
            let messageHTML = `<div class="mb-2">${marked.parse(content)}</div>`;
            
            if (annotation) {
                messageHTML += `<div class="isbar-annotation ${annotation.type} text-xs mt-2">
                    <strong>${annotation.type.toUpperCase()}:</strong> ${annotation.note}
                </div>`;
            }

            messageDiv.innerHTML = messageHTML;
            chatMessages.appendChild(messageDiv);
            
            // Scroll to bottom
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function addSystemMessage(content) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = ''; // Clear previous messages
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg text-center';
            messageDiv.innerHTML = `<div class="text-blue-800 dark:text-blue-200">${content}</div>`;
            chatMessages.appendChild(messageDiv);
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || !currentCase) return;

            // Add user message
            addMessage(message, true);
            conversationHistory.push({ role: 'user', content: message });
            
            // Clear input
            messageInput.value = '';
            
            // Analyze message for ISBAR components
            analyzeMessage(message);
            
            // Send to bot for response
            getBotResponse(message);
        }

        function analyzeMessage(message) {
            const lowerMessage = message.toLowerCase();
            let annotation = null;

            // Simple ISBAR detection based on keywords and structure
            if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('my name') || lowerMessage.includes('calling from')) {
                annotation = { type: 'introduction', note: 'Good introduction with identification' };
            } else if (lowerMessage.includes('patient') && (lowerMessage.includes('has') || lowerMessage.includes('presenting'))) {
                annotation = { type: 'situation', note: 'Clear situation statement' };
            } else if (lowerMessage.includes('history') || lowerMessage.includes('background') || lowerMessage.includes('medical')) {
                annotation = { type: 'background', note: 'Relevant background information provided' };
            } else if (lowerMessage.includes('assessment') || lowerMessage.includes('think') || lowerMessage.includes('appears')) {
                annotation = { type: 'assessment', note: 'Clinical assessment shared' };
            } else if (lowerMessage.includes('recommend') || lowerMessage.includes('need') || lowerMessage.includes('request')) {
                annotation = { type: 'recommendation', note: 'Clear recommendation made' };
            }

            if (annotation) {
                // Update the last message with annotation
                const messages = document.getElementById('chatMessages').children;
                const lastMessage = messages[messages.length - 1];
                lastMessage.innerHTML += `<div class="isbar-annotation ${annotation.type} text-xs mt-2">
                    <strong>${annotation.type.toUpperCase()}:</strong> ${annotation.note}
                </div>`;
            }
        }

        function getBotResponse(userMessage) {
            const caseData = clinicalCases[currentCase];
            const conversationStage = getConversationStage();
            
            // Simulate typing delay
            setTimeout(() => {
                const response = generateContextualResponse(userMessage, conversationStage, caseData);
                addMessage(response.content, false, response.pedagogicalNote);
                conversationHistory.push({ role: 'assistant', content: response.content });
                
                // Provide pedagogical feedback
                providePedagogicalFeedback(userMessage, conversationStage);
            }, 1000 + Math.random() * 1000); // 1-2 second delay
        }

        function generateContextualResponse(userMessage, stage, caseData) {
            const responses = getResponseDatabase();
            const caseResponses = responses[currentCase] || responses.default;
            
            // Analyze user message for keywords and structure
            const messageAnalysis = analyzeUserMessage(userMessage);
            
            // Select appropriate response based on stage and content
            let response = selectBestResponse(caseResponses, stage, messageAnalysis);
            
            return {
                content: response.content,
                pedagogicalNote: response.pedagogicalNote
            };
        }

        function getResponseDatabase() {
            return {
                case1: {
                    introduction: [
                        {
                            content: "Thank you for calling. This is Dr. Matthews, the cardiologist on call. I understand you have a patient for me?",
                            triggers: ["hello", "hi", "calling", "name"],
                            pedagogicalNote: { type: "positive", note: "Recipient appropriately identifies themselves and shows readiness to receive handover" }
                        },
                        {
                            content: "Good morning, this is the cardiology department. How can I help you?",
                            triggers: ["general"],
                            pedagogicalNote: { type: "neutral", note: "Professional greeting, waiting for proper introduction" }
                        }
                    ],
                    situation: [
                        {
                            content: "I see, chest pain in a 70-year-old. What's his current status? Is he stable?",
                            triggers: ["chest pain", "70", "male", "patient"],
                            pedagogicalNote: { type: "positive", note: "Appropriate clarifying question about patient stability" }
                        },
                        {
                            content: "Okay, can you give me more details about the presentation?",
                            triggers: ["situation", "presenting"],
                            pedagogicalNote: { type: "neutral", note: "Seeking more specific information" }
                        }
                    ],
                    background: [
                        {
                            content: "Right, so he has a cardiac history. What about his current medications? Any recent changes?",
                            triggers: ["history", "coronary", "medication"],
                            pedagogicalNote: { type: "positive", note: "Following up on relevant background information" }
                        },
                        {
                            content: "That's important background. What do the current investigations show?",
                            triggers: ["background", "history"],
                            pedagogicalNote: { type: "positive", note: "Transitioning appropriately to current clinical findings" }
                        }
                    ],
                    assessment: [
                        {
                            content: "Based on what you're telling me, this does sound like an acute coronary syndrome. What's your assessment?",
                            triggers: ["ecg", "troponin", "elevated"],
                            pedagogicalNote: { type: "positive", note: "Confirming clinical impression and seeking student's assessment" }
                        },
                        {
                            content: "Those findings are concerning. What do you think we should do?",
                            triggers: ["assessment", "think"],
                            pedagogicalNote: { type: "positive", note: "Encouraging student to provide recommendation" }
                        }
                    ],
                    recommendation: [
                        {
                            content: "I agree. We'll accept the patient for urgent catheterization. Please ensure he's stable for transport and has continuous monitoring.",
                            triggers: ["recommend", "transfer", "catheter"],
                            pedagogicalNote: { type: "positive", note: "Accepting recommendation and providing clear instructions" }
                        },
                        {
                            content: "Yes, that sounds appropriate. What's the timeframe you're thinking?",
                            triggers: ["urgent", "immediate"],
                            pedagogicalNote: { type: "positive", note: "Confirming urgency and planning logistics" }
                        }
                    ]
                },
                default: {
                    general: [
                        {
                            content: "I'm ready to receive your handover. Please go ahead.",
                            triggers: ["general"],
                            pedagogicalNote: { type: "neutral", note: "Standard opening response" }
                        },
                        {
                            content: "Can you repeat that last part? I want to make sure I have all the details.",
                            triggers: ["unclear"],
                            pedagogicalNote: { type: "positive", note: "Seeking clarification - good practice" }
                        },
                        {
                            content: "Thank you for that information. What's your main concern?",
                            triggers: ["information"],
                            pedagogicalNote: { type: "positive", note: "Acknowledging information and focusing discussion" }
                        }
                    ]
                }
            };
        }

        function selectBestResponse(caseResponses, stage, messageAnalysis) {
            let candidateResponses = [];
            
            // Try to match stage-specific responses first
            if (caseResponses[stage]) {
                candidateResponses = caseResponses[stage];
            } else if (caseResponses.general) {
                candidateResponses = caseResponses.general;
            } else {
                // Fallback to default responses
                candidateResponses = getResponseDatabase().default.general;
            }
            
            // Find best matching response based on triggers
            let bestMatch = candidateResponses[0];
            let bestScore = 0;
            
            candidateResponses.forEach(response => {
                let score = 0;
                response.triggers.forEach(trigger => {
                    if (messageAnalysis.keywords.includes(trigger)) {
                        score++;
                    }
                });
                
                if (score > bestScore) {
                    bestScore = score;
                    bestMatch = response;
                }
            });
            
            return bestMatch;
        }

        function showExample() {
            if (!currentCase) return;
            
            const caseData = clinicalCases[currentCase];
            let example = '';
            
            if (currentCase === 'case1') {
                example = `**Example ISBAR Handover:**

**Introduction:** "Hi, this is Dr. Smith from the Emergency Department. I'm calling about John Toder who came in overnight with chest pain."

**Situation:** "He's a 70-year-old male presenting with chest pain and shortness of breath."

**Background:** "He has a history of coronary artery disease, currently on metoprolol and aspirin. ECG shows ST-segment depression and his troponin levels are elevated."

**Assessment:** "I believe this is an acute coronary syndrome requiring immediate intervention."

**Recommendation:** "I recommend urgent transfer to the cardiac catheterization lab for evaluation and possible intervention."`;
            } else {
                example = `**ISBAR Framework Example:**

**Introduction:** Identify yourself, your role, and the patient

**Situation:** Briefly state what's happening with the patient

**Background:** Provide relevant medical history and current status  

**Assessment:** Share your clinical judgment

**Recommendation:** State what action you think should be taken`;
            }
            
            addMessage(example, false);
        }

        function resetConversation() {
            conversationHistory = [];
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';
            
            if (currentCase) {
                const caseData = clinicalCases[currentCase];
                addSystemMessage(`Clinical handover simulation: ${caseData.title}. You are the ${currentRole}. Begin your handover using ISBAR framework.`);
            }
        }

        function analyzeUserMessage(message) {
            const lowerMessage = message.toLowerCase();
            const keywords = lowerMessage.split(/\s+/).filter(word => word.length > 2);
            
            return {
                keywords: keywords,
                isbar_detected: detectISBARComponent(lowerMessage),
                communication_quality: assessCommunicationQuality(lowerMessage),
                length: message.length,
                clarity: assessClarity(lowerMessage)
            };
        }

        function detectISBARComponent(message) {
            const patterns = {
                introduction: /\b(hello|hi|name|calling|from|department|i am)\b/,
                situation: /\b(patient|presenting|has|current|status|condition)\b/,
                background: /\b(history|background|medical|previous|chronic|medication)\b/,
                assessment: /\b(assessment|think|believe|appears|seems|clinical)\b/,
                recommendation: /\b(recommend|need|request|suggest|transfer|urgent)\b/
            };

            for (const [component, pattern] of Object.entries(patterns)) {
                if (pattern.test(message)) {
                    return component;
                }
            }
            return 'unclear';
        }

        function assessCommunicationQuality(message) {
            let score = 0;
            const issues = [];
            
            // Check for clarity indicators
            if (message.includes('um') || message.includes('uh') || message.includes('...')) {
                issues.push('Contains hesitation markers');
            } else {
                score += 1;
            }
            
            // Check for completeness
            if (message.length < 10) {
                issues.push('Message too brief');
            } else if (message.length > 200) {
                issues.push('Message might be too lengthy');
            } else {
                score += 1;
            }
            
            // Check for professional language
            if (/\b(please|thank you|urgent|patient)\b/.test(message)) {
                score += 1;
            }
            
            return { score: score, issues: issues };
        }

        function assessClarity(message) {
            // Simple clarity assessment based on sentence structure
            const sentences = message.split(/[.!?]+/).filter(s => s.trim().length > 0);
            const avgSentenceLength = message.length / Math.max(sentences.length, 1);
            
            if (avgSentenceLength > 100) {
                return 'complex';
            } else if (avgSentenceLength < 20) {
                return 'simple';
            }
            return 'appropriate';
        }

        function getConversationStage() {
            const userMessages = conversationHistory.filter(msg => msg.role === 'user');
            
            if (userMessages.length === 0) return 'introduction';
            if (userMessages.length === 1) return 'situation';
            if (userMessages.length === 2) return 'background';
            if (userMessages.length === 3) return 'assessment';
            if (userMessages.length >= 4) return 'recommendation';
            
            return 'general';
        }

        function providePedagogicalFeedback(userMessage, stage) {
            setTimeout(() => {
                const analysis = analyzeUserMessage(userMessage);
                const feedback = generateDetailedFeedback(analysis, stage, userMessage);
                
                // Show analysis panel
                const analysisPanel = document.getElementById('analysisPanel');
                const analysisContent = document.getElementById('analysisContent');
                
                analysisPanel.classList.remove('hidden');
                
                // Add new feedback entry
                const feedbackDiv = document.createElement('div');
                feedbackDiv.className = 'border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-white dark:bg-gray-700';
                feedbackDiv.innerHTML = `
                    <div class="flex justify-between items-start mb-3">
                        <h3 class="font-semibold text-sm">Message Analysis</h3>
                        <span class="text-xs text-gray-500">${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="space-y-3">
                        ${feedback.map(item => `
                            <div class="flex items-start gap-3">
                                <div class="w-2 h-2 rounded-full mt-1.5 ${
                                    item.type === 'positive' ? 'bg-green-500' : 
                                    item.type === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                                }"></div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium">${item.category}</p>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">${item.feedback}</p>
                                    ${item.suggestion ? `<p class="text-xs text-blue-600 dark:text-blue-400 mt-1"><strong>Tip:</strong> ${item.suggestion}</p>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                
                analysisContent.insertBefore(feedbackDiv, analysisContent.firstChild);
                
                // Keep only last 5 feedback entries
                while (analysisContent.children.length > 5) {
                    analysisContent.removeChild(analysisContent.lastChild);
                }
            }, 2000); // Show feedback after bot response
        }

        function generateDetailedFeedback(analysis, expectedStage, userMessage) {
            const feedback = [];
            
            // ISBAR Component Analysis
            const detectedComponent = analysis.isbar_detected;
            if (detectedComponent === expectedStage) {
                feedback.push({
                    type: 'positive',
                    category: 'ISBAR Structure',
                    feedback: `Correctly identified as ${expectedStage.toUpperCase()} component`,
                    suggestion: null
                });
            } else if (detectedComponent === 'unclear') {
                feedback.push({
                    type: 'warning',
                    category: 'ISBAR Structure',
                    feedback: `Component not clearly identified. Expected ${expectedStage.toUpperCase()}`,
                    suggestion: `Try starting with key words like "${getISBARStarters(expectedStage)}"`
                });
            } else {
                feedback.push({
                    type: 'negative',
                    category: 'ISBAR Structure',
                    feedback: `Detected as ${detectedComponent.toUpperCase()} but expected ${expectedStage.toUpperCase()}`,
                    suggestion: `Focus on ${expectedStage} information at this stage`
                });
            }
            
            // Communication Quality
            const quality = analysis.communication_quality;
            if (quality.score >= 2) {
                feedback.push({
                    type: 'positive',
                    category: 'Communication Quality',
                    feedback: 'Clear and professional communication',
                    suggestion: null
                });
            } else {
                feedback.push({
                    type: 'warning',
                    category: 'Communication Quality',
                    feedback: `Issues: ${quality.issues.join(', ')}`,
                    suggestion: 'Use clear, concise language and professional terminology'
                });
            }
            
            // Length and Clarity
            if (analysis.clarity === 'appropriate') {
                feedback.push({
                    type: 'positive',
                    category: 'Message Clarity',
                    feedback: 'Appropriate message length and structure',
                    suggestion: null
                });
            } else if (analysis.clarity === 'complex') {
                feedback.push({
                    type: 'warning',
                    category: 'Message Clarity',
                    feedback: 'Message may be too complex or lengthy',
                    suggestion: 'Break down information into shorter, clearer statements'
                });
            } else {
                feedback.push({
                    type: 'warning',
                    category: 'Message Clarity',
                    feedback: 'Message might be too brief',
                    suggestion: 'Provide more specific details relevant to the handover'
                });
            }
            
            // Stage-specific feedback
            const stageSpecificFeedback = getStageSpecificFeedback(expectedStage, userMessage);
            if (stageSpecificFeedback) {
                feedback.push(stageSpecificFeedback);
            }
            
            return feedback;
        }

        function getISBARStarters(stage) {
            const starters = {
                introduction: '"Hello, this is [name] from [department]"',
                situation: '"The patient is..." or "We have a patient who..."',
                background: '"The patient has a history of..." or "Background includes..."',
                assessment: '"My assessment is..." or "I believe..."',
                recommendation: '"I recommend..." or "We need to..."'
            };
            return starters[stage] || 'appropriate opening phrases';
        }

        function getStageSpecificFeedback(stage, message) {
            const lowerMessage = message.toLowerCase();
            
            switch (stage) {
                case 'introduction':
                    if (!lowerMessage.includes('name') && !lowerMessage.includes('calling')) {
                        return {
                            type: 'warning',
                            category: 'Introduction Requirements',
                            feedback: 'Missing personal identification',
                            suggestion: 'Include your name, role, and department'
                        };
                    }
                    break;
                    
                case 'situation':
                    if (!lowerMessage.includes('patient') && !lowerMessage.includes('year') && !lowerMessage.includes('old')) {
                        return {
                            type: 'warning',
                            category: 'Situation Requirements',
                            feedback: 'Missing key patient demographics',
                            suggestion: 'Include patient age, gender, and presenting complaint'
                        };
                    }
                    break;
                    
                case 'background':
                    if (!lowerMessage.includes('history') && !lowerMessage.includes('medication') && !lowerMessage.includes('previous')) {
                        return {
                            type: 'warning',
                            category: 'Background Requirements',
                            feedback: 'Limited background information provided',
                            suggestion: 'Include relevant medical history, medications, and previous treatments'
                        };
                    }
                    break;
                    
                case 'assessment':
                    if (!lowerMessage.includes('think') && !lowerMessage.includes('believe') && !lowerMessage.includes('assessment')) {
                        return {
                            type: 'warning',
                            category: 'Assessment Requirements',
                            feedback: 'Clinical assessment not clearly stated',
                            suggestion: 'Share your clinical impression and reasoning'
                        };
                    }
                    break;
                    
                case 'recommendation':
                    if (!lowerMessage.includes('recommend') && !lowerMessage.includes('need') && !lowerMessage.includes('transfer')) {
                        return {
                            type: 'warning',
                            category: 'Recommendation Requirements',
                            feedback: 'No clear recommendation provided',
                            suggestion: 'State specific actions needed and urgency level'
                        };
                    }
                    break;
            }
            
            return null;
        }

        // Simulated conversations database
        const simulatedConversations = {
            case1: {
                good: [
                    {
                        speaker: 'student',
                        message: 'Hello, this is Dr. Emily Smith from the Emergency Department. I\'m calling about a 70-year-old male patient, John Toder, who presented with chest pain.',
                        isbar: 'introduction',
                        quality: 'excellent',
                        notes: 'Clear identification, patient name, presenting complaint'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Thank you Dr. Smith. This is Dr. Matthews, cardiology. I\'m ready to receive the handover. What\'s the current situation?',
                        pedagogy: 'Professional acknowledgment, appropriate follow-up question'
                    },
                    {
                        speaker: 'student',
                        message: 'The patient is currently stable but experiencing ongoing chest pain. He arrived 2 hours ago with severe substernal chest pain radiating to his left arm.',
                        isbar: 'situation',
                        quality: 'good',
                        notes: 'Clear situation description with relevant details'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I understand. Can you tell me about his background and any relevant medical history?',
                        pedagogy: 'Appropriate transition to background information'
                    },
                    {
                        speaker: 'student',
                        message: 'He has a significant history of coronary artery disease with previous MI in 2018. Currently on metoprolol 50mg BD, aspirin 100mg daily, and atorvastatin 40mg. ECG shows ST-segment depression in leads II, III, and aVF. Troponin levels are elevated at 2.5.',
                        isbar: 'background',
                        quality: 'excellent',
                        notes: 'Comprehensive background with medications and investigation results'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Those are concerning findings. What\'s your clinical assessment?',
                        pedagogy: 'Encouraging student to provide assessment'
                    },
                    {
                        speaker: 'student',
                        message: 'Based on his history, presentation, and investigations, I believe this is an acute coronary syndrome, likely NSTEMI. He requires urgent cardiology intervention.',
                        isbar: 'assessment',
                        quality: 'excellent',
                        notes: 'Clear clinical reasoning and appropriate assessment'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I agree with your assessment. What are you recommending?',
                        pedagogy: 'Confirming assessment and seeking recommendation'
                    },
                    {
                        speaker: 'student',
                        message: 'I recommend urgent transfer to the cardiac catheterization lab for coronary angiography and possible PCI. He\'s currently stable for transport with continuous cardiac monitoring.',
                        isbar: 'recommendation',
                        quality: 'excellent',
                        notes: 'Specific recommendation with consideration for patient safety'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Excellent handover. We\'ll accept the patient urgently. Please ensure dual antiplatelet therapy is initiated and arrange immediate transfer. ETA?',
                        pedagogy: 'Accepting recommendation with clear instructions'
                    }
                ],
                needs_improvement: [
                    {
                        speaker: 'student',
                        message: 'Hi, um, this is someone from Emergency about a patient with chest pain.',
                        isbar: 'introduction',
                        quality: 'poor',
                        notes: 'Lacks professional introduction, name, and specific details'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Hello. Could you please identify yourself properly and give me more details about the patient?',
                        pedagogy: 'Requesting proper identification - teaching moment'
                    },
                    {
                        speaker: 'student',
                        message: 'Oh right, I\'m Dr. Johnson. We have this old guy, maybe 70s, who came in with chest pain. He seems okay now.',
                        isbar: 'situation',
                        quality: 'poor',
                        notes: 'Informal language, vague description, lacks urgency'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Thank you Dr. Johnson. Can you be more specific about the patient\'s presentation and current vital signs?',
                        pedagogy: 'Requesting specific clinical information'
                    },
                    {
                        speaker: 'student',
                        message: 'Well, he has some heart problems before. ECG looks a bit abnormal. His blood tests are high.',
                        isbar: 'background',
                        quality: 'poor',
                        notes: 'Vague medical history, lacks specific details about investigations'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I need more specific information. What exactly do you mean by "heart problems" and "high blood tests"?',
                        pedagogy: 'Teaching moment - requesting clinical precision'
                    },
                    {
                        speaker: 'student',
                        message: 'I think it might be a heart attack or something. Should we send him up to you?',
                        isbar: 'assessment',
                        quality: 'poor',
                        notes: 'Uncertain assessment, lacks clinical reasoning'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Let\'s review the specific ECG changes and troponin levels first. Can you give me those details?',
                        pedagogy: 'Guiding toward systematic approach'
                    }
                ],
                poor: [
                    {
                        speaker: 'student',
                        message: 'Hello, we need to send someone up.',
                        isbar: 'introduction',
                        quality: 'very_poor',
                        notes: 'No identification, no patient details, unclear purpose'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I need you to start again with a proper handover. Please identify yourself, the patient, and the reason for the call.',
                        pedagogy: 'Complete restart required - fundamental structure missing'
                    },
                    {
                        speaker: 'student',
                        message: 'Patient has chest pain. He\'s old.',
                        isbar: 'situation',
                        quality: 'very_poor',
                        notes: 'Minimal information, unprofessional, lacks clinical detail'
                    },
                    {
                        speaker: 'avatar',
                        message: 'This handover lacks essential information. Let me guide you through the ISBAR structure. First, tell me your name and department.',
                        pedagogy: 'Educational intervention - teaching ISBAR framework'
                    },
                    {
                        speaker: 'student',
                        message: 'I\'m from Emergency. The patient, uh, he came in.',
                        isbar: 'background',
                        quality: 'very_poor',
                        notes: 'Still missing critical information, poor communication continues'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I need specific clinical information. What are his vital signs? What investigations have been done? This patient needs proper assessment.',
                        pedagogy: 'Continued educational guidance - emphasizing clinical requirements'
                    },
                    {
                        speaker: 'student',
                        message: 'He needs to go upstairs. Can you take him?',
                        isbar: 'recommendation',
                        quality: 'very_poor',
                        notes: 'No assessment provided, inappropriate recommendation without clinical basis'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I cannot accept a patient without proper handover information. Please get your supervisor to call me with complete details.',
                        pedagogy: 'Professional boundary - refusing unsafe handover'
                    }
                ]
            },
            case2: {
                good: [
                    {
                        speaker: 'student',
                        message: 'Hello, this is Nurse Sarah from Emergency Department. I need to hand over a 25-year-old female trauma patient to surgery urgently.',
                        isbar: 'introduction',
                        quality: 'excellent',
                        notes: 'Clear professional introduction with urgency indication'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Thank you Sarah. This is Dr. Chen from surgery. I\'m ready for the handover. What\'s the current situation?',
                        pedagogy: 'Professional acknowledgment, ready to receive information'
                    },
                    {
                        speaker: 'student',
                        message: 'The patient was involved in a high-speed motor vehicle accident 30 minutes ago. She\'s currently unconscious with a GCS of 3, hypotensive at 80/45, and tachycardic at 130 BPM.',
                        isbar: 'situation',
                        quality: 'excellent',
                        notes: 'Specific mechanism of injury with critical vital signs'
                    },
                    {
                        speaker: 'avatar',
                        message: 'That\'s very concerning. What do the imaging studies show?',
                        pedagogy: 'Appropriate follow-up seeking diagnostic information'
                    },
                    {
                        speaker: 'student',
                        message: 'CT scan shows multiple rib fractures, splenic laceration with active bleeding, and an open fracture of the left tibia. We\'ve initiated two large-bore IVs, blood transfusion is running, and she\'s intubated.',
                        isbar: 'background',
                        quality: 'excellent',
                        notes: 'Comprehensive imaging results with current interventions'
                    },
                    {
                        speaker: 'avatar',
                        message: 'What\'s your assessment of her condition?',
                        pedagogy: 'Seeking clinical assessment from the handover provider'
                    },
                    {
                        speaker: 'student',
                        message: 'This is a critically injured patient with polytrauma and ongoing internal hemorrhage. She needs immediate surgical intervention for damage control.',
                        isbar: 'assessment',
                        quality: 'excellent',
                        notes: 'Clear assessment with appropriate clinical reasoning'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Agreed. We\'ll take her to OR 3 immediately. Any other recommendations?',
                        pedagogy: 'Accepting patient and seeking additional input'
                    },
                    {
                        speaker: 'student',
                        message: 'I recommend immediate laparotomy for splenic repair, and orthopedics will need to be involved for the open fracture. Blood bank has 6 units ready.',
                        isbar: 'recommendation',
                        quality: 'excellent',
                        notes: 'Specific surgical recommendations with resource preparation'
                    }
                ],
                needs_improvement: [
                    {
                        speaker: 'student',
                        message: 'Hi, we have a trauma patient that needs to go to surgery.',
                        isbar: 'introduction',
                        quality: 'poor',
                        notes: 'Lacks identification and specific patient details'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Please identify yourself and give me more specific information about the patient.',
                        pedagogy: 'Requesting proper introduction and specifics'
                    },
                    {
                        speaker: 'student',
                        message: 'Sorry, I\'m Nurse Mike. It\'s a young woman, car accident, she\'s pretty bad.',
                        isbar: 'situation',
                        quality: 'poor',
                        notes: 'Informal language, lacks specific clinical details'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Can you give me her vital signs and level of consciousness?',
                        pedagogy: 'Requesting essential clinical parameters'
                    }
                ],
                poor: [
                    {
                        speaker: 'student',
                        message: 'Surgery needs to take this patient.',
                        isbar: 'introduction',
                        quality: 'very_poor',
                        notes: 'No identification, no patient information, command rather than handover'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I need a proper handover. Please start with identifying yourself and the patient.',
                        pedagogy: 'Educational redirection to proper handover structure'
                    },
                    {
                        speaker: 'student',
                        message: 'It\'s a trauma. Car crash. She\'s bleeding.',
                        isbar: 'situation',
                        quality: 'very_poor',
                        notes: 'Extremely vague, no clinical details, unprofessional communication'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I need specific information. What are her vital signs? What\'s her level of consciousness? What injuries have been identified?',
                        pedagogy: 'Educational intervention - requesting essential clinical data'
                    },
                    {
                        speaker: 'student',
                        message: 'She\'s unconscious. Blood pressure is low. Just take her.',
                        isbar: 'background',
                        quality: 'very_poor',
                        notes: 'Minimal information, no specific values, demanding tone continues'
                    },
                    {
                        speaker: 'avatar',
                        message: 'This is completely inadequate. I cannot accept a critically injured patient without proper clinical information. Get your senior colleague now.',
                        pedagogy: 'Professional refusal - unsafe practice intervention'
                    }
                ]
            },
            case3: {
                good: [
                    {
                        speaker: 'student',
                        message: 'Hello, this is respiratory therapist Mark calling about a 68-year-old male COPD patient who needs ICU transfer.',
                        isbar: 'introduction',
                        quality: 'excellent',
                        notes: 'Clear professional introduction with patient summary'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Thank you Mark. This is Dr. Williams from ICU. Please tell me about the current situation.',
                        pedagogy: 'Professional acknowledgment and request for details'
                    },
                    {
                        speaker: 'student',
                        message: 'The patient presented with severe dyspnea and hypoxia. He\'s currently on high-flow oxygen at 15L, oxygen saturation 88%, respiratory rate 35, using accessory muscles.',
                        isbar: 'situation',
                        quality: 'excellent',
                        notes: 'Specific respiratory status with objective measurements'
                    },
                    {
                        speaker: 'avatar',
                        message: 'What\'s his medical history and current treatment?',
                        pedagogy: 'Seeking background and current management'
                    },
                    {
                        speaker: 'student',
                        message: 'He has severe COPD, 40-year smoking history with multiple previous ICU admissions. Currently on salbutamol nebulizers, prednisolone 40mg, and we\'ve started him on BiPAP.',
                        isbar: 'background',
                        quality: 'excellent',
                        notes: 'Relevant history with current therapeutic interventions'
                    },
                    {
                        speaker: 'avatar',
                        message: 'What\'s your assessment of his respiratory status?',
                        pedagogy: 'Seeking clinical assessment'
                    },
                    {
                        speaker: 'student',
                        message: 'This appears to be a severe COPD exacerbation with impending respiratory failure. Despite maximal therapy, he\'s not improving and may need intubation.',
                        isbar: 'assessment',
                        quality: 'excellent',
                        notes: 'Clear assessment with prognosis and escalation needs'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I agree. We\'ll accept him for close monitoring. What are your recommendations?',
                        pedagogy: 'Accepting patient and seeking recommendations'
                    },
                    {
                        speaker: 'student',
                        message: 'I recommend ICU monitoring with consideration for intubation if no improvement in 2 hours. Continue current bronchodilators and steroids.',
                        isbar: 'recommendation',
                        quality: 'excellent',
                        notes: 'Specific timeline and treatment recommendations'
                    }
                ],
                needs_improvement: [
                    {
                        speaker: 'student',
                        message: 'Hi, we have a COPD patient having trouble breathing.',
                        isbar: 'introduction',
                        quality: 'poor',
                        notes: 'Missing identification and specific patient details'
                    },
                    {
                        speaker: 'avatar',
                        message: 'Please identify yourself and give me more details about the patient\'s condition.',
                        pedagogy: 'Requesting proper identification and specifics'
                    }
                ],
                poor: [
                    {
                        speaker: 'student',
                        message: 'Patient can\'t breathe. ICU needs to take him.',
                        isbar: 'introduction',
                        quality: 'very_poor',
                        notes: 'No identification, minimal information, demanding tone'
                    },
                    {
                        speaker: 'avatar',
                        message: 'I need a complete handover. Please start by identifying yourself and providing structured information.',
                        pedagogy: 'Complete restart with educational guidance'
                    },
                    {
                        speaker: 'student',
                        message: 'He has COPD. He\'s wheezing.',
                        isbar: 'situation',
                        quality: 'very_poor',
                        notes: 'Extremely limited information, no vital signs or assessment'
                    },
                    {
                        speaker: 'avatar',
                        message: 'What are his vital signs? Oxygen saturation? Respiratory rate? I need specific clinical parameters.',
                        pedagogy: 'Educational intervention - requesting essential respiratory data'
                    },
                    {
                        speaker: 'student',
                        message: 'He\'s on oxygen. Been smoking for years.',
                        isbar: 'background',
                        quality: 'very_poor',
                        notes: 'Vague information, no specific treatment details or clinical values'
                    },
                    {
                        speaker: 'avatar',
                        message: 'This is inadequate for a respiratory emergency. I cannot accept a COPD patient without proper clinical assessment and vital signs. Please get your charge nurse.',
                        pedagogy: 'Professional refusal - patient safety priority'
                    }
                ]
            }
        };

        let currentConversation = [];
        let currentMessageIndex = 0;
        let isPlaying = false;
        let playbackTimer = null;

        function playSimulatedConversation() {
            if (!currentCase) return;
            
            const studentLevel = document.getElementById('studentSelect').value;
            currentConversation = simulatedConversations[currentCase][studentLevel];
            currentMessageIndex = 0;
            isPlaying = true;
            
            // Clear chat
            document.getElementById('chatMessages').innerHTML = '';
            
            // Show controls
            document.getElementById('playConversationBtn').classList.add('hidden');
            document.getElementById('pauseBtn').classList.remove('hidden');
            
            // Start playback
            playNextMessage();
        }

        function playNextMessage() {
            if (!isPlaying || currentMessageIndex >= currentConversation.length) {
                endPlayback();
                return;
            }
            
            const messageData = currentConversation[currentMessageIndex];
            const isStudent = messageData.speaker === 'student';
            
            // Add message to chat
            addConversationMessage(messageData, isStudent);
            
            // Add pedagogical analysis for student messages
            if (isStudent) {
                setTimeout(() => {
                    addPedagogicalAnalysis(messageData, currentMessageIndex);
                }, 1000);
            }
            
            currentMessageIndex++;
            
            // Schedule next message
            playbackTimer = setTimeout(playNextMessage, isStudent ? 3000 : 2000);
        }

        function addConversationMessage(messageData, isStudent) {
            const chatMessages = document.getElementById('chatMessages');
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded-lg ${isStudent ? 'bg-primary text-white ml-8' : 'bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 mr-8'}`;
            
            let messageHTML = `
                <div class="flex justify-between items-start mb-2">
                    <strong class="text-sm">${isStudent ? 'Student' : 'Avatar (Cardiologist)'}</strong>
                    <span class="text-xs opacity-75">${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="mb-2">${messageData.message}</div>
            `;
            
            // Add ISBAR annotation for student messages
            if (isStudent && messageData.isbar) {
                const qualityColor = messageData.quality === 'excellent' ? 'bg-green-100 text-green-800' :
                                  messageData.quality === 'good' ? 'bg-blue-100 text-blue-800' :
                                  messageData.quality === 'poor' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-red-100 text-red-800';
                
                messageHTML += `
                    <div class="isbar-annotation ${messageData.isbar} text-xs mt-2">
                        <strong>${messageData.isbar.toUpperCase()}</strong>
                    </div>
                    <div class="mt-2 text-xs">
                        <span class="inline-block px-2 py-1 rounded-full text-xs ${qualityColor}">${messageData.quality.replace('_', ' ')}</span>
                    </div>
                `;
            }
            
            messageDiv.innerHTML = messageHTML;
            chatMessages.appendChild(messageDiv);
            
            // Scroll to bottom
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function addPedagogicalAnalysis(messageData, messageIndex) {
            const analysisPanel = document.getElementById('analysisPanel');
            const analysisContent = document.getElementById('analysisContent');
            
            analysisPanel.classList.remove('hidden');
            
            const analysisDiv = document.createElement('div');
            analysisDiv.className = 'border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-white dark:bg-gray-700';
            
            const qualityScore = messageData.quality === 'excellent' ? 95 :
                               messageData.quality === 'good' ? 80 :
                               messageData.quality === 'poor' ? 40 : 20;
            
            const qualityColor = qualityScore >= 80 ? 'text-green-600' :
                               qualityScore >= 60 ? 'text-blue-600' :
                               qualityScore >= 40 ? 'text-yellow-600' : 'text-red-600';
            
            analysisDiv.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <h3 class="font-semibold text-sm">Message ${messageIndex + 1} Analysis</h3>
                    <div class="text-right">
                        <div class="text-xs text-gray-500">${new Date().toLocaleTimeString()}</div>
                        <div class="text-sm font-semibold ${qualityColor}">${qualityScore}%</div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Original Message</h4>
                    <div class="p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs italic border-l-2 border-primary">
                        "${messageData.message}"
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                    <div>
                        <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">ISBAR Component</h4>
                        <span class="inline-block px-2 py-1 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            ${messageData.isbar ? messageData.isbar.toUpperCase() : 'N/A'}
                        </span>
                    </div>
                    <div>
                        <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Communication Quality</h4>
                        <span class="inline-block px-2 py-1 rounded text-xs ${
                            messageData.quality === 'excellent' ? 'bg-green-100 text-green-800' :
                            messageData.quality === 'good' ? 'bg-blue-100 text-blue-800' :
                            messageData.quality === 'poor' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                        }">
                            ${messageData.quality.replace('_', ' ').toUpperCase()}
                        </span>
                    </div>
                </div>
                
                <div class="space-y-2">
                    <div>
                        <h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Detailed Analysis</h4>
                        <p class="text-xs text-gray-600 dark:text-gray-400">${messageData.notes}</p>
                    </div>
                    
                    ${getDetailedFeedbackForMessage(messageData)}
                </div>
            `;
            
            analysisContent.insertBefore(analysisDiv, analysisContent.firstChild);
            
            // Keep only last 8 analysis entries
            while (analysisContent.children.length > 8) {
                analysisContent.removeChild(analysisContent.lastChild);
            }
        }

        function getDetailedFeedbackForMessage(messageData) {
            const suggestions = [];
            
            if (messageData.quality === 'poor' || messageData.quality === 'very_poor') {
                suggestions.push({
                    type: 'improvement',
                    text: 'Consider using more specific medical terminology and clear structure'
                });
                
                if (messageData.isbar === 'introduction') {
                    suggestions.push({
                        type: 'template',
                        text: 'Use format: "Hello, this is [Name] from [Department], calling about [Patient]"'
                    });
                }
            }
            
            if (messageData.quality === 'excellent') {
                suggestions.push({
                    type: 'positive',
                    text: 'Excellent communication demonstrates professional standards'
                });
            }
            
            return suggestions.map(suggestion => `
                <div class="flex items-start gap-2 mt-2">
                    <div class="w-2 h-2 rounded-full mt-1 ${
                        suggestion.type === 'positive' ? 'bg-green-500' :
                        suggestion.type === 'improvement' ? 'bg-yellow-500' : 'bg-blue-500'
                    }"></div>
                    <p class="text-xs text-gray-600 dark:text-gray-400">${suggestion.text}</p>
                </div>
            `).join('');
        }

        function pauseConversation() {
            isPlaying = false;
            if (playbackTimer) {
                clearTimeout(playbackTimer);
                playbackTimer = null;
            }
            
            document.getElementById('playConversationBtn').classList.remove('hidden');
            document.getElementById('pauseBtn').classList.add('hidden');
            document.getElementById('playConversationBtn').textContent = '▶ Resume';
        }

        function endPlayback() {
            isPlaying = false;
            if (playbackTimer) {
                clearTimeout(playbackTimer);
                playbackTimer = null;
            }
            
            document.getElementById('playConversationBtn').classList.remove('hidden');
            document.getElementById('pauseBtn').classList.add('hidden');
            document.getElementById('playConversationBtn').textContent = '▶ Play Conversation';
            
            // Add session summary
            setTimeout(() => {
                addSessionSummary();
            }, 2000);
        }

        function addSessionSummary() {
            const analysisPanel = document.getElementById('analysisPanel');
            const analysisContent = document.getElementById('analysisContent');
            
            const studentLevel = document.getElementById('studentSelect').value;
            const totalMessages = currentConversation.filter(msg => msg.speaker === 'student').length;
            
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'border-2 border-primary rounded-lg p-4 bg-primary/5 dark:bg-primary/10';
            summaryDiv.innerHTML = `
                <h3 class="font-semibold text-lg mb-3 text-primary">Session Summary</h3>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Performance Level</div>
                        <div class="font-medium">${studentLevel.replace('_', ' ').toUpperCase()}</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Messages Analyzed</div>
                        <div class="font-medium">${totalMessages}</div>
                    </div>
                </div>
                
                <div class="text-sm">
                    <h4 class="font-medium mb-2">Pedagogical Insights:</h4>
                    <ul class="space-y-1 text-gray-600 dark:text-gray-400">
                        ${getPedagogicalInsights(studentLevel)}
                    </ul>
                </div>
                
                <div class="mt-4 text-xs text-gray-500">
                    This session demonstrates how avatar solutions can record, analyze, and provide detailed feedback on clinical communication skills for educational purposes.
                </div>
            `;
            
            analysisContent.insertBefore(summaryDiv, analysisContent.firstChild);
        }

        function getPedagogicalInsights(level) {
            const insights = {
                good: [
                    '<li>• Student demonstrates strong ISBAR structure throughout handover</li>',
                    '<li>• Professional language and clear communication evident</li>',
                    '<li>• Appropriate level of clinical detail provided</li>',
                    '<li>• Ready for independent practice with minimal supervision</li>'
                ],
                needs_improvement: [
                    '<li>• ISBAR structure present but inconsistent application</li>',
                    '<li>• Communication lacks precision in medical terminology</li>',
                    '<li>• Requires practice with structured handover protocols</li>',
                    '<li>• Would benefit from additional training sessions</li>'
                ],
                poor: [
                    '<li>• Fundamental gaps in handover structure and content</li>',
                    '<li>• Requires intensive training on ISBAR framework</li>',
                    '<li>• Communication skills need significant development</li>',
                    '<li>• Recommend supervised practice before independent handovers</li>'
                ]
            };
            
            return insights[level].join('');
        }

        function resetSimulation() {
            isPlaying = false;
            if (playbackTimer) {
                clearTimeout(playbackTimer);
                playbackTimer = null;
            }
            
            currentConversation = [];
            currentMessageIndex = 0;
            
            // Reset UI
            document.getElementById('playConversationBtn').classList.remove('hidden');
            document.getElementById('pauseBtn').classList.add('hidden');
            document.getElementById('playConversationBtn').textContent = '▶ Play Conversation';
            
            // Clear messages
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';
            
            // Clear analysis
            const analysisContent = document.getElementById('analysisContent');
            analysisContent.innerHTML = '';
            document.getElementById('analysisPanel').classList.add('hidden');
            
            if (currentCase) {
                const caseData = clinicalCases[currentCase];
                addSystemMessage(`Avatar Training Simulation: ${caseData.title}. Select a student performance level and click "Play Conversation" to view recorded interaction.`);
            }
        }

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>


</body></html>