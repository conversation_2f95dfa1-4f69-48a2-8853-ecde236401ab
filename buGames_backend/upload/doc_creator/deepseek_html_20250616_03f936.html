<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IGCSE Climate Change Vocabulary Quiz</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f8ff;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .game-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .question {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            background-color: #eaf7fd;
        }
        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        .option {
            padding: 10px;
            border: 1px solid #3498db;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .option:hover {
            background-color: #3498db;
            color: white;
        }
        .selected {
            background-color: #3498db;
            color: white;
        }
        .correct {
            background-color: #2ecc71;
            border-color: #2ecc71;
            color: white;
        }
        .incorrect {
            background-color: #e74c3c;
            border-color: #e74c3c;
            color: white;
        }
        .feedback {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .correct-feedback {
            background-color: #d5f5e3;
            color: #27ae60;
        }
        .incorrect-feedback {
            background-color: #fadbd8;
            color: #e74c3c;
        }
        .next-btn, .restart-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            display: block;
            margin-left: auto;
            transition: background-color 0.3s;
        }
        .next-btn:hover, .restart-btn:hover {
            background-color: #2980b9;
        }
        .score-display {
            text-align: center;
            font-size: 1.2em;
            margin: 20px 0;
            font-weight: bold;
        }
        .progress-bar {
            height: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background-color: #3498db;
            width: 0%;
            transition: width 0.5s;
        }
        .definition {
            font-style: italic;
            margin-top: 5px;
            color: #7f8c8d;
        }
        .results {
            text-align: center;
            display: none;
        }
        .results h2 {
            color: #2c3e50;
        }
        .celebration {
            font-size: 3em;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>IGCSE Climate Change Vocabulary Quiz</h1>
        <div class="progress-bar">
            <div class="progress" id="progress"></div>
        </div>
        <div class="score-display" id="score">Score: 0/0</div>
        
        <div id="quiz-area">
            <div class="question" id="question-text"></div>
            <div class="options" id="options-container"></div>
            <div class="feedback" id="feedback"></div>
            <button class="next-btn" id="next-btn" style="display: none;">Next Question</button>
        </div>
        
        <div class="results" id="results">
            <h2>Quiz Completed!</h2>
            <div class="celebration" id="celebration">🎉</div>
            <p>Your final score: <span id="final-score">0</span>/<span id="total-questions">0</span></p>
            <p id="performance-comment"></p>
            <button class="restart-btn" id="restart-btn">Restart Quiz</button>
        </div>
    </div>

    <script>
        // Climate change vocabulary questions
        const questions = [
            {
                question: "What is the definition of 'climate change'?",
                options: [
                    "Daily variations in weather conditions",
                    "Long-term shifts in temperatures and weather patterns",
                    "Seasonal changes in precipitation",
                    "Short-term atmospheric disturbances"
                ],
                answer: 1,
                definition: "Climate change refers to long-term shifts in temperatures and weather patterns, primarily caused by human activities like burning fossil fuels."
            },
            {
                question: "What does 'greenhouse effect' mean?",
                options: [
                    "The process of plants growing in a greenhouse",
                    "The trapping of heat by gases in Earth's atmosphere",
                    "The reflection of sunlight by polar ice caps",
                    "The cooling of Earth's surface at night"
                ],
                answer: 1,
                definition: "The greenhouse effect is the natural process where certain gases in Earth's atmosphere trap heat, preventing it from escaping into space and keeping our planet warm enough to sustain life."
            },
            {
                question: "Which term refers to gases that contribute to the greenhouse effect?",
                options: [
                    "Oxygen gases",
                    "Noble gases",
                    "Greenhouse gases",
                    "Inert gases"
                ],
                answer: 2,
                definition: "Greenhouse gases include carbon dioxide (CO₂), methane (CH₄), nitrous oxide (N₂O), and water vapor that trap heat in the atmosphere."
            },
            {
                question: "What is 'carbon footprint'?",
                options: [
                    "The mark left by carbon paper",
                    "The total greenhouse gas emissions caused by an individual or organization",
                    "The amount of carbon in fossil fuels",
                    "The carbon content in soil"
                ],
                answer: 1,
                definition: "A carbon footprint is the total amount of greenhouse gases (including carbon dioxide and methane) that are generated by our actions, usually measured in equivalent tons of CO₂."
            },
            {
                question: "What does 'mitigation' mean in climate change context?",
                options: [
                    "Reducing the severity of climate change impacts",
                    "Adapting to climate change effects",
                    "Studying climate patterns",
                    "Measuring temperature changes"
                ],
                answer: 0,
                definition: "Mitigation refers to actions that reduce the severity of climate change, primarily by cutting greenhouse gas emissions or enhancing carbon sinks."
            },
            {
                question: "What is 'adaptation' in relation to climate change?",
                options: [
                    "Changing the climate back to its original state",
                    "Adjusting to current or expected climate changes",
                    "Preventing climate change from occurring",
                    "Measuring climate change impacts"
                ],
                answer: 1,
                definition: "Adaptation involves adjusting to actual or expected climate change and its effects to moderate harm or exploit beneficial opportunities."
            },
            {
                question: "What does 'sustainable development' mean?",
                options: [
                    "Development that meets present needs without compromising future generations",
                    "Rapid industrial growth regardless of environmental impact",
                    "Development focused only on economic growth",
                    "Temporary solutions to environmental problems"
                ],
                answer: 0,
                definition: "Sustainable development meets the needs of the present without compromising the ability of future generations to meet their own needs, balancing economic, social and environmental considerations."
            },
            {
                question: "What is 'renewable energy'?",
                options: [
                    "Energy from sources that are finite and will run out",
                    "Energy from sources that naturally replenish",
                    "Energy that cannot be used again",
                    "Energy from fossil fuels"
                ],
                answer: 1,
                definition: "Renewable energy comes from natural sources that are constantly replenished, such as sunlight, wind, water movement, and geothermal heat."
            },
            {
                question: "What does 'deforestation' refer to?",
                options: [
                    "Planting new trees in urban areas",
                    "The natural decay of forests over time",
                    "The permanent removal of forests for other land uses",
                    "Seasonal changes in forest cover"
                ],
                answer: 2,
                definition: "Deforestation is the permanent removal of trees to make room for something besides forest, contributing to climate change by reducing carbon storage capacity."
            },
            {
                question: "What is 'sea level rise' primarily caused by?",
                options: [
                    "Increased rainfall over oceans",
                    "Melting land ice and thermal expansion of seawater",
                    "Changes in ocean currents",
                    "Underwater volcanic activity"
                ],
                answer: 1,
                definition: "Sea level rise is primarily caused by two factors related to global warming: the added water from melting ice sheets and glaciers, and the expansion of seawater as it warms."
            }
        ];

        // Game variables
        let currentQuestionIndex = 0;
        let score = 0;
        let selectedOption = null;
        let quizCompleted = false;

        // DOM elements
        const questionText = document.getElementById('question-text');
        const optionsContainer = document.getElementById('options-container');
        const feedback = document.getElementById('feedback');
        const nextBtn = document.getElementById('next-btn');
        const scoreDisplay = document.getElementById('score');
        const progressBar = document.getElementById('progress');
        const quizArea = document.getElementById('quiz-area');
        const resultsArea = document.getElementById('results');
        const finalScore = document.getElementById('final-score');
        const totalQuestions = document.getElementById('total-questions');
        const performanceComment = document.getElementById('performance-comment');
        const restartBtn = document.getElementById('restart-btn');
        const celebration = document.getElementById('celebration');

        // Initialize the quiz
        function initQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            quizCompleted = false;
            quizArea.style.display = 'block';
            resultsArea.style.display = 'none';
            showQuestion();
        }

        // Display the current question
        function showQuestion() {
            const question = questions[currentQuestionIndex];
            questionText.textContent = question.question;
            
            optionsContainer.innerHTML = '';
            question.options.forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.classList.add('option');
                optionElement.textContent = option;
                optionElement.dataset.index = index;
                optionElement.addEventListener('click', selectOption);
                optionsContainer.appendChild(optionElement);
            });
            
            feedback.style.display = 'none';
            nextBtn.style.display = 'none';
            selectedOption = null;
            
            // Update progress
            progressBar.style.width = `${(currentQuestionIndex / questions.length) * 100}%`;
            scoreDisplay.textContent = `Score: ${score}/${currentQuestionIndex}`;
        }

        // Handle option selection
        function selectOption(e) {
            if (quizCompleted) return;
            
            const selectedIndex = parseInt(e.target.dataset.index);
            const question = questions[currentQuestionIndex];
            
            // Remove previous selection
            if (selectedOption !== null) {
                const prevOption = optionsContainer.children[selectedOption];
                prevOption.classList.remove('selected');
            }
            
            // Mark new selection
            e.target.classList.add('selected');
            selectedOption = selectedIndex;
            
            // Check answer
            const isCorrect = selectedIndex === question.answer;
            
            // Show feedback
            feedback.style.display = 'block';
            feedback.textContent = question.definition;
            
            if (isCorrect) {
                feedback.classList.add('correct-feedback');
                feedback.classList.remove('incorrect-feedback');
            } else {
                feedback.classList.add('incorrect-feedback');
                feedback.classList.remove('correct-feedback');
                
                // Highlight correct answer
                const correctOption = optionsContainer.children[question.answer];
                correctOption.classList.add('correct');
            }
            
            // Highlight selected option
            e.target.classList.add(isCorrect ? 'correct' : 'incorrect');
            
            // Update score if correct
            if (isCorrect) {
                score++;
                scoreDisplay.textContent = `Score: ${score}/${currentQuestionIndex + 1}`;
            }
            
            nextBtn.style.display = 'block';
        }

        // Move to next question
        function nextQuestion() {
            currentQuestionIndex++;
            
            if (currentQuestionIndex < questions.length) {
                showQuestion();
            } else {
                endQuiz();
            }
        }

        // End the quiz and show results
        function endQuiz() {
            quizCompleted = true;
            quizArea.style.display = 'none';
            resultsArea.style.display = 'block';
            
            finalScore.textContent = score;
            totalQuestions.textContent = questions.length;
            
            // Performance comment
            const percentage = (score / questions.length) * 100;
            if (percentage >= 80) {
                performanceComment.textContent = "Excellent! You have a strong understanding of climate change vocabulary.";
                celebration.textContent = "🌟 Excellent! 🌟";
            } else if (percentage >= 60) {
                performanceComment.textContent = "Good job! You have a decent understanding but could review some terms.";
                celebration.textContent = "👍 Good Job! 👍";
            } else {
                performanceComment.textContent = "Keep studying! Review the climate change vocabulary to improve your understanding.";
                celebration.textContent = "📚 Keep Learning! 📚";
            }
            
            progressBar.style.width = '100%';
        }

        // Event listeners
        nextBtn.addEventListener('click', nextQuestion);
        restartBtn.addEventListener('click', initQuiz);

        // Start the quiz
        initQuiz();
    </script>
</body>
</html>