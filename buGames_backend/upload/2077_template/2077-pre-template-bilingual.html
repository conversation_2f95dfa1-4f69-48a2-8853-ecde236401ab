<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voluntary Teaching Project - LANG 2077</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'bg-light': '#FFFFFF',
                        'bg-dark': '#181818'
                    }
                }
            }
        }
    </script>
    <style>
        .section {
            scroll-margin-top: 100px;
        }
        .tab-button.active {
            background-color: #5D5CDE;
            color: white;
        }
        .placeholder-img {
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .dark .placeholder-img {
            background: linear-gradient(45deg, #404040 25%, transparent 25%), 
                        linear-gradient(-45deg, #404040 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #404040 75%), 
                        linear-gradient(-45deg, transparent 75%, #404040 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
    </style>
</head>
<body class="bg-bg-light dark:bg-bg-dark text-gray-900 dark:text-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- Language Switcher -->
        <div class="flex justify-end mb-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-1 flex">
                <button onclick="switchLanguage('en')" id="lang-en" class="px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white">
                    English
                </button>
                <button onclick="switchLanguage('zh')" id="lang-zh" class="px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary">
                    中文
                </button>
            </div>
        </div>

        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-primary mb-2" id="main-title">Voluntary Teaching Project</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400" id="main-subtitle">HKBU LANG 2077 - Innovative Language Education Initiative</p>
        </div>

        <!-- Navigation Tabs -->
        <nav class="sticky top-0 bg-bg-light dark:bg-bg-dark z-10 pb-4">
            <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700 mb-8">
                <button onclick="scrollToSection('video')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-video">
                    📹 Video
                </button>
                <button onclick="scrollToSection('contexts')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-contexts">
                    🎯 Teaching Contexts
                </button>
                <button onclick="scrollToSection('ai-solutions')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-ai">
                    🤖 AI Solutions
                </button>
                <button onclick="scrollToSection('reflection')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-reflection">
                    💭 Sharing & Reflection
                </button>
            </div>
        </nav>

        <!-- Content Sections -->
        <div class="space-y-16">
            <!-- Video Section -->
            <section id="video" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-6 text-center" id="video-title">Project Overview Video</h2>
                    <div class="aspect-video max-w-4xl mx-auto" id="video-container">
                        <iframe 
                            width="100%" 
                            height="100%" 
                            src="https://www.youtube.com/embed/o8NPllzkFhE?start=1118" 
                            title="Voluntary Teaching Project Video" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen
                            class="rounded-lg">
                        </iframe>
                    </div>
                    <p class="text-center text-gray-600 dark:text-gray-400 mt-4" id="video-description">
                        Watch our comprehensive overview of the voluntary teaching project and its impact on language education.
                    </p>
                </div>
            </section>

            <!-- Teaching Contexts Section -->
            <section id="contexts" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center" id="contexts-title">Teaching Contexts, Challenges and Needs</h2>
                    
                    <!-- Context 1 -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="context1-title">Educational Context</h3>
                            <p class="text-lg leading-relaxed mb-4" id="context1-p1">
                                Our voluntary teaching project targets underserved communities where English language education resources are limited. We focus on providing quality language instruction to students who lack access to traditional educational opportunities.
                            </p>
                            <p class="text-lg leading-relaxed" id="context1-p2">
                                The project emphasizes practical communication skills, cultural exchange, and building confidence in English language use through interactive and engaging teaching methodologies.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="context1-img">Teaching Context Image</span>
                        </div>
                    </div>

                    <!-- Context 2 -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center order-2 md:order-1">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="context2-img">Challenges Overview</span>
                        </div>
                        <div class="order-1 md:order-2">
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="context2-title">Key Challenges</h3>
                            <ul class="text-lg leading-relaxed space-y-2">
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span id="challenge1">Limited technological infrastructure in target communities</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span id="challenge2">Diverse learning levels and backgrounds among students</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span id="challenge3">Cultural and linguistic barriers affecting communication</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span id="challenge4">Time constraints and scheduling difficulties</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Context 3 -->
                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="context3-title">Community Needs</h3>
                            <p class="text-lg leading-relaxed mb-4" id="context3-p1">
                                Through community engagement and needs assessment, we identified critical gaps in English language education. Local communities expressed strong desire for practical English skills that could enhance employment opportunities and social mobility.
                            </p>
                            <p class="text-lg leading-relaxed" id="context3-p2">
                                The program addresses these needs through tailored curriculum design, flexible scheduling, and culturally sensitive teaching approaches that respect local contexts while promoting global communication skills.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="context3-img">Community Needs Assessment</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI Solutions Section -->
            <section id="ai-solutions" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center" id="ai-title">AI-Powered Teaching Solutions</h2>
                    
                    <!-- AI Introduction -->
                    <div class="mb-12">
                        <h3 class="text-2xl font-semibold mb-4 text-primary" id="ai-intro-title">Innovative AI Integration</h3>
                        <p class="text-lg leading-relaxed mb-6" id="ai-intro-p">
                            Our project leverages cutting-edge AI technology to enhance the teaching and learning experience. We've developed customized chatbots and AI-assisted tools that provide personalized support to both teachers and students, making language learning more accessible and effective.
                        </p>
                    </div>

                    <!-- Customized Chatbot -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="chatbot-title">Custom AI Teaching Assistant</h3>
                            <p class="text-lg leading-relaxed mb-4" id="chatbot-p">
                                Our AI chatbot serves as a 24/7 teaching assistant, providing instant feedback on grammar, pronunciation, and vocabulary. It adapts to individual learning styles and progress levels, offering personalized exercises and explanations.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2" id="chatbot-features-title">Key Features:</h4>
                                <ul class="space-y-1 text-sm">
                                    <li id="feature1">• Real-time grammar correction</li>
                                    <li id="feature2">• Pronunciation guidance</li>
                                    <li id="feature3">• Adaptive learning paths</li>
                                    <li id="feature4">• Progress tracking</li>
                                </ul>
                            </div>
                        </div>
                        <div class="placeholder-img rounded-lg h-80 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="chatbot-img">AI Chatbot Interface Screenshot</span>
                        </div>
                    </div>

                    <!-- AI Tools -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-80 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="analytics-img">AI Analytics Dashboard</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="analytics-title">Learning Analytics Platform</h3>
                            <p class="text-lg leading-relaxed mb-4" id="analytics-p">
                                Advanced AI analytics help teachers understand student progress patterns, identify learning difficulties, and optimize teaching strategies. The platform provides detailed insights into student engagement and performance metrics.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2" id="analytics-features-title">Analytics Features:</h4>
                                <ul class="space-y-1 text-sm">
                                    <li id="analytics1">• Student progress visualization</li>
                                    <li id="analytics2">• Learning pattern analysis</li>
                                    <li id="analytics3">• Predictive performance modeling</li>
                                    <li id="analytics4">• Customized intervention recommendations</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Implementation Results -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-primary" id="impact-title">Implementation Impact</h3>
                        <p class="text-lg leading-relaxed" id="impact-p">
                            The integration of AI solutions has resulted in improved learning outcomes, increased student engagement, and more efficient teaching processes. Students report higher confidence levels and teachers benefit from data-driven insights to enhance their instructional methods.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Sharing & Reflection Section -->
            <section id="reflection" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center" id="reflection-title">Sharing & Reflection</h2>
                    
                    <!-- Project Outcomes -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="outcomes-title">Project Outcomes</h3>
                            <p class="text-lg leading-relaxed mb-4" id="outcomes-p1">
                                Our voluntary teaching project has yielded significant positive outcomes for both students and teachers. Student engagement has increased by 40%, and standardized test scores have improved across all participating communities.
                            </p>
                            <p class="text-lg leading-relaxed" id="outcomes-p2">
                                The project has also enhanced the professional development of HKBU students, providing them with valuable real-world teaching experience and cross-cultural communication skills.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="outcomes-img">Student Success Stories</span>
                        </div>
                    </div>

                    <!-- Teacher Reflections -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="teacher-img">Teacher Feedback Session</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="teacher-title">Teacher Reflections</h3>
                            <blockquote class="text-lg italic mb-4 border-l-4 border-primary pl-4" id="teacher-quote">
                                "This project has transformed my understanding of language teaching. Working with diverse communities has made me a more adaptable and empathetic educator."
                            </blockquote>
                            <p class="text-lg leading-relaxed" id="teacher-p">
                                Participating teachers report enhanced cultural sensitivity, improved classroom management skills, and a deeper appreciation for the challenges faced by English language learners in underserved communities.
                            </p>
                        </div>
                    </div>

                    <!-- Community Impact -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="community-title">Community Impact</h3>
                            <p class="text-lg leading-relaxed mb-4" id="community-p">
                                The project has strengthened community bonds and created lasting partnerships between HKBU and local organizations. Community leaders have expressed enthusiasm for continuing the program and expanding its reach.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2" id="impact-stats-title">Measurable Impact:</h4>
                                <ul class="space-y-1">
                                    <li id="stat1">• 150+ students directly benefited</li>
                                    <li id="stat2">• 25 HKBU student teachers participated</li>
                                    <li id="stat3">• 8 community partnerships established</li>
                                    <li id="stat4">• 95% student satisfaction rate</li>
                                </ul>
                            </div>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="community-img">Community Partnership Event</span>
                        </div>
                    </div>

                    <!-- Future Directions -->
                    <div class="bg-primary text-white rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4" id="future-title">Future Directions</h3>
                        <p class="text-lg leading-relaxed" id="future-p">
                            Building on our success, we plan to expand the program to additional communities, integrate more advanced AI technologies, and develop a sustainable model for long-term impact. We're also exploring partnerships with international organizations to scale our innovative teaching approaches globally.
                        </p>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Current language
        let currentLang = 'en';

        // Content translations
        const translations = {
            en: {
                'main-title': 'Voluntary Teaching Project',
                'main-subtitle': 'HKBU LANG 2077 - Innovative Language Education Initiative',
                'nav-video': '📹 Video',
                'nav-contexts': '🎯 Teaching Contexts',
                'nav-ai': '🤖 AI Solutions',
                'nav-reflection': '💭 Sharing & Reflection',
                'video-title': 'Project Overview Video',
                'video-description': 'Watch our comprehensive overview of the voluntary teaching project and its impact on language education.',
                'contexts-title': 'Teaching Contexts, Challenges and Needs',
                'context1-title': 'Educational Context',
                'context1-p1': 'Our voluntary teaching project targets underserved communities where English language education resources are limited. We focus on providing quality language instruction to students who lack access to traditional educational opportunities.',
                'context1-p2': 'The project emphasizes practical communication skills, cultural exchange, and building confidence in English language use through interactive and engaging teaching methodologies.',
                'context1-img': 'Teaching Context Image',
                'context2-title': 'Key Challenges',
                'context2-img': 'Challenges Overview',
                'challenge1': 'Limited technological infrastructure in target communities',
                'challenge2': 'Diverse learning levels and backgrounds among students',
                'challenge3': 'Cultural and linguistic barriers affecting communication',
                'challenge4': 'Time constraints and scheduling difficulties',
                'context3-title': 'Community Needs',
                'context3-p1': 'Through community engagement and needs assessment, we identified critical gaps in English language education. Local communities expressed strong desire for practical English skills that could enhance employment opportunities and social mobility.',
                'context3-p2': 'The program addresses these needs through tailored curriculum design, flexible scheduling, and culturally sensitive teaching approaches that respect local contexts while promoting global communication skills.',
                'context3-img': 'Community Needs Assessment',
                'ai-title': 'AI-Powered Teaching Solutions',
                'ai-intro-title': 'Innovative AI Integration',
                'ai-intro-p': 'Our project leverages cutting-edge AI technology to enhance the teaching and learning experience. We\'ve developed customized chatbots and AI-assisted tools that provide personalized support to both teachers and students, making language learning more accessible and effective.',
                'chatbot-title': 'Custom AI Teaching Assistant',
                'chatbot-p': 'Our AI chatbot serves as a 24/7 teaching assistant, providing instant feedback on grammar, pronunciation, and vocabulary. It adapts to individual learning styles and progress levels, offering personalized exercises and explanations.',
                'chatbot-features-title': 'Key Features:',
                'feature1': '• Real-time grammar correction',
                'feature2': '• Pronunciation guidance',
                'feature3': '• Adaptive learning paths',
                'feature4': '• Progress tracking',
                'chatbot-img': 'AI Chatbot Interface Screenshot',
                'analytics-title': 'Learning Analytics Platform',
                'analytics-p': 'Advanced AI analytics help teachers understand student progress patterns, identify learning difficulties, and optimize teaching strategies. The platform provides detailed insights into student engagement and performance metrics.',
                'analytics-features-title': 'Analytics Features:',
                'analytics1': '• Student progress visualization',
                'analytics2': '• Learning pattern analysis',
                'analytics3': '• Predictive performance modeling',
                'analytics4': '• Customized intervention recommendations',
                'analytics-img': 'AI Analytics Dashboard',
                'impact-title': 'Implementation Impact',
                'impact-p': 'The integration of AI solutions has resulted in improved learning outcomes, increased student engagement, and more efficient teaching processes. Students report higher confidence levels and teachers benefit from data-driven insights to enhance their instructional methods.',
                'reflection-title': 'Sharing & Reflection',
                'outcomes-title': 'Project Outcomes',
                'outcomes-p1': 'Our voluntary teaching project has yielded significant positive outcomes for both students and teachers. Student engagement has increased by 40%, and standardized test scores have improved across all participating communities.',
                'outcomes-p2': 'The project has also enhanced the professional development of HKBU students, providing them with valuable real-world teaching experience and cross-cultural communication skills.',
                'outcomes-img': 'Student Success Stories',
                'teacher-title': 'Teacher Reflections',
                'teacher-quote': '"This project has transformed my understanding of language teaching. Working with diverse communities has made me a more adaptable and empathetic educator."',
                'teacher-p': 'Participating teachers report enhanced cultural sensitivity, improved classroom management skills, and a deeper appreciation for the challenges faced by English language learners in underserved communities.',
                'teacher-img': 'Teacher Feedback Session',
                'community-title': 'Community Impact',
                'community-p': 'The project has strengthened community bonds and created lasting partnerships between HKBU and local organizations. Community leaders have expressed enthusiasm for continuing the program and expanding its reach.',
                'impact-stats-title': 'Measurable Impact:',
                'stat1': '• 150+ students directly benefited',
                'stat2': '• 25 HKBU student teachers participated',
                'stat3': '• 8 community partnerships established',
                'stat4': '• 95% student satisfaction rate',
                'community-img': 'Community Partnership Event',
                'future-title': 'Future Directions',
                'future-p': 'Building on our success, we plan to expand the program to additional communities, integrate more advanced AI technologies, and develop a sustainable model for long-term impact. We\'re also exploring partnerships with international organizations to scale our innovative teaching approaches globally.'
            },
            zh: {
                'main-title': '志愿教学项目',
                'main-subtitle': '香港浸会大学 LANG 2077 - 创新语言教育倡议',
                'nav-video': '📹 视频',
                'nav-contexts': '🎯 教学背景',
                'nav-ai': '🤖 AI解决方案',
                'nav-reflection': '💭 分享与反思',
                'video-title': '项目概述视频',
                'video-description': '观看我们志愿教学项目的全面概述及其对语言教育的影响。',
                'contexts-title': '教学背景、挑战与需求',
                'context1-title': '教育背景',
                'context1-p1': '我们的志愿教学项目针对英语教育资源有限的服务不足社区。我们专注于为缺乏传统教育机会的学生提供优质的语言教学。',
                'context1-p2': '该项目强调实用沟通技能、文化交流，并通过互动和引人入胜的教学方法建立使用英语的信心。',
                'context1-img': '教学背景图片',
                'context2-title': '主要挑战',
                'context2-img': '挑战概述',
                'challenge1': '目标社区技术基础设施有限',
                'challenge2': '学生学习水平和背景多样化',
                'challenge3': '影响沟通的文化和语言障碍',
                'challenge4': '时间限制和日程安排困难',
                'context3-title': '社区需求',
                'context3-p1': '通过社区参与和需求评估，我们确定了英语教育的关键差距。当地社区强烈希望获得能够增强就业机会和社会流动性的实用英语技能。',
                'context3-p2': '该计划通过量身定制的课程设计、灵活的时间安排和文化敏感的教学方法来解决这些需求，这些方法既尊重当地环境又促进全球沟通技能。',
                'context3-img': '社区需求评估',
                'ai-title': 'AI驱动的教学解决方案',
                'ai-intro-title': '创新AI集成',
                'ai-intro-p': '我们的项目利用前沿AI技术来增强教学和学习体验。我们开发了定制的聊天机器人和AI辅助工具，为教师和学生提供个性化支持，使语言学习更加便捷和有效。',
                'chatbot-title': '定制AI教学助手',
                'chatbot-p': '我们的AI聊天机器人作为全天候教学助手，提供语法、发音和词汇的即时反馈。它适应个人学习风格和进度水平，提供个性化的练习和解释。',
                'chatbot-features-title': '主要功能：',
                'feature1': '• 实时语法纠正',
                'feature2': '• 发音指导',
                'feature3': '• 适应性学习路径',
                'feature4': '• 进度跟踪',
                'chatbot-img': 'AI聊天机器人界面截图',
                'analytics-title': '学习分析平台',
                'analytics-p': '先进的AI分析帮助教师了解学生的进步模式，识别学习困难，并优化教学策略。该平台提供学生参与度和表现指标的详细见解。',
                'analytics-features-title': '分析功能：',
                'analytics1': '• 学生进度可视化',
                'analytics2': '• 学习模式分析',
                'analytics3': '• 预测性能建模',
                'analytics4': '• 定制干预建议',
                'analytics-img': 'AI分析仪表板',
                'impact-title': '实施影响',
                'impact-p': 'AI解决方案的集成已经带来了更好的学习成果、增加的学生参与度和更高效的教学过程。学生报告信心水平提高，教师受益于数据驱动的见解来增强他们的教学方法。',
                'reflection-title': '分享与反思',
                'outcomes-title': '项目成果',
                'outcomes-p1': '我们的志愿教学项目为学生和教师都产生了显著的积极成果。学生参与度提高了40%，所有参与社区的标准化考试成绩都有所提高。',
                'outcomes-p2': '该项目还增强了香港浸会大学学生的专业发展，为他们提供了宝贵的真实世界教学经验和跨文化沟通技能。',
                'outcomes-img': '学生成功故事',
                'teacher-title': '教师反思',
                'teacher-quote': '"这个项目改变了我对语言教学的理解。与不同社区的合作使我成为了一个更具适应性和同理心的教育者。"',
                'teacher-p': '参与的教师报告了增强的文化敏感性、改进的课堂管理技能，以及对服务不足社区英语学习者面临挑战的更深层次理解。',
                'teacher-img': '教师反馈会议',
                'community-title': '社区影响',
                'community-p': '该项目加强了社区联系，并在香港浸会大学和当地组织之间建立了持久的伙伴关系。社区领导人对继续该计划并扩大其影响力表示热情。',
                'impact-stats-title': '可衡量的影响：',
                'stat1': '• 150+名学生直接受益',
                'stat2': '• 25名香港浸会大学学生教师参与',
                'stat3': '• 建立了8个社区伙伴关系',
                'stat4': '• 95%的学生满意度',
                'community-img': '社区伙伴关系活动',
                'future-title': '未来方向',
                'future-p': '基于我们的成功，我们计划将项目扩展到更多社区，集成更先进的AI技术，并开发可持续的长期影响模式。我们还在探索与国际组织的伙伴关系，以在全球范围内推广我们的创新教学方法。'
            }
        };

        // Language switching function - defined globally
        function switchLanguage(lang) {
            currentLang = lang;
            
            // Update language buttons
            document.getElementById('lang-en').className = lang === 'en' 
                ? 'px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white'
                : 'px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary';
            
            document.getElementById('lang-zh').className = lang === 'zh' 
                ? 'px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white'
                : 'px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary';
            
            // Update content
            for (const [id, text] of Object.entries(translations[lang])) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = text;
                }
            }
            
            // Update video iframe
            const videoContainer = document.getElementById('video-container');
            if (lang === 'zh') {
                videoContainer.innerHTML = `
                    <div class="aspect-video w-full">
                        <iframe 
                            src="https://player.bilibili.com/player.html?bvid=BV1SNGyehETm&high_quality=1&danmaku=0" 
                            width="100%" 
                            height="100%" 
                            scrolling="no" 
                            frameborder="no" 
                            framespacing="0" 
                            allowfullscreen="true"
                            class="rounded-lg">
                        </iframe>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center mt-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">如果视频无法正常播放，请点击下方按钮</p>
                        <a href="https://www.bilibili.com/video/BV1SNGyehETm/" 
                           target="_blank" 
                           rel="noopener noreferrer"
                           class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors text-sm">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"/>
                                <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-1a1 1 0 10-2 0v1H5V7h1a1 1 0 000-2H5z"/>
                            </svg>
                            在 Bilibili 观看
                        </a>
                    </div>
                `;
            } else {
                videoContainer.innerHTML = `
                    <iframe 
                        width="100%" 
                        height="100%" 
                        src="https://www.youtube.com/embed/o8NPllzkFhE?start=1118" 
                        title="Voluntary Teaching Project Video" 
                        frameborder="0" 
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                        allowfullscreen
                        class="rounded-lg aspect-video">
                    </iframe>
                `;
            }
        }

        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Scroll to section function
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update active tab
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Update active tab based on scroll position
            const sections = document.querySelectorAll('.section');
            const navButtons = document.querySelectorAll('.tab-button');
            
            function updateActiveTab() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 120;
                    if (window.scrollY >= sectionTop) {
                        current = section.getAttribute('id');
                    }
                });
                
                navButtons.forEach((button, index) => {
                    button.classList.remove('active');
                    const buttonSections = ['video', 'contexts', 'ai-solutions', 'reflection'];
                    if (buttonSections[index] === current) {
                        button.classList.add('active');
                    }
                });
            }
            
            window.addEventListener('scroll', updateActiveTab);
            updateActiveTab();
            
            // Set first tab as active initially
            if (navButtons.length > 0) {
                navButtons[0].classList.add('active');
            }
        });
    </script>
</body>
</html>