<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voluntary Teaching Project - LANG 2077</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'bg-light': '#FFFFFF',
                        'bg-dark': '#181818'
                    }
                }
            }
        }
    </script>
    <style>
        .section {
            scroll-margin-top: 100px;
        }
        .tab-button.active {
            background-color: #5D5CDE;
            color: white;
        }
        .placeholder-img {
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .dark .placeholder-img {
            background: linear-gradient(45deg, #404040 25%, transparent 25%), 
                        linear-gradient(-45deg, #404040 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #404040 75%), 
                        linear-gradient(-45deg, transparent 75%, #404040 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
    </style>
</head>
<body class="bg-bg-light dark:bg-bg-dark text-gray-900 dark:text-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-primary mb-2">Voluntary Teaching Project</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400">HKBU LANG 2077 - Innovative Language Education Initiative</p>
        </div>

        <!-- Navigation Tabs -->
        <nav class="sticky top-0 bg-bg-light dark:bg-bg-dark z-10 pb-4">
            <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700 mb-8">
                <button onclick="scrollToSection('video')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors">
                    📹 Video
                </button>
                <button onclick="scrollToSection('contexts')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors">
                    🎯 Teaching Contexts
                </button>
                <button onclick="scrollToSection('ai-solutions')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors">
                    🤖 AI Solutions
                </button>
                <button onclick="scrollToSection('reflection')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors">
                    💭 Sharing &amp; Reflection
                </button>
            </div>
        </nav>

        <!-- Content Sections -->
        <div class="space-y-16">
            <!-- Video Section -->
            <section id="video" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-6 text-center">Project Overview Video</h2>
                    <div class="aspect-video max-w-4xl mx-auto">
                        <iframe width="100%" height="100%" src="https://www.youtube.com/embed/o8NPllzkFhE?start=1118" title="Voluntary Teaching Project Video" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" class="rounded-lg">
                        </iframe>
                    </div>
                    <p class="text-center text-gray-600 dark:text-gray-400 mt-4">
                        Watch our comprehensive overview of the voluntary teaching project and its impact on language education.
                    </p>
                </div>
            </section>

            <!-- Teaching Contexts Section -->
            <section id="contexts" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center">Teaching Contexts, Challenges and Needs</h2>
                    
                    <!-- Context 1 -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary">Educational Context</h3>
                            <p class="text-lg leading-relaxed mb-4">
                                Our voluntary teaching project targets underserved communities where English language education resources are limited. We focus on providing quality language instruction to students who lack access to traditional educational opportunities.
                            </p>
                            <p class="text-lg leading-relaxed">
                                The project emphasizes practical communication skills, cultural exchange, and building confidence in English language use through interactive and engaging teaching methodologies.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium">Teaching Context Image</span>
                        </div>
                    </div>

                    <!-- Context 2 -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center order-2 md:order-1">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium">Challenges Overview</span>
                        </div>
                        <div class="order-1 md:order-2">
                            <h3 class="text-2xl font-semibold mb-4 text-primary">Key Challenges</h3>
                            <ul class="text-lg leading-relaxed space-y-2">
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    Limited technological infrastructure in target communities
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    Diverse learning levels and backgrounds among students
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    Cultural and linguistic barriers affecting communication
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    Time constraints and scheduling difficulties
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Context 3 -->
                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary">Community Needs</h3>
                            <p class="text-lg leading-relaxed mb-4">
                                Through community engagement and needs assessment, we identified critical gaps in English language education. Local communities expressed strong desire for practical English skills that could enhance employment opportunities and social mobility.
                            </p>
                            <p class="text-lg leading-relaxed">
                                The program addresses these needs through tailored curriculum design, flexible scheduling, and culturally sensitive teaching approaches that respect local contexts while promoting global communication skills.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium">Community Needs Assessment</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI Solutions Section -->
            <section id="ai-solutions" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center">AI-Powered Teaching Solutions</h2>
                    
                    <!-- AI Introduction -->
                    <div class="mb-12">
                        <h3 class="text-2xl font-semibold mb-4 text-primary">Innovative AI Integration</h3>
                        <p class="text-lg leading-relaxed mb-6">
                            Our project leverages cutting-edge AI technology to enhance the teaching and learning experience. We've developed customized chatbots and AI-assisted tools that provide personalized support to both teachers and students, making language learning more accessible and effective.
                        </p>
                    </div>

                    <!-- Customized Chatbot -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary">Custom AI Teaching Assistant</h3>
                            <p class="text-lg leading-relaxed mb-4">
                                Our AI chatbot serves as a 24/7 teaching assistant, providing instant feedback on grammar, pronunciation, and vocabulary. It adapts to individual learning styles and progress levels, offering personalized exercises and explanations.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2">Key Features:</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• Real-time grammar correction</li>
                                    <li>• Pronunciation guidance</li>
                                    <li>• Adaptive learning paths</li>
                                    <li>• Progress tracking</li>
                                </ul>
                            </div>
                        </div>
                        <div class="placeholder-img rounded-lg h-80 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium">AI Chatbot Interface Screenshot</span>
                        </div>
                    </div>

                    <!-- AI Tools -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-80 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium">AI Analytics Dashboard</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary">Learning Analytics Platform</h3>
                            <p class="text-lg leading-relaxed mb-4">
                                Advanced AI analytics help teachers understand student progress patterns, identify learning difficulties, and optimize teaching strategies. The platform provides detailed insights into student engagement and performance metrics.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2">Analytics Features:</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• Student progress visualization</li>
                                    <li>• Learning pattern analysis</li>
                                    <li>• Predictive performance modeling</li>
                                    <li>• Customized intervention recommendations</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Implementation Results -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-primary">Implementation Impact</h3>
                        <p class="text-lg leading-relaxed">
                            The integration of AI solutions has resulted in improved learning outcomes, increased student engagement, and more efficient teaching processes. Students report higher confidence levels and teachers benefit from data-driven insights to enhance their instructional methods.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Sharing & Reflection Section -->
            <section id="reflection" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center">Sharing &amp; Reflection</h2>
                    
                    <!-- Project Outcomes -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary">Project Outcomes</h3>
                            <p class="text-lg leading-relaxed mb-4">
                                Our voluntary teaching project has yielded significant positive outcomes for both students and teachers. Student engagement has increased by 40%, and standardized test scores have improved across all participating communities.
                            </p>
                            <p class="text-lg leading-relaxed">
                                The project has also enhanced the professional development of HKBU students, providing them with valuable real-world teaching experience and cross-cultural communication skills.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium">Student Success Stories</span>
                        </div>
                    </div>

                    <!-- Teacher Reflections -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium">Teacher Feedback Session</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary">Teacher Reflections</h3>
                            <blockquote class="text-lg italic mb-4 border-l-4 border-primary pl-4">
                                "This project has transformed my understanding of language teaching. Working with diverse communities has made me a more adaptable and empathetic educator."
                            </blockquote>
                            <p class="text-lg leading-relaxed">
                                Participating teachers report enhanced cultural sensitivity, improved classroom management skills, and a deeper appreciation for the challenges faced by English language learners in underserved communities.
                            </p>
                        </div>
                    </div>

                    <!-- Community Impact -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary">Community Impact</h3>
                            <p class="text-lg leading-relaxed mb-4">
                                The project has strengthened community bonds and created lasting partnerships between HKBU and local organizations. Community leaders have expressed enthusiasm for continuing the program and expanding its reach.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2">Measurable Impact:</h4>
                                <ul class="space-y-1">
                                    <li>• 150+ students directly benefited</li>
                                    <li>• 25 HKBU student teachers participated</li>
                                    <li>• 8 community partnerships established</li>
                                    <li>• 95% student satisfaction rate</li>
                                </ul>
                            </div>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium">Community Partnership Event</span>
                        </div>
                    </div>

                    <!-- Future Directions -->
                    <div class="bg-primary text-white rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4">Future Directions</h3>
                        <p class="text-lg leading-relaxed">
                            Building on our success, we plan to expand the program to additional communities, integrate more advanced AI technologies, and develop a sustainable model for long-term impact. We're also exploring partnerships with international organizations to scale our innovative teaching approaches globally.
                        </p>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Scroll to section function
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update active tab
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
            }
        }

        // Smooth scrolling for navigation
        document.addEventListener('DOMContentLoaded', function() {
            // Update active tab based on scroll position
            const sections = document.querySelectorAll('.section');
            const navButtons = document.querySelectorAll('.tab-button');
            
            function updateActiveTab() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 120;
                    if (window.scrollY >= sectionTop) {
                        current = section.getAttribute('id');
                    }
                });
                
                navButtons.forEach((button, index) => {
                    button.classList.remove('active');
                    const buttonSections = ['video', 'contexts', 'ai-solutions', 'reflection'];
                    if (buttonSections[index] === current) {
                        button.classList.add('active');
                    }
                });
            }
            
            window.addEventListener('scroll', updateActiveTab);
            updateActiveTab(); // Initialize
            
            // Set first tab as active initially
            if (navButtons.length > 0) {
                navButtons[0].classList.add('active');
            }
        });
    </script>


</body></html>