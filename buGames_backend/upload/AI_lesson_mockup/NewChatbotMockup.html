<!DOCTYPE html>

<html lang="en">

<head>

  <meta charset="UTF-8">

  <title>Bytewise Enhanced Student Assignment</title>

  <style>

    body { font-family: Arial, sans-serif; background: #f7f7fb; margin: 0; padding: 0; }

    .container { max-width: 750px; margin: 40px auto; background: #fff; padding: 30px 40px; border-radius: 8px; box-shadow: 0 2px 16px rgba(0,0,0,0.07);}

    h2 { color: #334e68; }

    h3 { margin-bottom: 6px; }

    .section { margin-bottom: 36px; }

    .feedback { color: #388e3c; margin-top: 8px; }

    .note { background: #fffbe6; padding: 12px; border-left: 4px solid #ffecb3; margin-bottom: 18px; }

    .chatbot-suggestion, .ai-feedback {

      background: #e3f2fd;

      padding: 16px;

      border-radius: 5px;

      margin-top: 12px;

    }

    .toggle-btn { background: none; color: #1976d2; border: none; cursor: pointer; margin-left: 8px; text-decoration: underline;}

    .hidden { display: none; }

    .show { display: block; }

    button {

      padding: 8px 20px;

      background: #1976d2;

      color: #fff;

      border: none;

      border-radius: 4px;

      cursor: pointer;

      margin-top: 10px;

    }

    button:disabled { background: #b0bec5; }

    label { display: block; margin-top: 15px; }

    .matching-list { list-style: none; padding: 0; }

    .matching-list li { margin-bottom: 8px; }

    textarea, input[type="text"] { width: 100%; min-height: 40px; margin-top: 8px; padding: 6px; }

    textarea { min-height: 80px; }

    .resource-link { color: #1976d2; text-decoration: underline; }

  </style>

</head>

<body>

  <div class="container">


    <h2>Student Assignment</h2>


    <!-- OBJECTIVE TASKS SECTION -->

    <div class="section" id="mc-section">

      <h3>Step 1: Warm-up Tasks</h3>

      <!-- Explanatory Note for Teachers and Students -->

      <div class="note">

        <strong>Note:</strong> 

        <ul>

          <li>Correct answers and explanations are preloaded by the teacher in advance. Students will get instant feedback after submitting each task.</li>

          <li>These warm-up tasks help set the scene for more advanced practice and ensure students have the base knowledge before moving forward.</li>

        </ul>

      </div>

      <form id="mc-form">

        <label>

          1. What is the capital of France?

          <br>

          <input type="radio" name="q1" value="A"> A) Berlin

          <input type="radio" name="q1" value="B"> B) Paris

          <input type="radio" name="q1" value="C"> C) Madrid

        </label>

        <div class="feedback" id="mcq-feedback"></div>

        <label style="margin-top:20px;">

          2. Match the following:

        </label>

        <ul class="matching-list">

          <li>

            <select id="match1">

              <option value="">--Select--</option>

              <option value="Dog">Dog</option>

              <option value="Cat">Cat</option>

            </select>

            &nbsp;is to Bark as Cat is to

            <select id="match2">

              <option value="">--Select--</option>

              <option value="Meow">Meow</option>

              <option value="Roar">Roar</option>

            </select>

          </li>

        </ul>

        <button type="button" onclick="gradeMC()">Check Answers</button>

      </form>

      <div class="feedback" id="match-feedback"></div>

    </div>


    <!-- SUBJECTIVE TASK SECTION -->

    <div class="section" id="subjective-section">

      <h3>Step 2: Advanced Practice - Subjective Task</h3>

      <!-- Toggle for additional instructions/resources -->

      <button class="toggle-btn" type="button" onclick="toggleInstructions()">Show/Hide Task Instructions & Resources</button>

      <div class="note hidden" id="instructions-note">

        <strong>Instructions:</strong>

        <ul>

          <li>Write about why Paris is an important city in European history.</li>

          <li>Consider its political, cultural, and historical relevance.</li>

          <li>Refer to the following resources for more information:</li>

          <ul>

            <li><a class="resource-link" href="https://en.wikipedia.org/wiki/History_of_Paris" target="_blank">History of Paris (Wikipedia)</a></li>

            <li><a class="resource-link" href="https://www.britannica.com/place/Paris" target="_blank">Paris (Britannica)</a></li>

          </ul>

        </ul>

      </div>


      <!-- Multiple fields for gathering info -->

      <form id="subjective-form">

        <label>

          <strong>1. Main Topic:</strong>

          <input type="text" id="main-topic" placeholder="E.g., Paris as a center of revolution">

        </label>

        <label>

          <strong>2. Key Event(s):</strong>

          <input type="text" id="key-events" placeholder="E.g., The French Revolution, Treaty of Paris">

        </label>

        <label>

          <strong>3. Important Figures:</strong>

          <input type="text" id="figures" placeholder="E.g., Napoleon, Louis XVI">

        </label>

        <label>

          <strong>4. Cultural Impact:</strong>

          <input type="text" id="culture" placeholder="E.g., Art, philosophy, literature">

        </label>

        <button type="button" id="submit-info" onclick="submitInfo()">Get AI Outline & Suggestions</button>

      </form>


      <!-- AI Outline appears here after student submits info -->

      <div class="chatbot-suggestion hidden" id="ai-suggestion">

        <!-- AI outline goes here -->

      </div>


      <!-- Field for full answer, appears after AI suggestion -->

      <div class="hidden" id="full-answer-section">

        <label>

          <strong>Write Your Full Answer:</strong>

          <textarea id="full-answer" placeholder="Now write your full answer here, using the outline and suggestions above."></textarea>

        </label>

        <button type="button" onclick="submitFullAnswer()">Submit Full Answer for Feedback</button>

      </div>


      <!-- AI Feedback on full answer, appears after submission -->

      <div class="ai-feedback hidden" id="ai-feedback">

        <!-- AI feedback goes here -->

      </div>

    </div>

  </div>


  <!-- JS for UI/Workflow simulation -->

  <script>

    // OBJECTIVE TASKS FEEDBACK

    function gradeMC() {

      let q1 = document.querySelector('input[name="q1"]:checked');

      let match1 = document.getElementById('match1').value;

      let match2 = document.getElementById('match2').value;

      let mcqFeedback = '';

      let matchFeedback = '';

      // MCQ

      if (q1 && q1.value === 'B') {

        mcqFeedback = 'Correct! Paris is the capital of France.<br><em>Explanation: Paris has been the political and cultural capital of France for centuries.</em>';

      } else {

        mcqFeedback = 'Incorrect. The correct answer is Paris.<br><em>Explanation: Paris is the capital and largest city of France.</em>';

      }

      document.getElementById('mcq-feedback').innerHTML = mcqFeedback;

      // Matching

      if (match1 === 'Dog' && match2 === 'Meow') {

        matchFeedback = 'Correct! Dog is to Bark, Cat is to Meow.';

      } else if (match1 && match2) {

        matchFeedback = 'Try again. Hint: Dogs bark and cats meow.';

      } else {

        matchFeedback = '';

      }

      document.getElementById('match-feedback').innerText = matchFeedback;

    }


    // TOGGLE INSTRUCTIONS

    function toggleInstructions() {

      var node = document.getElementById('instructions-note');

      if (node.classList.contains('hidden')) {

        node.classList.remove('hidden');

        node.classList.add('show');

      } else {

        node.classList.add('hidden');

        node.classList.remove('show');

      }

    }


    // SUBJECTIVE TASK: GATHER INFO, SHOW AI OUTLINE

    function submitInfo() {

      // Get values

      let topic = document.getElementById('main-topic').value.trim();

      let events = document.getElementById('key-events').value.trim();

      let figures = document.getElementById('figures').value.trim();

      let culture = document.getElementById('culture').value.trim();

      if (!topic || !events || !figures || !culture) {

        alert('Please fill in all fields before proceeding.');

        return;

      }

      // Simulated AI outline, using info provided

      let aiOutline = `

        <strong>AI-Generated Outline:</strong>

        <ul>

          <li><strong>Topic:</strong> ${topic}</li>

          <li><strong>Key Event(s):</strong> ${events}</li>

          <li><strong>Important Figures:</strong> ${figures}</li>

          <li><strong>Cultural Impact:</strong> ${culture}</li>

        </ul>

        <em>Suggestion:</em> Use this outline to structure your full answer. Include examples and connect the events and people to the broader context of European history.

      `;

      // Show the suggestion and the full answer box

      document.getElementById('ai-suggestion').innerHTML = aiOutline;

      document.getElementById('ai-suggestion').classList.remove('hidden');

      document.getElementById('full-answer-section').classList.remove('hidden');

      // Hide previous feedback if any

      document.getElementById('ai-feedback').classList.add('hidden');

    }


    // SUBJECTIVE TASK: FULL ANSWER AND AI FEEDBACK

    function submitFullAnswer() {

      let answer = document.getElementById('full-answer').value.trim();

      if (answer.length < 40) {

        alert('Please write a more detailed answer.');

        return;

      }

      // Simulated AI feedback

      let feedback = `

        <strong>AI Feedback:</strong>

        <ul>

          <li>Your answer covers the main points from the outline well.</li>

          <li>Consider adding more analysis about how the key events influenced European culture.</li>

          <li>Good use of historical figures. Try to connect them more explicitly to the events.</li>

          <li>Well done! See if you can add one or two quotes or specific examples.</li>

        </ul>

      `;

      document.getElementById('ai-feedback').innerHTML = feedback;

      document.getElementById('ai-feedback').classList.remove('hidden');

    }

  </script>

</body>

</html>
