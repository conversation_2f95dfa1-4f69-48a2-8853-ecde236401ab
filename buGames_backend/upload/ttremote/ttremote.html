<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Game</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f0f0f0;
        }
        .game-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>Simple Number Guessing Game</h1>
        <p>I'm thinking of a number between 1 and 100. Can you guess it?</p>
        <input type="number" id="guessInput" placeholder="Enter your guess" min="1" max="100">
        <button onclick="checkGuess()">Guess!</button>
        <button onclick="resetGame()">New Game</button>
        <div id="result"></div>
        <div id="attempts"></div>
    </div>

    <script>
        let randomNumber = Math.floor(Math.random() * 100) + 1;
        let attempts = 0;

        function checkGuess() {
            const guess = parseInt(document.getElementById('guessInput').value);
            const result = document.getElementById('result');
            const attemptsDiv = document.getElementById('attempts');
            
            if (isNaN(guess) || guess < 1 || guess > 100) {
                result.textContent = 'Please enter a valid number between 1 and 100!';
                result.style.color = 'red';
                return;
            }
            
            attempts++;
            
            if (guess === randomNumber) {
                result.textContent = `Congratulations! You guessed it in ${attempts} attempts!`;
                result.style.color = 'green';
            } else if (guess < randomNumber) {
                result.textContent = 'Too low! Try a higher number.';
                result.style.color = 'orange';
            } else {
                result.textContent = 'Too high! Try a lower number.';
                result.style.color = 'orange';
            }
            
            attemptsDiv.textContent = `Attempts: ${attempts}`;
            document.getElementById('guessInput').value = '';
        }

        function resetGame() {
            randomNumber = Math.floor(Math.random() * 100) + 1;
            attempts = 0;
            document.getElementById('result').textContent = '';
            document.getElementById('attempts').textContent = '';
            document.getElementById('guessInput').value = '';
        }

        // Allow Enter key to submit guess
        document.getElementById('guessInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                checkGuess();
            }
        });
    </script>
</body>
</html>
