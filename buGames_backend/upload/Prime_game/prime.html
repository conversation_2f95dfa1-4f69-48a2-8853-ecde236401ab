
<!DOCTYPE html>

<html>

<head>

    <title>Prime Number Quiz</title>

    <meta charset="UTF-8">

    <style>

        body {

            font-family: 'Comic Sans MS', cursive, sans-serif;

            background: linear-gradient(120deg, #f9d423 0%, #ff4e50 100%);

            text-align: center;

            margin-top: 40px;

            color: #222;

        }

        .container {

            background: #fffbe7;

            border-radius: 20px;

            display: inline-block;

            padding: 30px 40px;

            box-shadow: 0 0 20px #f9d42388;

        }

        .question {

            font-size: 2em;

            margin: 20px;

            color: #ff4e50;

        }

        .feedback {

            font-size: 1.2em;

            margin: 18px;

            font-weight: bold;

        }

        .feedback.correct {

            color: #14b814;

        }

        .feedback.incorrect {

            color: #e82e2e;

        }

        button {

            font-size: 1.2em;

            margin: 10px 25px;

            padding: 12px 40px;

            border: none;

            border-radius: 8px;

            background: #f9d423;

            color: #fff;

            font-weight: bold;

            cursor: pointer;

            transition: background 0.2s;

        }

        button:hover {

            background: #ff4e50;

        }

        .score {

            margin-top: 20px;

            padding: 10px 0;

            background: #c6ffdd;

            border-radius: 10px;

            font-size: 1.1em;

            color: #222;

            box-shadow: 0 0 8px #c6ffdd88;

        }

    </style>

</head>

<body>

    <div class="container">

        <h1>Prime Number Quiz</h1>

        <div class="question" id="question"></div>

        <button onclick="checkAnswer(true)">Yes</button>

        <button onclick="checkAnswer(false)">No</button>

        <div class="feedback" id="feedback"></div>

        <div class="score" id="score"></div>

    </div>

    <script>

        let score = 0;

        let total = 0;

        let currentNumber = 0;


        function isPrime(n) {

            if (n < 2) return false;

            for (let i = 2; i <= Math.sqrt(n); i++) {

                if (n % i === 0) return false;

            }

            return true;

        }


        function getFactors(n) {

            let factors = [];

            for (let i=1; i<=n; i++) {

                if (n % i === 0) factors.push(i);

            }

            return factors;

        }


        function nextQuestion() {

            currentNumber = Math.floor(Math.random() * 50) + 1; // 1-50

            document.getElementById('question').innerText = `Is ${currentNumber} a prime number?`;

            document.getElementById('feedback').innerText = '';

            document.getElementById('feedback').className = "feedback";

        }


        function checkAnswer(userAnswer) {

            total++;

            let correct = isPrime(currentNumber);

            let feedback = '';

            let factors = getFactors(currentNumber);

            if (userAnswer === correct) {

                score++;

                feedback = `<span>✅ Correct!</span><br>`;

                feedback += correct

                    ? `${currentNumber} is a <b>prime number</b> because it has only two factors: 1 and ${currentNumber}.`

                    : `${currentNumber} is <b>not a prime number</b> because it can be divided by: ${factors.join(', ')}.`;

                document.getElementById('feedback').className = "feedback correct";

            } else {

                feedback = `<span>❌ Oops!</span><br>`;

                feedback += correct

                    ? `The correct answer is <b>Yes</b>.<br>${currentNumber} is a <b>prime number</b> because it has only two factors: 1 and ${currentNumber}.`

                    : `The correct answer is <b>No</b>.<br>${currentNumber} is <b>not a prime number</b> because it can be divided by: ${factors.join(', ')}.`;

                document.getElementById('feedback').className = "feedback incorrect";

            }

            document.getElementById('feedback').innerHTML = feedback;

            document.getElementById('score').innerText = `Score: ${score} / ${total}`;

            setTimeout(nextQuestion, 2500);

        }


        // Start the quiz

        nextQuestion();

    </script>

</body>

</html>

