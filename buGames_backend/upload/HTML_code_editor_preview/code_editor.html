<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Editor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#722ED1',
                        dark: '#1D2129',
                        light: '#F2F3F5',
                        success: '#00B42A',
                        warning: '#FF7D00',
                        danger: '#F53F3F',
                        editor: '#011627',
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                        code: ['Fira Code', 'monospace'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .scrollbar-thin {
                scrollbar-width: thin;
            }
            .scrollbar-thin::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }
            .scrollbar-thin::-webkit-scrollbar-thumb {
                background-color: rgba(156, 163, 175, 0.5);
                border-radius: 3px;
            }
            .editor-line-number {
                @apply text-gray-400 pr-4 select-none text-right w-8;
            }
            .tab-active {
                @apply bg-primary text-white font-medium;
            }
            .btn-hover {
                @apply hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200;
            }
        }
    </style>
</head>
<body class="font-inter bg-gray-50 text-dark min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white shadow-sm z-10">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <i class="fa fa-code text-primary text-2xl"></i>
                <h1 class="text-xl font-bold text-dark">Code<span class="text-primary">Preview</span></h1>
            </div>
            <div class="flex items-center space-x-4">
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="fa fa-moon-o text-gray-600"></i>
                </button>
                <button id="help-btn" class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="fa fa-question-circle text-gray-600"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 container mx-auto px-4 py-6">
        <!-- Toolbar -->
        <div class="bg-white rounded-lg shadow-sm mb-6 p-3 flex flex-wrap items-center justify-between">
            <div class="flex items-center space-x-2 mb-2 sm:mb-0">
                <button id="run-btn" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg flex items-center btn-hover">
                    <i class="fa fa-play mr-2"></i>
                    <span>Run</span>
                </button>
                <button id="reset-btn" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center btn-hover">
                    <i class="fa fa-refresh mr-2"></i>
                    <span>Reset</span>
                </button>
                <button id="fullscreen-btn" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center btn-hover">
                    <i class="fa fa-expand mr-2"></i>
                    <span>Fullscreen</span>
                </button>
            </div>
            <div class="flex items-center space-x-2">
                <div class="text-sm text-gray-500">
                    <span id="status-text">Ready</span>
                    <i id="status-icon" class="fa fa-check-circle text-success ml-1"></i>
                </div>
            </div>
        </div>

        <!-- Editor and Preview Area -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Code Editor -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="bg-gray-50 border-b border-gray-200 flex">
                    <button class="editor-tab tab-active px-4 py-2 text-sm font-medium" data-editor="mixed">HTML & JavaScript</button>
                </div>
                <div class="relative h-[500px] overflow-hidden">
                    <div id="mixed-editor" class="editor-pane absolute inset-0 overflow-auto">
                        <div class="flex h-full">
                            <div class="bg-gray-800 p-2 text-xs font-code editor-line-number">
                                <div>1</div>
                                <div>2</div>
                                <div>3</div>
                                <div>4</div>
                                <div>5</div>
                                <div>6</div>
                                <div>7</div>
                                <div>8</div>
                                <div>9</div>
                                <div>10</div>
                                <div>11</div>
                                <div>12</div>
                                <div>13</div>
                                <div>14</div>
                                <div>15</div>
                                <div>16</div>
                                <div>17</div>
                                <div>18</div>
                                <div>19</div>
                                <div>20</div>
                                <div>21</div>
                                <div>22</div>
                                <div>23</div>
                                <div>24</div>
                                <div>25</div>
                                <div>26</div>
                                <div>27</div>
                                <div>28</div>
                                <div>29</div>
                                <div>30</div>
                                <div>31</div>
                                <div>32</div>
                                <div>33</div>
                                <div>34</div>
                                <div>35</div>
                                <div>36</div>
                                <div>37</div>
                                <div>38</div>
                                <div>39</div>
                                <div>40</div>
                                <div>41</div>
                                <div>42</div>
                                <div>43</div>
                                <div>44</div>
                                <div>45</div>
                                <div>46</div>
                                <div>47</div>
                                <div>48</div>
                                <div>49</div>
                                <div>50</div>
                            </div>
                            <textarea id="mixed-code" class="flex-1 bg-editor text-gray-100 font-code p-4 w-full resize-none focus:outline-none text-sm" rows="50" placeholder="Type your mixed HTML and JavaScript code here...">&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;My Page&lt;/title&gt;
    &lt;style&gt;
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #165DFF;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0E42B3;
        }
        .message {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="container"&gt;
        &lt;h1&gt;Hello, World!&lt;/h1&gt;
        &lt;p&gt;This is a sample page with mixed HTML and JavaScript.&lt;/p&gt;
        &lt;button id="myButton" class="btn"&gt;Click Me&lt;/button&gt;
        &lt;div id="output"&gt;&lt;/div&gt;
        
        &lt;!-- Chart container --&gt;
        &lt;div class="mt-6"&gt;
            &lt;h3&gt;Sample Chart&lt;/h3&gt;
            &lt;canvas id="myChart" width="400" height="200"&gt;&lt;/canvas&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;script&gt;
        // Wait for the DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Get references to DOM elements
            const button = document.getElementById('myButton');
            const output = document.getElementById('output');
            
            // Add click event listener to the button
            button.addEventListener('click', function() {
                // Create a new message element
                const message = document.createElement('div');
                message.className = 'message';
                
                // Get current date and time
                const now = new Date();
                const formattedDateTime = now.toLocaleString();
                
                // Set the content of the message
                message.innerHTML = `
                    &lt;p&gt;Button clicked at: ${formattedDateTime}&lt;/p&gt;
                    &lt;p&gt;You can create dynamic content with JavaScript!&lt;/p&gt;
                `;
                
                // Add the message to the output div
                output.appendChild(message);
                
                // Optional: Add a class for animation after a small delay
                setTimeout(() => {
                    message.classList.add('show');
                }, 10);
            });
            
            // Create a simple chart if Chart.js is available
            if (typeof Chart !== 'undefined') {
                const ctx = document.getElementById('myChart').getContext('2d');
                
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
                        datasets: [{
                            label: 'Sample Data',
                            data: [12, 19, 3, 5, 2, 3],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)',
                                'rgba(255, 159, 64, 0.7)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        });
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview Area -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="bg-gray-50 border-b border-gray-200 px-4 py-2 flex items-center justify-between">
                    <h3 class="font-medium text-gray-700">Preview</h3>
                    <div class="flex space-x-2">
                        <button id="refresh-preview" class="p-1.5 rounded hover:bg-gray-100 text-gray-600">
                            <i class="fa fa-refresh"></i>
                        </button>
                        <button id="open-new-tab" class="p-1.5 rounded hover:bg-gray-100 text-gray-600">
                            <i class="fa fa-external-link"></i>
                        </button>
                    </div>
                </div>
                <div class="relative h-[500px]">
                    <iframe id="preview-frame" class="w-full h-full border-0"></iframe>
                    <div id="loading-indicator" class="absolute inset-0 bg-white flex items-center justify-center hidden">
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
                            <p class="mt-3 text-gray-600">Loading preview...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Help Modal -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-dark">How to Use</h2>
                    <button id="close-help" class="text-gray-500 hover:text-gray-700">
                        <i class="fa fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <h3 class="font-semibold text-lg text-primary">Code Editor</h3>
                        <p class="text-gray-700">The editor allows you to write mixed HTML and JavaScript code in a single panel.</p>
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg text-primary">Toolbar</h3>
                        <ul class="list-disc pl-5 space-y-2 text-gray-700">
                            <li><span class="font-medium">Run:</span> Compiles and displays your code in the preview window.</li>
                            <li><span class="font-medium">Reset:</span> Resets the code editor to its initial state.</li>
                            <li><span class="font-medium">Fullscreen:</span> Opens the preview in fullscreen mode.</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg text-primary">Preview</h3>
                        <p class="text-gray-700">The preview window shows the result of your code. You can refresh it or open it in a new tab.</p>
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg text-primary">Tips</h3>
                        <ul class="list-disc pl-5 space-y-2 text-gray-700">
                            <li>Your JavaScript code should be placed inside &lt;script&gt; tags within your HTML.</li>
                            <li>The initial code includes a basic structure to get you started.</li>
                            <li>You can use modern JavaScript features as the preview uses the latest browser capabilities.</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="p-4 bg-gray-50 flex justify-end">
                <button id="got-it" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg btn-hover">
                    Got it!
                </button>
            </div>
        </div>
    </div>

    <!-- Fullscreen Preview Modal -->
    <div id="fullscreen-modal" class="fixed inset-0 bg-white z-50 hidden">
        <div class="flex flex-col h-full">
            <div class="bg-gray-50 border-b border-gray-200 px-4 py-2 flex items-center justify-between">
                <h3 class="font-medium text-gray-700">Fullscreen Preview</h3>
                <button id="exit-fullscreen" class="p-1.5 rounded hover:bg-gray-100 text-gray-600">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <iframe id="fullscreen-frame" class="flex-1 w-full border-0"></iframe>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-4 mt-8">
        <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
            <p>CodePreview &copy; 2023 | A modern code editor and preview tool</p>
        </div>
    </footer>

    <script>
        // DOM Elements
        const mixedEditor = document.getElementById('mixed-editor');
        const mixedCode = document.getElementById('mixed-code');
        const previewFrame = document.getElementById('preview-frame');
        const fullscreenFrame = document.getElementById('fullscreen-frame');
        const runBtn = document.getElementById('run-btn');
        const resetBtn = document.getElementById('reset-btn');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const refreshPreview = document.getElementById('refresh-preview');
        const openNewTab = document.getElementById('open-new-tab');
        const themeToggle = document.getElementById('theme-toggle');
        const helpBtn = document.getElementById('help-btn');
        const helpModal = document.getElementById('help-modal');
        const closeHelp = document.getElementById('close-help');
        const gotIt = document.getElementById('got-it');
        const fullscreenModal = document.getElementById('fullscreen-modal');
        const exitFullscreen = document.getElementById('exit-fullscreen');
        const statusText = document.getElementById('status-text');
        const statusIcon = document.getElementById('status-icon');
        const loadingIndicator = document.getElementById('loading-indicator');
        const editorTabs = document.querySelectorAll('.editor-tab');

        // Initial code
        const initialMixedCode = mixedCode.value;

        // Initialize
        function init() {
            // Set up event listeners
            runBtn.addEventListener('click', runCode);
            resetBtn.addEventListener('click', resetCode);
            fullscreenBtn.addEventListener('click', toggleFullscreen);
            refreshPreview.addEventListener('click', updatePreview);
            openNewTab.addEventListener('click', openInNewTab);
            themeToggle.addEventListener('click', toggleTheme);
            helpBtn.addEventListener('click', () => helpModal.classList.remove('hidden'));
            closeHelp.addEventListener('click', () => helpModal.classList.add('hidden'));
            gotIt.addEventListener('click', () => helpModal.classList.add('hidden'));
            exitFullscreen.addEventListener('click', () => fullscreenModal.classList.add('hidden'));
            
            // Update line numbers when scrolling
            mixedCode.addEventListener('scroll', syncLineNumbers);
            
            // Initialize preview
            updatePreview();
        }

        // Sync line numbers with scroll position
        function syncLineNumbers(e) {
            const textarea = e.target;
            const editorPane = textarea.parentElement.parentElement;
            const lineNumbers = editorPane.querySelector('.editor-line-number');
            
            lineNumbers.style.transform = `translateY(-${textarea.scrollTop}px)`;
        }

        // Update the preview
        function updatePreview() {
            showLoading(true);
            updateStatus('Compiling...', 'fa-circle-o-notch fa-spin', 'text-gray-500');
            
            setTimeout(() => {
                try {
                    const previewDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                    const fullscreenDoc = fullscreenFrame.contentDocument || fullscreenFrame.contentWindow.document;
                    
                    // Get the mixed code
                    const code = mixedCode.value;
                    
                    // Update both preview frames
                    previewDoc.open();
                    previewDoc.write(code);
                    previewDoc.close();
                    
                    fullscreenDoc.open();
                    fullscreenDoc.write(code);
                    fullscreenDoc.close();
                    
                    updateStatus('Preview updated', 'fa-check-circle', 'text-success');
                } catch (error) {
                    console.error('Error updating preview:', error);
                    updateStatus('Error: ' + error.message, 'fa-exclamation-circle', 'text-danger');
                } finally {
                    showLoading(false);
                }
            }, 300); // Simulate a small delay for better UX
        }

        // Run the code
        function runCode() {
            updateStatus('Running code...', 'fa-circle-o-notch fa-spin', 'text-gray-500');
            updatePreview();
        }

        // Reset the code to initial state
        function resetCode() {
            mixedCode.value = initialMixedCode;
            updateStatus('Code reset to initial state', 'fa-refresh', 'text-primary');
        }

        // Toggle fullscreen preview
        function toggleFullscreen() {
            fullscreenModal.classList.toggle('hidden');
            if (!fullscreenModal.classList.contains('hidden')) {
                updatePreview(); // Refresh fullscreen preview
            }
        }

        // Open preview in new tab
        function openInNewTab() {
            const code = mixedCode.value;
            const newTab = window.open('', '_blank');
            newTab.document.open();
            newTab.document.write(code);
            newTab.document.close();
        }

        // Toggle theme (light/dark)
        function toggleTheme() {
            const isDark = document.body.classList.toggle('dark-theme');
            
            if (isDark) {
                document.body.classList.add('bg-gray-900', 'text-white');
                document.body.classList.remove('bg-gray-50', 'text-dark');
                themeToggle.innerHTML = '<i class="fa fa-sun-o text-yellow-400"></i>';
                updateStatus('Switched to dark mode', 'fa-moon-o', 'text-yellow-400');
            } else {
                document.body.classList.remove('bg-gray-900', 'text-white');
                document.body.classList.add('bg-gray-50', 'text-dark');
                themeToggle.innerHTML = '<i class="fa fa-moon-o text-gray-600"></i>';
                updateStatus('Switched to light mode', 'fa-sun-o', 'text-yellow-400');
            }
        }

        // Update status message
        function updateStatus(message, iconClass, iconColor) {
            statusText.textContent = message;
            statusIcon.className = `fa ${iconClass} ${iconColor} ml-1`;
            
            // Auto reset status after 3 seconds
            setTimeout(() => {
                statusText.textContent = 'Ready';
                statusIcon.className = 'fa fa-check-circle text-success ml-1';
            }, 3000);
        }

        // Show/hide loading indicator
        function showLoading(show) {
            if (show) {
                loadingIndicator.classList.remove('hidden');
            } else {
                loadingIndicator.classList.add('hidden');
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
    