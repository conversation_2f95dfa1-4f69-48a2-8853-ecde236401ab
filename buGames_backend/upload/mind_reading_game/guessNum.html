
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>读心术游戏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors duration-200">
    <div class="container max-w-3xl mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center text-primary mb-6">神奇读心术</h1>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div id="instructions" class="mb-6">
                <h2 class="text-xl font-semibold mb-4">游戏规则</h2>
                <p class="mb-4">请在心里想一个1到30之间的数字。</p>
                <p class="mb-4">我会给你看5组数字，请告诉我你想的数字是否在这组里。</p>
                <p class="mb-4">回答完5个问题后，我就能猜出你心里想的数字！</p>
                <button id="start-button" class="bg-primary hover:bg-opacity-80 text-white font-medium py-2 px-6 rounded-md transition">开始游戏</button>
            </div>
            
            <div id="game-area" class="hidden">
                <div id="question-area" class="mb-6">
                    <h2 class="text-xl font-semibold mb-4">问题 <span id="question-number">1</span> (共5题)</h2>
                    
                    <div class="mb-6">
                        <h3 class="text-lg font-medium mb-3 text-center">你想的数字在这组里吗？</h3>
                        <div id="numbers-display" class="p-4 bg-green-50 dark:bg-green-900/30 rounded-md grid grid-cols-3 sm:grid-cols-5 gap-3 text-center"></div>
                    </div>
                    
                    <div class="flex justify-center space-x-8">
                        <button id="yes-button" class="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-8 rounded-lg transition text-lg">在</button>
                        <button id="no-button" class="bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-8 rounded-lg transition text-lg">不在</button>
                    </div>
                </div>
                
                <div id="progress-area" class="mb-4 mt-8">
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                        <div id="progress-bar" class="bg-primary h-3 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            
            <div id="result-area" class="hidden">
                <h2 class="text-2xl font-bold mb-6 text-center">你想的数字是：</h2>
                <div id="final-number" class="text-8xl font-bold text-primary mb-10 text-center"></div>
                <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <button id="play-again" class="bg-primary hover:bg-opacity-80 text-white font-medium py-2 px-6 rounded-md transition">再玩一次</button>
                    <button id="show-explanation" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-md transition">看看原理</button>
                </div>
            </div>
            
            <div id="explanation-area" class="hidden">
                <h2 class="text-xl font-semibold mb-4">我是怎么猜到 <span id="explanation-number" class="text-primary"></span> 的？</h2>
                
                <p class="mb-4">这是一个使用"二进制"的小魔术。每组数字都有特别的规律：</p>
                
                <div id="interactive-explanation" class="mb-6">
                    <!-- Interactive explanation will be populated here -->
                </div>
                
                <div class="mb-4">
                    <h3 class="text-lg font-medium mb-2">数字的秘密代码：</h3>
                    <div id="explanation-binary-display" class="flex justify-center space-x-2 mb-4"></div>
                    <p id="explanation-calculation" class="text-center mb-4"></p>
                </div>
                
                <div class="flex justify-center">
                    <button id="explanation-back" class="bg-primary hover:bg-opacity-80 text-white font-medium py-2 px-6 rounded-md transition">再玩一次</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check for dark mode preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Game variables
        const questions = [
            {
                bitPosition: 4,
                value: 16,
                description: "第1组",
                includedNumbers: [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
            },
            {
                bitPosition: 3,
                value: 8,
                description: "第2组",
                includedNumbers: [8, 9, 10, 11, 12, 13, 14, 15, 24, 25, 26, 27, 28, 29, 30]
            },
            {
                bitPosition: 2,
                value: 4,
                description: "第3组",
                includedNumbers: [4, 5, 6, 7, 12, 13, 14, 15, 20, 21, 22, 23, 28, 29, 30]
            },
            {
                bitPosition: 1,
                value: 2,
                description: "第4组",
                includedNumbers: [2, 3, 6, 7, 10, 11, 14, 15, 18, 19, 22, 23, 26, 27, 30]
            },
            {
                bitPosition: 0,
                value: 1,
                description: "第5组",
                includedNumbers: [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29]
            }
        ];
        
        let currentQuestion = 0;
        let binaryResult = ["?", "?", "?", "?", "?"];
        let userAnswers = [];
        let guessedNumber = null;
        
        // DOM Elements
        const startButton = document.getElementById('start-button');
        const yesButton = document.getElementById('yes-button');
        const noButton = document.getElementById('no-button');
        const playAgainButton = document.getElementById('play-again');
        const showExplanationButton = document.getElementById('show-explanation');
        const explanationBackButton = document.getElementById('explanation-back');
        
        const instructionsArea = document.getElementById('instructions');
        const gameArea = document.getElementById('game-area');
        const resultArea = document.getElementById('result-area');
        const explanationArea = document.getElementById('explanation-area');
        
        const questionNumber = document.getElementById('question-number');
        const numbersDisplay = document.getElementById('numbers-display');
        const progressBar = document.getElementById('progress-bar');
        const finalNumber = document.getElementById('final-number');
        const explanationNumber = document.getElementById('explanation-number');
        const interactiveExplanation = document.getElementById('interactive-explanation');
        const explanationBinaryDisplay = document.getElementById('explanation-binary-display');
        const explanationCalculation = document.getElementById('explanation-calculation');
        
        // Function to shuffle an array (Fisher-Yates algorithm)
        function shuffleArray(array) {
            const newArray = [...array];
            for (let i = newArray.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
            }
            return newArray;
        }
        
        // Function to display the current question
        function displayQuestion() {
            if (currentQuestion >= questions.length) {
                showResult();
                return;
            }
            
            const question = questions[currentQuestion];
            
            // Update question display
            questionNumber.textContent = currentQuestion + 1;
            
            // Display the numbers in random order
            const shuffledNumbers = shuffleArray(question.includedNumbers);
            
            // Update numbers display
            let numbersHTML = '';
            shuffledNumbers.forEach(num => {
                numbersHTML += `<div class="font-medium text-2xl py-3 bg-white dark:bg-gray-700 rounded shadow-sm">${num}</div>`;
            });
            numbersDisplay.innerHTML = numbersHTML;
            
            // Update progress bar
            const progress = (currentQuestion / questions.length) * 100;
            progressBar.style.width = `${progress}%`;
        }
        
        // Function to process the user's answer
        function processAnswer(isYes) {
            const question = questions[currentQuestion];
            const bitPosition = question.bitPosition;
            
            // Record the answer
            binaryResult[4 - bitPosition] = isYes ? "1" : "0"; 
            
            userAnswers.push({
                question: currentQuestion + 1,
                bitPosition: bitPosition,
                value: question.value,
                answer: isYes ? "在" : "不在",
                bit: isYes ? "1" : "0"
            });
            
            // Move to next question
            currentQuestion++;
            displayQuestion();
        }
        
        // Function to show the final result
        function showResult() {
            gameArea.classList.add('hidden');
            resultArea.classList.remove('hidden');
            
            const binaryString = binaryResult.join('');
            guessedNumber = parseInt(binaryString, 2);
            
            finalNumber.textContent = guessedNumber;
        }
        
        // Function to show the explanation
        function showExplanation() {
            resultArea.classList.add('hidden');
            explanationArea.classList.remove('hidden');
            
            explanationNumber.textContent = guessedNumber;
            
            // Build the interactive explanation
            let explanationHTML = '';
            userAnswers.forEach((answer, index) => {
                const bitValue = Math.pow(2, answer.bitPosition);
                explanationHTML += `
                    <div class="mb-4 p-4 rounded-lg ${answer.answer === '在' ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20'}">
                        <h4 class="font-medium mb-2">问题 ${index + 1}：你的数字在第${index + 1}组中吗？</h4>
                        <p class="mb-2">你的回答：<strong class="${answer.answer === '在' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">${answer.answer}</strong></p>
                        <p class="mb-1">第${index + 1}组包含了所有"二进制"中第${5-index}位是1的数字。</p>
                        <p class="text-sm italic">这个位置的值是${bitValue}，所以你的数字${answer.answer === '在' ? '有' : '没有'}这个部分。</p>
                    </div>
                `;
            });
            interactiveExplanation.innerHTML = explanationHTML;
            
            // Display the binary representation
            let binaryHTML = '';
            let calculationHTML = '';
            let total = 0;
            
            for (let i = 0; i < 5; i++) {
                const bit = binaryResult[i];
                const bitPosition = 4 - i;
                const value = Math.pow(2, bitPosition);
                
                let classes = "w-14 h-14 flex items-center justify-center text-xl font-mono rounded mx-1";
                if (bit === "1") {
                    classes += " bg-green-100 dark:bg-green-900 border-2 border-green-500";
                    calculationHTML += `${value} + `;
                    total += value;
                } else {
                    classes += " bg-red-100 dark:bg-red-900 border-2 border-red-500";
                    calculationHTML += `0 + `;
                }
                
                binaryHTML += `
                    <div class="text-center">
                        <div class="${classes}">${bit}</div>
                        <div class="text-xs mt-1">${value}</div>
                    </div>
                `;
            }
            
            explanationBinaryDisplay.innerHTML = binaryHTML;
            
            // Create the calculation explanation
            calculationHTML = calculationHTML.slice(0, -3); // Remove the last " + "
            explanationCalculation.innerHTML = `${calculationHTML} = ${total}`;
        }
        
        // Function to reset the game
        function resetGame() {
            currentQuestion = 0;
            binaryResult = ["?", "?", "?", "?", "?"];
            userAnswers = [];
            guessedNumber = null;
            
            explanationArea.classList.add('hidden');
            resultArea.classList.add('hidden');
            instructionsArea.classList.remove('hidden');
            gameArea.classList.add('hidden');
        }
        
        // Event listeners
        startButton.addEventListener('click', () => {
            instructionsArea.classList.add('hidden');
            gameArea.classList.remove('hidden');
            displayQuestion();
        });
        
        yesButton.addEventListener('click', () => processAnswer(true));
        noButton.addEventListener('click', () => processAnswer(false));
        playAgainButton.addEventListener('click', resetGame);
        showExplanationButton.addEventListener('click', showExplanation);
        explanationBackButton.addEventListener('click', resetGame);
        
        // Initialize
        resetGame();
    </script>
</body>
</html>
