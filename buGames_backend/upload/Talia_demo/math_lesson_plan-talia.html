<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math Lesson Plan</title>
    <style>
        body { font-family: 'Times New Roman', serif; line-height: 1.6; margin: 20px; }
        .task-container { border-left: 4px solid #5D5CDE; padding-left: 1rem; margin: 1rem 0; }
        .explanation-box { background: #f8fafc; border-radius: 8px; padding: 1rem; margin: 0.5rem 0; }
        .answer-key { background: #fff3cd; border: 1px solid #ffeaa7; padding: 0.5rem; border-radius: 4px; }
        .hidden { display: none; }
        button { background: #5D5CDE; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; margin: 0.25rem; }
        button:hover { background: #4B4BC8; }
        @media print { button { display: none; } }
    </style>
</head>
<body>
```html
<div style="max-width: 900px; margin: 0 auto; padding: 20px; font-family: 'Times New Roman', serif; line-height: 1.6; color: #333;">
    
    <!-- Lesson Header -->
    <div style="border-bottom: 3px solid #5D5CDE; padding-bottom: 20px; margin-bottom: 30px;">
        <h1 style="color: #5D5CDE; text-align: center; margin-bottom: 10px;">Statistical Inference with Vitamin C Quality Control</h1>
        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #5D5CDE;">
            <h3 style="margin-top: 0; color: #5D5CDE;">Lesson Overview</h3>
            <p><strong>Duration:</strong> 45 minutes</p>
            <p><strong>Grade Level:</strong> College</p>
            <p><strong>Learning Objectives:</strong></p>
            <ul>
                <li>Calculate point estimates and construct confidence intervals for population means</li>
                <li>Determine appropriate sample sizes for specified precision levels</li>
                <li>Formulate and conduct one-tailed hypothesis tests</li>
                <li>Interpret p-values and make statistical decisions using decision rules</li>
                <li>Apply statistical methods to quality control scenarios</li>
            </ul>
            <p><strong>Materials Needed:</strong> Calculator, Standard Normal (Z) table, Formula sheet</p>
        </div>
    </div>

    <!-- Introduction Activity -->
    <div style="margin-bottom: 30px; background: #e8f4fd; padding: 20px; border-radius: 8px; border-left: 4px solid #2563eb;">
        <h2 style="color: #2563eb; margin-top: 0;">🏭 Introduction Activity (8 minutes)</h2>
        <p><strong>Quality Control Scenario:</strong> A food manufacturer needs to verify the vitamin C content in their tomato juice to ensure regulatory compliance and accurate nutritional labeling.</p>
        
        <div style="background: white; padding: 15px; border-radius: 6px; margin: 15px 0;">
            <h4>Sample Data: Vitamin C Concentrations (mg per 100g)</h4>
            <p style="text-align: center; font-size: 20px; background: #f1f5f9; padding: 15px; border-radius: 4px; font-weight: bold;">
                16, 22, 21, 20, 23, 21, 17, 15, 18, 22
            </p>
            <p style="font-style: italic; color: #666;">Data from 10 randomly selected tins of tomato juice from the same brand</p>
        </div>

        <div style="background: #fff3cd; padding: 12px; border-radius: 4px; border-left: 3px solid #ffc107;">
            <strong>Warm-up Question:</strong> What questions might a quality control manager want to answer using this data?
        </div>
    </div>

    <!-- Part A: Point Estimation -->
    <div style="margin-bottom: 30px; border-left: 4px solid #5D5CDE; padding-left: 20px;">
        <h2 style="color: #5D5CDE;">Part A: Point Estimation</h2>
        
        <!-- Interactive Fill in the Blank Exercise -->
        <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>📝 Interactive Fill in the Blank - Sample Mean Calculation</h4>
            <p>Complete the calculation to find the point estimate for μ:</p>
            <div style="background: white; padding: 15px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 16px;">
                Step 1: Sum all values<br>
                16 + 22 + 21 + 20 + 23 + 21 + 17 + 15 + 18 + 22 = 
                <input type="number" id="sum_input" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">
                <button onclick="checkSum()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check</button>
                <span id="sum_feedback" style="margin-left: 10px; font-weight: bold;"></span><br><br>
                
                Step 2: Divide by sample size<br>
                x̄ = <input type="number" id="mean_input" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?"> ÷ 10 = 
                <input type="number" step="0.1" id="final_mean" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?"> mg per 100g
                <button onclick="checkMean()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check</button>
                <span id="mean_feedback" style="margin-left: 10px; font-weight: bold;"></span>
            </div>
            <div id="complete_solution" style="background: #d1fae5; padding: 10px; margin-top: 10px; border-radius: 4px; display: none;">
                <strong>Complete Solution:</strong><br>
                Step 1: 16 + 22 + 21 + 20 + 23 + 21 + 17 + 15 + 18 + 22 = <strong>195</strong><br>
                Step 2: x̄ = 195 ÷ 10 = <strong>19.5</strong> mg per 100g
            </div>
        </div>

        <!-- Interactive Multiple Choice Question -->
        <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>🎯 Interactive Multiple Choice - Statistical Terminology</h4>
            <p>The value x̄ = 19.5 mg per 100g is called a:</p>
            <div style="margin: 10px 0;" id="mc1_options">
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc1', 'a', this)">
                    <input type="radio" name="mc1" value="a" style="margin-right: 8px;"> A) Population parameter
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc1', 'b', this)">
                    <input type="radio" name="mc1" value="b" style="margin-right: 8px;"> B) Point estimate
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc1', 'c', this)">
                    <input type="radio" name="mc1" value="c" style="margin-right: 8px;"> C) Confidence interval
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc1', 'd', this)">
                    <input type="radio" name="mc1" value="d" style="margin-right: 8px;"> D) Test statistic
                </label>
            </div>
            <div id="mc1_feedback" style="margin-top: 15px; padding: 15px; border-radius: 4px; display: none;"></div>
        </div>
    </div>

    <!-- Part B: Confidence Intervals -->
    <div style="margin-bottom: 30px; border-left: 4px solid #5D5CDE; padding-left: 20px;">
        <h2 style="color: #5D5CDE;">Part B: Confidence Interval Construction</h2>
        
        <!-- Interactive Step-by-Step Guided Problem -->
        <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>📋 Interactive Step-by-Step Construction</h4>
            <p><strong>Given:</strong> σ² = 16 (so σ = 4), n = 10, x̄ = 19.5, 95% confidence level</p>
            
            <div style="background: white; padding: 15px; border-radius: 4px; margin: 10px 0;">
                <h5>Step 1: Find the critical z-value</h5>
                <p>For 95% confidence: α = 0.05, so α/2 = 0.025</p>
                <p>From the standard normal table: z<sub>0.025</sub> = 
                <input type="number" step="0.01" id="z_value" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">
                <button onclick="checkZValue()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check</button>
                <span id="z_feedback" style="margin-left: 10px; font-weight: bold;"></span></p>
            </div>

            <div style="background: white; padding: 15px; border-radius: 4px; margin: 10px 0;">
                <h5>Step 2: Calculate the standard error</h5>
                <p>SE = σ/√n = 4/√10 = 4/<input type="number" step="0.001" id="sqrt10" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?"> = 
                <input type="number" step="0.001" id="se_value" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">
                <button onclick="checkSE()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check</button>
                <span id="se_feedback" style="margin-left: 10px; font-weight: bold;"></span></p>
            </div>

            <div style="background: white; padding: 15px; border-radius: 4px; margin: 10px 0;">
                <h5>Step 3: Calculate the margin of error</h5>
                <p>E = z<sub>α/2</sub> × SE = 1.96 × 1.265 = 
                <input type="number" step="0.01" id="margin_error" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">
                <button onclick="checkMarginError()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check</button>
                <span id="me_feedback" style="margin-left: 10px; font-weight: bold;"></span></p>
            </div>

            <div style="background: white; padding: 15px; border-radius: 4px; margin: 10px 0;">
                <h5>Step 4: Construct the confidence interval</h5>
                <p>CI = x̄ ± E = 19.5 ± 2.48 = (
                <input type="number" step="0.01" id="ci_lower" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">, 
                <input type="number" step="0.01" id="ci_upper" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">)
                <button onclick="checkCI()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check</button>
                <span id="ci_feedback" style="margin-left: 10px; font-weight: bold;"></span></p>
            </div>
        </div>

        <!-- Interactive Multiple Choice - Interpretation -->
        <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>🎯 Interactive Multiple Choice - Interpretation</h4>
            <p>The correct interpretation of the 95% confidence interval (17.02, 21.98) is:</p>
            <div style="margin: 10px 0;" id="mc2_options">
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc2', 'a', this)">
                    <input type="radio" name="mc2" value="a" style="margin-right: 8px;"> A) There's a 95% probability that μ lies between 17.02 and 21.98
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc2', 'b', this)">
                    <input type="radio" name="mc2" value="b" style="margin-right: 8px;"> B) We are 95% confident that μ lies between 17.02 and 21.98
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc2', 'c', this)">
                    <input type="radio" name="mc2" value="c" style="margin-right: 8px;"> C) 95% of the data falls between 17.02 and 21.98
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc2', 'd', this)">
                    <input type="radio" name="mc2" value="d" style="margin-right: 8px;"> D) The sample mean has a 95% chance of being between 17.02 and 21.98
                </label>
            </div>
            <div id="mc2_feedback" style="margin-top: 15px; padding: 15px; border-radius: 4px; display: none;"></div>
        </div>
    </div>

    <!-- Part C: Sample Size Determination -->
    <div style="margin-bottom: 30px; border-left: 4px solid #5D5CDE; padding-left: 20px;">
        <h2 style="color: #5D5CDE;">Part C: Sample Size Analysis</h2>
        
        <!-- Interactive Fill in the Blank with Calculation -->
        <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>🧮 Interactive Sample Size Calculation</h4>
            <p><strong>Question:</strong> Is n = 10 sufficient for a margin of error of at most 1.5? If not, what should n be?</p>
            
            <div style="background: white; padding: 15px; border-radius: 4px; margin: 10px 0;">
                <h5>Part 1: Check current margin of error</h5>
                <p>Current margin of error with n = 10: E = 1.96 × (4/√10) = 
                <input type="number" step="0.01" id="current_margin" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">
                <button onclick="checkCurrentMargin()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check</button>
                <span id="current_margin_feedback" style="margin-left: 10px; font-weight: bold;"></span></p>
                <p>Since <input type="number" step="0.01" id="comparison_value" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?"> &gt; 1.5, we need a larger sample size.</p>
                
                <h5 style="margin-top: 20px;">Part 2: Calculate required sample size</h5>
                <p>For E ≤ 1.5: 1.5 = 1.96 × (4/√n)</p>
                <p>Solving for √n: √n = (1.96 × 4)/1.5 = 
                <input type="number" step="0.001" id="sqrt_n" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">
                <button onclick="checkSqrtN()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check</button>
                <span id="sqrt_n_feedback" style="margin-left: 10px; font-weight: bold;"></span></p>
                <p>Therefore: n = (<input type="number" step="0.001" id="n_squared" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">)² = 
                <input type="number" step="0.01" id="n_calculated" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?"></p>
                <p>Since n must be a whole number, round up to: n = 
                <input type="number" id="n_final" style="width: 80px; padding: 2px; border: 1px solid #ccc; border-radius: 3px;" placeholder="?">
                <button onclick="checkFinalN()" style="background: #5D5CDE; color: white; border: none; padding: 4px 8px; border-radius: 3px; margin-left: 5px; cursor: pointer;">Check All</button>
                <span id="final_n_feedback" style="margin-left: 10px; font-weight: bold;"></span></p>
            </div>
        </div>
    </div>

    <!-- Part D: Hypothesis Testing Setup -->
    <div style="margin-bottom: 30px; border-left: 4px solid #5D5CDE; padding-left: 20px;">
        <h2 style="color: #5D5CDE;">Part D: Hypothesis Testing</h2>
        
        <!-- Interactive Multiple Choice - Test Type -->
        <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>🎯 Interactive Test Selection</h4>
            <p>To test whether the mean vitamin C concentration is greater than 18 mg per 100g, which test is most appropriate?</p>
            <div style="margin: 10px 0;" id="mc3_options">
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc3', 'a', this)">
                    <input type="radio" name="mc3" value="a" style="margin-right: 8px;"> A) Two-tailed z-test
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc3', 'b', this)">
                    <input type="radio" name="mc3" value="b" style="margin-right: 8px;"> B) One-tailed z-test (right-tailed)
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc3', 'c', this)">
                    <input type="radio" name="mc3" value="c" style="margin-right: 8px;"> C) One-tailed z-test (left-tailed)
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc3', 'd', this)">
                    <input type="radio" name="mc3" value="d" style="margin-right: 8px;"> D) t-test (unknown variance)
                </label>
            </div>
            <div id="mc3_feedback" style="margin-top: 15px; padding: 15px; border-radius: 4px; display: none;"></div>
        </div>

        <!-- Interactive Fill in the Blank - Hypotheses -->
        <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>📝 Interactive Hypothesis Formulation</h4>
            <p>Complete the null and alternative hypotheses for testing whether μ &gt; 18:</p>
            <div style="background: white; padding: 15px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 18px;">
                H₀: μ <select id="h0_operator" style="font-size: 16px; padding: 2px;">
                    <option value="">Select</option>
                    <option value="&lt;">&lt;</option>
                    <option value="≤">≤</option>
                    <option value="=">=</option>
                    <option value="≥">≥</option>
                    <option value="&gt;">&gt;</option>
                </select> 18<br><br>
                H₁: μ <select id="h1_operator" style="font-size: 16px; padding: 2px;">
                    <option value="">Select</option>
                    <option value="&lt;">&lt;</option>
                    <option value="≤">≤</option>
                    <option value="=">=</option>
                    <option value="≥">≥</option>
                    <option value="&gt;">&gt;</option>
                </select> 18
                <button onclick="checkHypotheses()" style="background: #5D5CDE; color: white; border: none; padding: 6px 12px; border-radius: 3px; margin-left: 15px; cursor: pointer; font-family: 'Times New Roman', serif;">Check</button>
                <div id="hypotheses_feedback" style="margin-top: 10px; font-family: 'Times New Roman', serif; font-size: 14px;"></div>
            </div>
        </div>
    </div>

    <!-- Part E: Statistical Decision Making -->
    <div style="margin-bottom: 30px; border-left: 4px solid #5D5CDE; padding-left: 20px;">
        <h2 style="color: #5D5CDE;">Part E: Statistical Decision Making</h2>
        
        <!-- Interactive Multiple Choice - Decision Rule -->
        <div style="background: #f8fafc; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>⚖️ Interactive Decision Making</h4>
            <p><strong>Given:</strong> α = 0.05 (5% significance level) and p-value = 0.15</p>
            <p>What should the researcher conclude?</p>
            <div style="margin: 10px 0;" id="mc4_options">
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc4', 'a', this)">
                    <input type="radio" name="mc4" value="a" style="margin-right: 8px;"> A) Reject H₀ because p-value &gt; α
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc4', 'b', this)">
                    <input type="radio" name="mc4" value="b" style="margin-right: 8px;"> B) Fail to reject H₀ because p-value &gt; α
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc4', 'c', this)">
                    <input type="radio" name="mc4" value="c" style="margin-right: 8px;"> C) Accept H₀ because p-value &gt; α
                </label>
                <label style="display: block; margin: 5px 0; padding: 8px; background: white; border-radius: 4px; cursor: pointer; border: 2px solid transparent;" onclick="selectAnswer('mc4', 'd', this)">
                    <input type="radio" name="mc4" value="d" style="margin-right: 8px;"> D) The result is inconclusive
                </label>
            </div>
            <div id="mc4_feedback" style="margin-top: 15px; padding: 15px; border-radius: 4px; display: none;"></div>
        </div>
    </div>

    <!-- Comprehensive Assessment -->
    <div style="margin-bottom: 30px; background: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b;">
        <h2 style="color: #d97706; margin-top: 0;">📊 Assessment Questions</h2>
        
        <div style="background: white; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>Question 1: Application</h4>
            <p>A pharmaceutical company wants to test if their vitamin tablets contain more than 95 mg of vitamin C on average. A sample of 36 tablets gives x̄ = 98.5 mg with σ = 6 mg. Using α = 0.01:</p>
            <ol type="a">
                <li>State the hypotheses</li>
                <li>Calculate the test statistic</li>
                <li>Find the p-value</li>
                <li>Make a decision</li>
            </ol>
        </div>

        <div style="background: white; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>Question 2: Sample Size</h4>
            <p>How many vitamin tablets should be tested to estimate the mean vitamin C content within ±2 mg with 99% confidence, assuming σ = 6 mg?</p>
        </div>

        <div style="background: white; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>Question 3: Critical Thinking</h4>
            <p>Explain why we use "fail to reject H₀" instead of "accept H₀" when the p-value is greater than α. What are the implications for quality control decisions?</p>
        </div>
    </div>

    <!-- Homework and Extensions -->
    <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; border-left: 4px solid #16a34a;">
        <h2 style="color: #15803d; margin-top: 0;">🏠 Homework &amp; Extension Activities</h2>
        
        <div style="background: white; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>Practice Problem Set 1: Confidence Intervals</h4>
            <p>A quality control engineer measures the sugar content in soft drinks. From a sample of 25 bottles, x̄ = 38.2 g with σ = 2.1 g.</p>
            <ol>
                <li>Construct a 90% confidence interval for the mean sugar content</li>
                <li>Construct a 99% confidence interval for the mean sugar content</li>
                <li>Compare the widths and explain the difference</li>
            </ol>
        </div>

        <div style="background: white; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>Practice Problem Set 2: Hypothesis Testing</h4>
            <p>A company claims their energy bars contain 25g of protein on average. Test this claim using:</p>
            <ul>
                <li>Sample data: n = 40, x̄ = 24.1g, σ = 3.2g</li>
                <li>Significance level: α = 0.05</li>
                <li>Conduct both two-tailed and one-tailed tests (μ &lt; 25)</li>
            </ul>
        </div>

        <div style="background: white; padding: 15px; margin: 15px 0; border-radius: 8px;">
            <h4>Real-World Application Project</h4>
            <p>Research a recent food safety recall or quality control issue in the news. Write a 2-page analysis addressing:</p>
            <ul>
                <li>What statistical methods might have prevented the issue?</li>
                <li>How would you design a quality control sampling plan?</li>
                <li>What sample sizes would be appropriate for different precision levels?</li>
                <li>How do Type I and Type II errors apply to food safety decisions?</li>
            </ul>
        </div>

        <div style="background: #e0e7ff; padding: 15px; border-radius: 8px;">
            <h4>📚 Additional Resources for Review:</h4>
            <ul>
                <li>Practice using Z-tables and calculating critical values</li>
                <li>Review the Central Limit Theorem and sampling distributions</li>
                <li>Explore the relationship between confidence intervals and hypothesis tests</li>
                <li>Study real-world applications in pharmaceutical and food industries</li>
            </ul>
        </div>
    </div>

    <!-- Summary -->
    <div style="background: #1e293b; color: white; padding: 20px; border-radius: 8px; text-align: center;">
        <h3 style="margin-top: 0; color: #60a5fa;">Key Statistical Concepts Mastered</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
            <div>
                <p>✓ Point estimation using sample means</p>
                <p>✓ Confidence interval construction and interpretation</p>
                <p>✓ Sample size determination for specified precision</p>
            </div>
            <div>
                <p>✓ Hypothesis formulation and testing procedures</p>
                <p>✓ p-value interpretation and decision making</p>
                <p>✓ Application to quality control scenarios</p>
            </div>
        </div>
        <p style="margin-top: 15px; font-style: italic;">"Statistics is the art of making decisions under uncertainty"</p>
    </div>

    <script>
        // Fill in the blank functions
        function checkSum() {
            const input = document.getElementById('sum_input').value;
            const feedback = document.getElementById('sum_feedback');
            if (input == 195) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Correct!';
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ Try again. Add all the values: 16+22+21+20+23+21+17+15+18+22';
            }
        }

        function checkMean() {
            const meanInput = document.getElementById('mean_input').value;
            const finalMean = document.getElementById('final_mean').value;
            const feedback = document.getElementById('mean_feedback');
            if (meanInput == 195 && finalMean == 19.5) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Excellent! Sample mean = 19.5 mg per 100g';
                document.getElementById('complete_solution').style.display = 'block';
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ Check your calculation. Sum = 195, Mean = 195 ÷ 10 = 19.5';
            }
        }

        // Confidence interval functions
        function checkZValue() {
            const input = document.getElementById('z_value').value;
            const feedback = document.getElementById('z_feedback');
            if (Math.abs(input - 1.96) < 0.01) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Correct!';
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ For 95% confidence, z₀.₀₂₅ = 1.96';
            }
        }

        function checkSE() {
            const sqrt10 = document.getElementById('sqrt10').value;
            const se = document.getElementById('se_value').value;
            const feedback = document.getElementById('se_feedback');
            if (Math.abs(sqrt10 - 3.162) < 0.01 && Math.abs(se - 1.265) < 0.01) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Correct! √10 ≈ 3.162, SE ≈ 1.265';
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ √10 ≈ 3.162, so SE = 4/3.162 ≈ 1.265';
            }
        }

        function checkMarginError() {
            const input = document.getElementById('margin_error').value;
            const feedback = document.getElementById('me_feedback');
            if (Math.abs(input - 2.48) < 0.01) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Correct!';
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ E = 1.96 × 1.265 ≈ 2.48';
            }
        }

        function checkCI() {
            const lower = document.getElementById('ci_lower').value;
            const upper = document.getElementById('ci_upper').value;
            const feedback = document.getElementById('ci_feedback');
            if (Math.abs(lower - 17.02) < 0.01 && Math.abs(upper - 21.98) < 0.01) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Perfect! CI = (17.02, 21.98)';
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ Lower = 19.5 - 2.48 = 17.02, Upper = 19.5 + 2.48 = 21.98';
            }
        }

        // Sample size functions
        function checkCurrentMargin() {
            const input = document.getElementById('current_margin').value;
            const feedback = document.getElementById('current_margin_feedback');
            if (Math.abs(input - 2.48) < 0.01) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Correct!';
                document.getElementById('comparison_value').value = 2.48;
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ E = 1.96 × (4/√10) ≈ 2.48';
            }
        }

        function checkSqrtN() {
            const input = document.getElementById('sqrt_n').value;
            const feedback = document.getElementById('sqrt_n_feedback');
            if (Math.abs(input - 5.227) < 0.01) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Correct!';
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ √n = (1.96 × 4)/1.5 = 7.84/1.5 ≈ 5.227';
            }
        }

        function checkFinalN() {
            const nSquared = document.getElementById('n_squared').value;
            const nCalc = document.getElementById('n_calculated').value;
            const nFinal = document.getElementById('n_final').value;
            const feedback = document.getElementById('final_n_feedback');
            
            if (Math.abs(nSquared - 5.227) < 0.01 && Math.abs(nCalc - 27.32) < 0.1 && nFinal == 28) {
                feedback.style.color = 'green';
                feedback.textContent = '✓ Perfect! n = (5.227)² ≈ 27.32, round up to 28';
            } else {
                feedback.style.color = 'red';
                feedback.textContent = '✗ n = (5.227)² ≈ 27.32, round up to 28';
            }
        }

        // Hypothesis testing function
        function checkHypotheses() {
            const h0 = document.getElementById('h0_operator').value;
            const h1 = document.getElementById('h1_operator').value;
            const feedback = document.getElementById('hypotheses_feedback');
            
            if (h0 === '≤' && h1 === '>') {
                feedback.style.color = 'green';
                feedback.innerHTML = '<strong>✓ Correct!</strong><br>H₀: μ ≤ 18 (null: mean is not greater than 18)<br>H₁: μ > 18 (alternative: mean is greater than 18)';
            } else {
                feedback.style.color = 'red';
                feedback.innerHTML = '<strong>✗ Try again.</strong><br>For testing μ > 18: H₀ should be ≤ and H₁ should be >';
            }
        }

        // Multiple choice functions
        function selectAnswer(questionId, selectedValue, element) {
            // Clear previous selections
            const options = document.getElementById(questionId + '_options').getElementsByTagName('label');
            for (let option of options) {
                option.style.border = '2px solid transparent';
                option.style.backgroundColor = 'white';
            }
            
            // Highlight selected option
            element.style.border = '2px solid #5D5CDE';
            element.style.backgroundColor = '#f8fafc';
            
            // Show feedback
            const feedbackDiv = document.getElementById(questionId + '_feedback');
            feedbackDiv.style.display = 'block';
            
            if (questionId === 'mc1') {
                if (selectedValue === 'b') {
                    feedbackDiv.style.background = '#d1fae5';
                    feedbackDiv.innerHTML = '<strong>✓ Correct!</strong><br><strong>Explanation:</strong> A point estimate is a single value calculated from sample data that serves as our best estimate of an unknown population parameter. Here, x̄ = 19.5 is our point estimate for μ (the true population mean).<br><br><strong>Common Mistake:</strong> Don\'t confuse parameters (unknown population values like μ) with statistics (calculated sample values like x̄).';
                } else {
                    feedbackDiv.style.background = '#fee2e2';
                    feedbackDiv.innerHTML = '<strong>✗ Incorrect.</strong><br>The correct answer is B) Point estimate. A point estimate is a single value that serves as an estimate of an unknown population parameter.';
                }
            } else if (questionId === 'mc2') {
                if (selectedValue === 'b') {
                    feedbackDiv.style.background = '#d1fae5';
                    feedbackDiv.innerHTML = '<strong>✓ Correct!</strong><br><strong>Explanation:</strong> Confidence refers to our confidence in the method. If we repeated this process many times, 95% of the intervals we construct would contain the true population mean μ.<br><br><strong>Common Mistake:</strong> Option A is incorrect because μ is a fixed (but unknown) value—it either is or isn\'t in the interval. There\'s no probability involved once the interval is calculated.';
                } else {
                    feedbackDiv.style.background = '#fee2e2';
                    feedbackDiv.innerHTML = '<strong>✗ Incorrect.</strong><br>The correct answer is B. Confidence intervals refer to our confidence in the method, not the probability that μ is in a specific interval.';
                }
            } else if (questionId === 'mc3') {
                if (selectedValue === 'b') {
                    feedbackDiv.style.background = '#d1fae5';
                    feedbackDiv.innerHTML = '<strong>✓ Correct!</strong><br><strong>Explanation:</strong> We use a z-test because σ is known (σ = 4). It\'s one-tailed because we\'re testing for "greater than" (directional claim). Right-tailed because we\'re looking for evidence that μ > 18.';
                } else {
                    feedbackDiv.style.background = '#fee2e2';
                    feedbackDiv.innerHTML = '<strong>✗ Incorrect.</strong><br>The correct answer is B) One-tailed z-test (right-tailed). We use z-test because σ is known, and it\'s right-tailed because we\'re testing μ > 18.';
                }
            } else if (questionId === 'mc4') {
                if (selectedValue === 'b') {
                    feedbackDiv.style.background = '#d1fae5';
                    feedbackDiv.innerHTML = '<strong>✓ Correct!</strong><br><strong>Decision Rule:</strong> If p-value ≤ α, reject H₀. If p-value > α, fail to reject H₀.<br>Since 0.15 > 0.05, we fail to reject H₀.<br><br><strong>Practical Interpretation:</strong> There is insufficient evidence to conclude that the mean vitamin C concentration is greater than 18 mg per 100g.<br><br><strong>Important:</strong> We never "accept" H₀ in statistical testing—we can only fail to find evidence against it.';
                } else {
                    feedbackDiv.style.background = '#fee2e2';
                    feedbackDiv.innerHTML = '<strong>✗ Incorrect.</strong><br>The correct answer is B) Fail to reject H₀ because p-value > α. Since 0.15 > 0.05, we fail to reject the null hypothesis.';
                }
            }
        }

        // Make elements with data-toggle work
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButtons = document.querySelectorAll('[data-toggle="answer"], [data-toggle="explanation"]');
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const target = document.querySelector(this.getAttribute('data-target'));
                    if (target) {
                        const isHidden = target.classList.contains('hidden') || target.style.display === 'none';
                        if (isHidden) {
                            target.classList.remove('hidden');
                            target.style.display = 'block';
                            this.textContent = this.textContent.replace('Show', 'Hide');
                        } else {
                            target.classList.add('hidden');
                            target.style.display = 'none';
                            this.textContent = this.textContent.replace('Hide', 'Show');
                        }
                    }
                });
            });
        });
    </script>
</div>
```
<script>
    // Add interactivity
    document.querySelectorAll('[data-toggle="explanation"]').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const target = document.querySelector(this.getAttribute('data-target'));
            if (target) {
                target.classList.toggle('hidden');
                this.textContent = target.classList.contains('hidden') ? 'Show Explanation' : 'Hide Explanation';
            }
        });
    });
    
    document.querySelectorAll('[data-toggle="answer"]').forEach(toggle => {
        toggle.addEventListener('click', function() {
            const target = document.querySelector(this.getAttribute('data-target'));
            if (target) {
                target.classList.toggle('hidden');
                this.textContent = target.classList.contains('hidden') ? 'Show Answer' : 'Hide Answer';
            }
        });
    });</script>
</body>
</html>