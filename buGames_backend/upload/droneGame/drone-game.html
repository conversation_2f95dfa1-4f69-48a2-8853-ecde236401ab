
<!DOCTYPE html>

<html>

<head>

  <title><PERSON><PERSON> vs Monster Battle!</title>

  <style>

    body { font-family: sans-serif; background: #d0f4fa; text-align: center; }

    #monster { width: 120px; vertical-align: middle; }

    #drone { width: 100px; vertical-align: middle; }

    #questionBox, #result, #nextBtn { margin: 20px; }

    #score { font-size: 1.2em; color: #22577A; }

  </style>

</head>

<body>

  <h1>Dr<PERSON> vs Monster!</h1>

  <div>

    <img id="drone" src="https://upload.wikimedia.org/wikipedia/commons/2/2b/Drone_icon.svg" alt="drone" onerror="this.style.display='none';document.getElementById('droneAlt').style.display='inline';">

    <span id="droneAlt" style="display:none;font-size:2em;">🚁</span>

    <img id="monster" src="https://openclipart.org/image/400px/svg_to_png/286415/Monster.png" alt="monster" onerror="this.style.display='none';document.getElementById('monsterAlt').style.display='inline';">

    <span id="monsterAlt" style="display:none;font-size:2em;">👾</span>

  </div>

  <div id="score">Score: 0</div>

  <div id="questionBox"></div>

  <div id="choices"></div>

  <div id="result"></div>

  <button id="nextBtn" style="display:none;">Next</button>


  <script>

    // Simple question bank

    const questions = [

      {

        question: "What part of the drone helps it fly?",

        choices: ["Propellers", "Wheels", "Wings", "Tail"],

        answer: 0

      },

      {

        question: "Where should you fly a drone?",

        choices: ["Open field", "Crowded street", "Near airport", "Inside classroom"],

        answer: 0

      },

      {

        question: "What do you press to make the drone go up?",

        choices: ["Throttle up", "Turn left", "Turn right", "Throttle down"],

        answer: 0

      },

      {

        question: "What should you do before landing the drone?",

        choices: ["Slowly lower it", "Spin quickly", "Fly higher", "Turn off remote"],

        answer: 0

      }

    ];


    let current = 0;

    let score = 0;


    function showQuestion() {

      document.getElementById('result').innerHTML = '';

      document.getElementById('nextBtn').style.display = 'none';

      const q = questions[current];

      document.getElementById('questionBox').innerHTML = `<b>Q${current+1}:</b> ${q.question}`;

      let html = "";

      q.choices.forEach((choice, idx) => {

        html += `<button onclick="checkAnswer(${idx})">${choice}</button> `;

      });

      document.getElementById('choices').innerHTML = html;

    }


    window.checkAnswer = function(idx) {

      const q = questions[current];

      if (idx === q.answer) {

        document.getElementById('result').innerHTML = "🎉 Correct! The monster is hit!";

        score++;

        document.getElementById('monster').style.filter = "grayscale(100%)";

      } else {

        document.getElementById('result').innerHTML = "❌ Oops! Try again next time.";

        document.getElementById('monster').style.filter = "none";

      }

      document.getElementById('score').innerText = "Score: " + score;

      document.getElementById('choices').innerHTML = "";

      document.getElementById('nextBtn').style.display = 'inline-block';

    }


    document.getElementById('nextBtn').onclick = function() {

      document.getElementById('monster').style.filter = "none";

      current++;

      if (current < questions.length) {

        showQuestion();

      } else {

        document.getElementById('questionBox').innerHTML = "Game Over!";

        document.getElementById('choices').innerHTML = "";

        document.getElementById('nextBtn').style.display = 'none';

        document.getElementById('result').innerHTML = `Final Score: ${score}/${questions.length}`;

      }

    };


    // Start game

    showQuestion();

  </script>

</body>

</html>

