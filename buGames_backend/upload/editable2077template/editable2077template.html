<!DOCTYPE html><html lang="en"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voluntary Teaching Project - LANG 2077</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'bg-light': '#FFFFFF',
                        'bg-dark': '#181818'
                    }
                }
            }
        }
    </script>
    <style>
        .section {
            scroll-margin-top: 100px;
        }
        .tab-button.active {
            background-color: #5D5CDE;
            color: white;
        }
        .placeholder-img {
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .dark .placeholder-img {
            background: linear-gradient(45deg, #404040 25%, transparent 25%), 
                        linear-gradient(-45deg, #404040 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #404040 75%), 
                        linear-gradient(-45deg, transparent 75%, #404040 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .editable {
            border: 2px dashed transparent;
            padding: 4px;
            border-radius: 4px;
            transition: border-color 0.2s;
        }
        .editable:hover {
            border-color: #5D5CDE;
            background-color: rgba(93, 92, 222, 0.05);
        }
        .editable:focus {
            outline: none;
            border-color: #5D5CDE;
            background-color: rgba(93, 92, 222, 0.1);
        }
        .edit-input {
            width: 100%;
            background: transparent;
            border: none;
            font-size: inherit;
            font-weight: inherit;
            color: inherit;
            line-height: inherit;
        }
        .edit-input:focus {
            outline: none;
        }
        .edit-section {
            position: relative;
        }
        .edit-controls {
            position: absolute;
            top: 8px;
            right: 8px;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .edit-section:hover .edit-controls {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-bg-light dark:bg-bg-dark text-gray-900 dark:text-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- Language Switcher & Download Button -->
        <div class="flex justify-between items-center mb-4">
            <button onclick="downloadHTML()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download HTML
            </button>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-1 flex">
                <button onclick="switchLanguage('en')" id="lang-en" class="px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white">
                    English
                </button>
                <button onclick="switchLanguage('zh')" id="lang-zh" class="px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary">
                    中文
                </button>
            </div>
        </div>

        <!-- Header -->
        <div id="header" class="text-center mb-8 edit-section">
            <div class="edit-controls">
                <button onclick="saveSection('header')" class="bg-primary hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                    Save Header
                </button>
            </div>
            <h1 class="text-4xl font-bold text-primary mb-2 editable" contenteditable="true" id="main-title">Voluntary Teaching Project</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 editable" contenteditable="true" id="main-subtitle">HKBU LANG 2077 - Innovative Language Education Initiative</p>
        </div>

        <!-- Navigation Tabs -->
        <nav class="sticky top-0 bg-bg-light dark:bg-bg-dark z-10 pb-4">
            <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700 mb-8">
                <button onclick="scrollToSection('video')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-video">
                    📹 Video
                </button>
                <button onclick="scrollToSection('contexts')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-contexts">
                    🎯 Teaching Contexts
                </button>
                <button onclick="scrollToSection('ai-solutions')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-ai">
                    🤖 AI Solutions
                </button>
                <button onclick="scrollToSection('reflection')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-reflection">
                    💭 Sharing &amp; Reflection
                </button>
            </div>
        </nav>

        <!-- Content Sections -->
        <div class="space-y-16">
            <!-- Video Section -->
            <section id="video" class="section edit-section">
                <div class="edit-controls">
                    <button onclick="saveSection('video')" class="bg-primary hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        Save Video
                    </button>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-6 text-center editable" contenteditable="true" id="video-title">Project Overview Video</h2>
                    
                    <!-- Video URL Input -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2">Video URL (YouTube or Bilibili):</label>
                        <div class="flex gap-2">
                            <input type="url" id="video-url-input" class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-base bg-white dark:bg-gray-700" placeholder="Enter YouTube or Bilibili URL">
                            <button onclick="updateVideo()" class="bg-primary hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                                Update Video
                            </button>
                        </div>
                    </div>
                    
                    <div class="aspect-video max-w-4xl mx-auto" id="video-container">
                        <iframe width="100%" height="100%" src="https://www.youtube.com/embed/o8NPllzkFhE?start=1118" title="Voluntary Teaching Project Video" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" class="rounded-lg">
                        </iframe>
                    </div>
                    <p class="text-center text-gray-600 dark:text-gray-400 mt-4 editable" contenteditable="true" id="video-description">
                        Watch our comprehensive overview of the voluntary teaching project and its impact on language education.
                    </p>
                </div>
            </section>

            <!-- Teaching Contexts Section -->
            <section id="contexts" class="section edit-section">
                <div class="edit-controls">
                    <button onclick="saveSection('contexts')" class="bg-primary hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        Save Contexts
                    </button>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center editable" contenteditable="true" id="contexts-title">Teaching Contexts, Challenges and Needs</h2>
                    
                    <!-- Context 1 -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="context1-title">Educational Context</h3>
                            <p class="text-lg leading-relaxed mb-4 editable" contenteditable="true" id="context1-p1">
                                Our voluntary teaching project targets underserved communities where English language education resources are limited. We focus on providing quality language instruction to students who lack access to traditional educational opportunities.
                            </p>
                            <p class="text-lg leading-relaxed editable" contenteditable="true" id="context1-p2">
                                The project emphasizes practical communication skills, cultural exchange, and building confidence in English language use through interactive and engaging teaching methodologies.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center cursor-pointer hover:bg-opacity-80 transition-all relative" onclick="uploadImage('context1-img')" id="context1-img-container">
                            <input type="file" id="context1-img-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, 'context1-img')">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="context1-img-text">
                                Click to upload image<br><small class="text-sm">(or edit this text)</small>
                            </span>
                            <span class="editable hidden" contenteditable="true" id="context1-img">Teaching Context Image</span>
                        </div>
                    </div>

                    <!-- Context 2 -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center order-2 md:order-1 cursor-pointer hover:bg-opacity-80 transition-all relative" onclick="uploadImage('context2-img')" id="context2-img-container">
                            <input type="file" id="context2-img-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, 'context2-img')">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="context2-img-text">
                                Click to upload image<br><small class="text-sm">(or edit this text)</small>
                            </span>
                            <span class="editable hidden" contenteditable="true" id="context2-img">Challenges Overview</span>
                        </div>
                        <div class="order-1 md:order-2">
                            <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="context2-title">Key Challenges</h3>
                            <ul class="text-lg leading-relaxed space-y-2">
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span class="editable" contenteditable="true" id="challenge1">Limited technological infrastructure in target communities</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span class="editable" contenteditable="true" id="challenge2">Diverse learning levels and backgrounds among students</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span class="editable" contenteditable="true" id="challenge3">Cultural and linguistic barriers affecting communication</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-primary mr-2">•</span>
                                    <span class="editable" contenteditable="true" id="challenge4">Time constraints and scheduling difficulties</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Context 3 -->
                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="context3-title">Community Needs</h3>
                            <p class="text-lg leading-relaxed mb-4 editable" contenteditable="true" id="context3-p1">
                                Through community engagement and needs assessment, we identified critical gaps in English language education. Local communities expressed strong desire for practical English skills that could enhance employment opportunities and social mobility.
                            </p>
                            <p class="text-lg leading-relaxed editable" contenteditable="true" id="context3-p2">
                                The program addresses these needs through tailored curriculum design, flexible scheduling, and culturally sensitive teaching approaches that respect local contexts while promoting global communication skills.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center cursor-pointer hover:bg-opacity-80 transition-all relative" onclick="uploadImage('context3-img')" id="context3-img-container">
                            <input type="file" id="context3-img-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, 'context3-img')">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="context3-img-text">
                                Click to upload image<br><small class="text-sm">(or edit this text)</small>
                            </span>
                            <span class="editable hidden" contenteditable="true" id="context3-img">Community Needs Assessment</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI Solutions Section -->
            <section id="ai-solutions" class="section edit-section">
                <div class="edit-controls">
                    <button onclick="saveSection('ai-solutions')" class="bg-primary hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        Save AI Solutions
                    </button>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center editable" contenteditable="true" id="ai-title">AI-Powered Teaching Solutions</h2>
                    
                    <!-- AI Introduction -->
                    <div class="mb-12">
                        <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="ai-intro-title">Innovative AI Integration</h3>
                        <p class="text-lg leading-relaxed mb-6 editable" contenteditable="true" id="ai-intro-p">
                            Our project leverages cutting-edge AI technology to enhance the teaching and learning experience. We've developed customized chatbots and AI-assisted tools that provide personalized support to both teachers and students, making language learning more accessible and effective.
                        </p>
                    </div>

                    <!-- Customized Chatbot -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="chatbot-title">Custom AI Teaching Assistant</h3>
                            <p class="text-lg leading-relaxed mb-4 editable" contenteditable="true" id="chatbot-p">
                                Our AI chatbot serves as a 24/7 teaching assistant, providing instant feedback on grammar, pronunciation, and vocabulary. It adapts to individual learning styles and progress levels, offering personalized exercises and explanations.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2 editable" contenteditable="true" id="chatbot-features-title">Key Features:</h4>
                                <ul class="space-y-1 text-sm">
                                    <li class="editable" contenteditable="true" id="feature1">• Real-time grammar correction</li>
                                    <li class="editable" contenteditable="true" id="feature2">• Pronunciation guidance</li>
                                    <li class="editable" contenteditable="true" id="feature3">• Adaptive learning paths</li>
                                    <li class="editable" contenteditable="true" id="feature4">• Progress tracking</li>
                                </ul>
                            </div>
                        </div>
                        <div class="placeholder-img rounded-lg h-80 flex items-center justify-center cursor-pointer hover:bg-opacity-80 transition-all relative" onclick="uploadImage('chatbot-img')" id="chatbot-img-container">
                            <input type="file" id="chatbot-img-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, 'chatbot-img')">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="chatbot-img-text">
                                Click to upload image<br><small class="text-sm">(or edit this text)</small>
                            </span>
                            <span class="editable hidden" contenteditable="true" id="chatbot-img">AI Chatbot Interface Screenshot</span>
                        </div>
                    </div>

                    <!-- AI Tools -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-80 flex items-center justify-center cursor-pointer hover:bg-opacity-80 transition-all relative" onclick="uploadImage('analytics-img')" id="analytics-img-container">
                            <input type="file" id="analytics-img-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, 'analytics-img')">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="analytics-img-text">
                                Click to upload image<br><small class="text-sm">(or edit this text)</small>
                            </span>
                            <span class="editable hidden" contenteditable="true" id="analytics-img">AI Analytics Dashboard</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="analytics-title">Learning Analytics Platform</h3>
                            <p class="text-lg leading-relaxed mb-4 editable" contenteditable="true" id="analytics-p">
                                Advanced AI analytics help teachers understand student progress patterns, identify learning difficulties, and optimize teaching strategies. The platform provides detailed insights into student engagement and performance metrics.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2 editable" contenteditable="true" id="analytics-features-title">Analytics Features:</h4>
                                <ul class="space-y-1 text-sm">
                                    <li class="editable" contenteditable="true" id="analytics1">• Student progress visualization</li>
                                    <li class="editable" contenteditable="true" id="analytics2">• Learning pattern analysis</li>
                                    <li class="editable" contenteditable="true" id="analytics3">• Predictive performance modeling</li>
                                    <li class="editable" contenteditable="true" id="analytics4">• Customized intervention recommendations</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Implementation Results -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-primary editable" contenteditable="true" id="impact-title">Implementation Impact</h3>
                        <p class="text-lg leading-relaxed editable" contenteditable="true" id="impact-p">
                            The integration of AI solutions has resulted in improved learning outcomes, increased student engagement, and more efficient teaching processes. Students report higher confidence levels and teachers benefit from data-driven insights to enhance their instructional methods.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Sharing & Reflection Section -->
            <section id="reflection" class="section edit-section">
                <div class="edit-controls">
                    <button onclick="saveSection('reflection')" class="bg-primary hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                        Save Reflection
                    </button>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center editable" contenteditable="true" id="reflection-title">Sharing &amp; Reflection</h2>
                    
                    <!-- Project Outcomes -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="outcomes-title">Project Outcomes</h3>
                            <p class="text-lg leading-relaxed mb-4 editable" contenteditable="true" id="outcomes-p1">
                                Our voluntary teaching project has yielded significant positive outcomes for both students and teachers. Student engagement has increased by 40%, and standardized test scores have improved across all participating communities.
                            </p>
                            <p class="text-lg leading-relaxed editable" contenteditable="true" id="outcomes-p2">
                                The project has also enhanced the professional development of HKBU students, providing them with valuable real-world teaching experience and cross-cultural communication skills.
                            </p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center cursor-pointer hover:bg-opacity-80 transition-all relative" onclick="uploadImage('outcomes-img')" id="outcomes-img-container">
                            <input type="file" id="outcomes-img-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, 'outcomes-img')">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="outcomes-img-text">
                                Click to upload image<br><small class="text-sm">(or edit this text)</small>
                            </span>
                            <span class="editable hidden" contenteditable="true" id="outcomes-img">Student Success Stories</span>
                        </div>
                    </div>

                    <!-- Teacher Reflections -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center cursor-pointer hover:bg-opacity-80 transition-all relative" onclick="uploadImage('teacher-img')" id="teacher-img-container">
                            <input type="file" id="teacher-img-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, 'teacher-img')">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="teacher-img-text">
                                Click to upload image<br><small class="text-sm">(or edit this text)</small>
                            </span>
                            <span class="editable hidden" contenteditable="true" id="teacher-img">Teacher Feedback Session</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="teacher-title">Teacher Reflections</h3>
                            <blockquote class="text-lg italic mb-4 border-l-4 border-primary pl-4 editable" contenteditable="true" id="teacher-quote">
                                "This project has transformed my understanding of language teaching. Working with diverse communities has made me a more adaptable and empathetic educator."
                            </blockquote>
                            <p class="text-lg leading-relaxed editable" contenteditable="true" id="teacher-p">
                                Participating teachers report enhanced cultural sensitivity, improved classroom management skills, and a deeper appreciation for the challenges faced by English language learners in underserved communities.
                            </p>
                        </div>
                    </div>

                    <!-- Community Impact -->
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary editable" contenteditable="true" id="community-title">Community Impact</h3>
                            <p class="text-lg leading-relaxed mb-4 editable" contenteditable="true" id="community-p">
                                The project has strengthened community bonds and created lasting partnerships between HKBU and local organizations. Community leaders have expressed enthusiasm for continuing the program and expanding its reach.
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2 editable" contenteditable="true" id="impact-stats-title">Measurable Impact:</h4>
                                <ul class="space-y-1">
                                    <li class="editable" contenteditable="true" id="stat1">• 150+ students directly benefited</li>
                                    <li class="editable" contenteditable="true" id="stat2">• 25 HKBU student teachers participated</li>
                                    <li class="editable" contenteditable="true" id="stat3">• 8 community partnerships established</li>
                                    <li class="editable" contenteditable="true" id="stat4">• 95% student satisfaction rate</li>
                                </ul>
                            </div>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center cursor-pointer hover:bg-opacity-80 transition-all relative" onclick="uploadImage('community-img')" id="community-img-container">
                            <input type="file" id="community-img-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, 'community-img')">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="community-img-text">
                                Click to upload image<br><small class="text-sm">(or edit this text)</small>
                            </span>
                            <span class="editable hidden" contenteditable="true" id="community-img">Community Partnership Event</span>
                        </div>
                    </div>

                    <!-- Future Directions -->
                    <div class="bg-primary text-white rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 editable" contenteditable="true" id="future-title">Future Directions</h3>
                        <p class="text-lg leading-relaxed editable" contenteditable="true" id="future-p">
                            Building on our success, we plan to expand the program to additional communities, integrate more advanced AI technologies, and develop a sustainable model for long-term impact. We're also exploring partnerships with international organizations to scale our innovative teaching approaches globally.
                        </p>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Current language
        let currentLang = 'en';
        
        // Store edited content
        let editedContent = {};
        let uploadedImages = {};
        let currentVideoUrl = 'https://www.youtube.com/embed/o8NPllzkFhE?start=1118';

        // Content translations (same as before)
        const translations = {
            en: {
                'main-title': 'Voluntary Teaching Project',
                'main-subtitle': 'HKBU LANG 2077 - Innovative Language Education Initiative',
                'nav-video': '📹 Video',
                'nav-contexts': '🎯 Teaching Contexts',
                'nav-ai': '🤖 AI Solutions',
                'nav-reflection': '💭 Sharing & Reflection',
                'video-title': 'Project Overview Video',
                'video-description': 'Watch our comprehensive overview of the voluntary teaching project and its impact on language education.',
                'contexts-title': 'Teaching Contexts, Challenges and Needs',
                'context1-title': 'Educational Context',
                'context1-p1': 'Our voluntary teaching project targets underserved communities where English language education resources are limited. We focus on providing quality language instruction to students who lack access to traditional educational opportunities.',
                'context1-p2': 'The project emphasizes practical communication skills, cultural exchange, and building confidence in English language use through interactive and engaging teaching methodologies.',
                'context1-img': 'Teaching Context Image',
                'context2-title': 'Key Challenges',
                'context2-img': 'Challenges Overview',
                'challenge1': 'Limited technological infrastructure in target communities',
                'challenge2': 'Diverse learning levels and backgrounds among students',
                'challenge3': 'Cultural and linguistic barriers affecting communication',
                'challenge4': 'Time constraints and scheduling difficulties',
                'context3-title': 'Community Needs',
                'context3-p1': 'Through community engagement and needs assessment, we identified critical gaps in English language education. Local communities expressed strong desire for practical English skills that could enhance employment opportunities and social mobility.',
                'context3-p2': 'The program addresses these needs through tailored curriculum design, flexible scheduling, and culturally sensitive teaching approaches that respect local contexts while promoting global communication skills.',
                'context3-img': 'Community Needs Assessment',
                'ai-title': 'AI-Powered Teaching Solutions',
                'ai-intro-title': 'Innovative AI Integration',
                'ai-intro-p': 'Our project leverages cutting-edge AI technology to enhance the teaching and learning experience. We\'ve developed customized chatbots and AI-assisted tools that provide personalized support to both teachers and students, making language learning more accessible and effective.',
                'chatbot-title': 'Custom AI Teaching Assistant',
                'chatbot-p': 'Our AI chatbot serves as a 24/7 teaching assistant, providing instant feedback on grammar, pronunciation, and vocabulary. It adapts to individual learning styles and progress levels, offering personalized exercises and explanations.',
                'chatbot-features-title': 'Key Features:',
                'feature1': '• Real-time grammar correction',
                'feature2': '• Pronunciation guidance',
                'feature3': '• Adaptive learning paths',
                'feature4': '• Progress tracking',
                'chatbot-img': 'AI Chatbot Interface Screenshot',
                'analytics-title': 'Learning Analytics Platform',
                'analytics-p': 'Advanced AI analytics help teachers understand student progress patterns, identify learning difficulties, and optimize teaching strategies. The platform provides detailed insights into student engagement and performance metrics.',
                'analytics-features-title': 'Analytics Features:',
                'analytics1': '• Student progress visualization',
                'analytics2': '• Learning pattern analysis',
                'analytics3': '• Predictive performance modeling',
                'analytics4': '• Customized intervention recommendations',
                'analytics-img': 'AI Analytics Dashboard',
                'impact-title': 'Implementation Impact',
                'impact-p': 'The integration of AI solutions has resulted in improved learning outcomes, increased student engagement, and more efficient teaching processes. Students report higher confidence levels and teachers benefit from data-driven insights to enhance their instructional methods.',
                'reflection-title': 'Sharing & Reflection',
                'outcomes-title': 'Project Outcomes',
                'outcomes-p1': 'Our voluntary teaching project has yielded significant positive outcomes for both students and teachers. Student engagement has increased by 40%, and standardized test scores have improved across all participating communities.',
                'outcomes-p2': 'The project has also enhanced the professional development of HKBU students, providing them with valuable real-world teaching experience and cross-cultural communication skills.',
                'outcomes-img': 'Student Success Stories',
                'teacher-title': 'Teacher Reflections',
                'teacher-quote': '"This project has transformed my understanding of language teaching. Working with diverse communities has made me a more adaptable and empathetic educator."',
                'teacher-p': 'Participating teachers report enhanced cultural sensitivity, improved classroom management skills, and a deeper appreciation for the challenges faced by English language learners in underserved communities.',
                'teacher-img': 'Teacher Feedback Session',
                'community-title': 'Community Impact',
                'community-p': 'The project has strengthened community bonds and created lasting partnerships between HKBU and local organizations. Community leaders have expressed enthusiasm for continuing the program and expanding its reach.',
                'impact-stats-title': 'Measurable Impact:',
                'stat1': '• 150+ students directly benefited',
                'stat2': '• 25 HKBU student teachers participated',
                'stat3': '• 8 community partnerships established',
                'stat4': '• 95% student satisfaction rate',
                'community-img': 'Community Partnership Event',
                'future-title': 'Future Directions',
                'future-p': 'Building on our success, we plan to expand the program to additional communities, integrate more advanced AI technologies, and develop a sustainable model for long-term impact. We\'re also exploring partnerships with international organizations to scale our innovative teaching approaches globally.'
            },
            zh: {
                'main-title': '志愿教学项目',
                'main-subtitle': '香港浸会大学 LANG 2077 - 创新语言教育倡议',
                'nav-video': '📹 视频',
                'nav-contexts': '🎯 教学背景',
                'nav-ai': '🤖 AI解决方案',
                'nav-reflection': '💭 分享与反思',
                'video-title': '项目概述视频',
                'video-description': '观看我们志愿教学项目的全面概述及其对语言教育的影响。',
                'contexts-title': '教学背景、挑战与需求',
                'context1-title': '教育背景',
                'context1-p1': '我们的志愿教学项目针对英语教育资源有限的服务不足社区。我们专注于为缺乏传统教育机会的学生提供优质的语言教学。',
                'context1-p2': '该项目强调实用沟通技能、文化交流，并通过互动和引人入胜的教学方法建立使用英语的信心。',
                'context1-img': '教学背景图片',
                'context2-title': '主要挑战',
                'context2-img': '挑战概述',
                'challenge1': '目标社区技术基础设施有限',
                'challenge2': '学生学习水平和背景多样化',
                'challenge3': '影响沟通的文化和语言障碍',
                'challenge4': '时间限制和日程安排困难',
                'context3-title': '社区需求',
                'context3-p1': '通过社区参与和需求评估，我们确定了英语教育的关键差距。当地社区强烈希望获得能够增强就业机会和社会流动性的实用英语技能。',
                'context3-p2': '该计划通过量身定制的课程设计、灵活的时间安排和文化敏感的教学方法来解决这些需求，这些方法既尊重当地环境又促进全球沟通技能。',
                'context3-img': '社区需求评估',
                'ai-title': 'AI驱动的教学解决方案',
                'ai-intro-title': '创新AI集成',
                'ai-intro-p': '我们的项目利用前沿AI技术来增强教学和学习体验。我们开发了定制的聊天机器人和AI辅助工具，为教师和学生提供个性化支持，使语言学习更加便捷和有效。',
                'chatbot-title': '定制AI教学助手',
                'chatbot-p': '我们的AI聊天机器人作为全天候教学助手，提供语法、发音和词汇的即时反馈。它适应个人学习风格和进度水平，提供个性化的练习和解释。',
                'chatbot-features-title': '主要功能：',
                'feature1': '• 实时语法纠正',
                'feature2': '• 发音指导',
                'feature3': '• 适应性学习路径',
                'feature4': '• 进度跟踪',
                'chatbot-img': 'AI聊天机器人界面截图',
                'analytics-title': '学习分析平台',
                'analytics-p': '先进的AI分析帮助教师了解学生的进步模式，识别学习困难，并优化教学策略。该平台提供学生参与度和表现指标的详细见解。',
                'analytics-features-title': '分析功能：',
                'analytics1': '• 学生进度可视化',
                'analytics2': '• 学习模式分析',
                'analytics3': '• 预测性能建模',
                'analytics4': '• 定制干预建议',
                'analytics-img': 'AI分析仪表板',
                'impact-title': '实施影响',
                'impact-p': 'AI解决方案的集成已经带来了更好的学习成果、增加的学生参与度和更高效的教学过程。学生报告信心水平提高，教师受益于数据驱动的见解来增强他们的教学方法。',
                'reflection-title': '分享与反思',
                'outcomes-title': '项目成果',
                'outcomes-p1': '我们的志愿教学项目为学生和教师都产生了显著的积极成果。学生参与度提高了40%，所有参与社区的标准化考试成绩都有所提高。',
                'outcomes-p2': '该项目还增强了香港浸会大学学生的专业发展，为他们提供了宝贵的真实世界教学经验和跨文化沟通技能。',
                'outcomes-img': '学生成功故事',
                'teacher-title': '教师反思',
                'teacher-quote': '"这个项目改变了我对语言教学的理解。与不同社区的合作使我成为了一个更具适应性和同理心的教育者。"',
                'teacher-p': '参与的教师报告了增强的文化敏感性、改进的课堂管理技能，以及对服务不足社区英语学习者面临挑战的更深层次理解。',
                'teacher-img': '教师反馈会议',
                'community-title': '社区影响',
                'community-p': '该项目加强了社区联系，并在香港浸会大学和当地组织之间建立了持久的伙伴关系。社区领导人对继续该计划并扩大其影响力表示热情。',
                'impact-stats-title': '可衡量的影响：',
                'stat1': '• 150+名学生直接受益',
                'stat2': '• 25名香港浸会大学学生教师参与',
                'stat3': '• 建立了8个社区伙伴关系',
                'stat4': '• 95%的学生满意度',
                'community-img': '社区伙伴关系活动',
                'future-title': '未来方向',
                'future-p': '基于我们的成功，我们计划将项目扩展到更多社区，集成更先进的AI技术，并开发可持续的长期影响模式。我们还在探索与国际组织的伙伴关系，以在全球范围内推广我们的创新教学方法。'
            }
        };

        // Save section function
        function saveSection(sectionId) {
            const section = document.getElementById(sectionId);
            const editableElements = section.querySelectorAll('.editable[contenteditable="true"]');
            
            editableElements.forEach(element => {
                if (element.id) {
                    if (!editedContent[currentLang]) {
                        editedContent[currentLang] = {};
                    }
                    editedContent[currentLang][element.id] = element.textContent.trim();
                }
            });
            
            // Show success message
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Saved!';
            button.style.backgroundColor = '#10B981';
            setTimeout(() => {
                button.textContent = originalText;
                button.style.backgroundColor = '';
            }, 2000);
        }

        // Update video function
        function updateVideo() {
            const urlInput = document.getElementById('video-url-input');
            const url = urlInput.value.trim();
            
            if (!url) {
                alert('Please enter a video URL');
                return;
            }
            
            let embedUrl = '';
            
            // YouTube URL processing
            if (url.includes('youtube.com') || url.includes('youtu.be')) {
                let videoId = '';
                if (url.includes('youtu.be/')) {
                    videoId = url.split('youtu.be/')[1].split('?')[0];
                } else if (url.includes('watch?v=')) {
                    videoId = url.split('watch?v=')[1].split('&')[0];
                }
                if (videoId) {
                    embedUrl = `https://www.youtube.com/embed/${videoId}`;
                }
            }
            // Bilibili URL processing
            else if (url.includes('bilibili.com')) {
                let bvid = '';
                if (url.includes('/video/')) {
                    const match = url.match(/video\/(BV[a-zA-Z0-9]+)/);
                    if (match) {
                        bvid = match[1];
                        embedUrl = `//player.bilibili.com/player.html?bvid=${bvid}&page=1`;
                    }
                }
            }
            
            if (embedUrl) {
                currentVideoUrl = embedUrl;
                updateVideoContainer();
                urlInput.value = '';
                alert('Video updated successfully!');
            } else {
                alert('Invalid video URL. Please enter a valid YouTube or Bilibili URL.');
            }
        }

        // Update video container
        function updateVideoContainer() {
            const videoContainer = document.getElementById('video-container');
            
            if (currentVideoUrl.includes('bilibili.com')) {
                videoContainer.innerHTML = `
                    <div class="space-y-4">
                        <iframe 
                            width="100%" 
                            height="100%" 
                            src="${currentVideoUrl}" 
                            scrolling="no" 
                            border="0" 
                            frameborder="no" 
                            framespacing="0" 
                            allowfullscreen="true"
                            class="rounded-lg aspect-video">
                        </iframe>
                    </div>
                `;
            } else {
                videoContainer.innerHTML = `
                    <iframe 
                        width="100%" 
                        height="100%" 
                        src="${currentVideoUrl}" 
                        title="Project Video" 
                        frameborder="0" 
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                        allowfullscreen
                        class="rounded-lg">
                    </iframe>
                `;
            }
        }

        // Image upload functions
        function uploadImage(imageId) {
            const fileInput = document.getElementById(imageId + '-upload');
            fileInput.click();
        }

        function handleImageUpload(event, imageId) {
            const file = event.target.files[0];
            if (!file) return;

            // Check if it's an image
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file.');
                return;
            }

            // Check file size (limit to 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Image size should be less than 5MB.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const base64Image = e.target.result;
                
                // Store the base64 image
                uploadedImages[imageId] = base64Image;
                
                // Update the container to show the image
                const container = document.getElementById(imageId + '-container');
                container.innerHTML = `
                    <img src="${base64Image}" alt="Uploaded image" class="w-full h-full object-cover rounded-lg">
                    <div class="absolute top-2 right-2 flex gap-2">
                        <button onclick="editImageText('${imageId}')" class="bg-primary text-white px-2 py-1 rounded text-xs opacity-80 hover:opacity-100">
                            Edit Text
                        </button>
                        <button onclick="removeImage('${imageId}')" class="bg-red-600 text-white px-2 py-1 rounded text-xs opacity-80 hover:opacity-100">
                            Remove
                        </button>
                    </div>
                `;
                container.style.backgroundImage = 'none';
                
                // Show success message
                const successMsg = document.createElement('div');
                successMsg.textContent = 'Image uploaded successfully!';
                successMsg.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg z-50';
                document.body.appendChild(successMsg);
                setTimeout(() => document.body.removeChild(successMsg), 3000);
            };
            reader.readAsDataURL(file);
        }

        function editImageText(imageId) {
            const textElement = document.getElementById(imageId);
            const newText = prompt('Enter image caption/text:', textElement.textContent);
            if (newText !== null) {
                textElement.textContent = newText;
            }
        }

        function removeImage(imageId) {
            if (confirm('Remove this image?')) {
                delete uploadedImages[imageId];
                
                // Reset to placeholder
                const container = document.getElementById(imageId + '-container');
                const textElement = document.getElementById(imageId);
                const originalText = textElement.textContent;
                
                container.innerHTML = `
                    <input type="file" id="${imageId}-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(event, '${imageId}')">
                    <span class="text-gray-500 dark:text-gray-400 text-lg font-medium text-center" id="${imageId}-text">
                        Click to upload image<br><small class="text-sm">(or edit this text)</small>
                    </span>
                    <span class="editable hidden" contenteditable="true" id="${imageId}">${originalText}</span>
                `;
                container.className = container.className.replace('relative', '') + ' cursor-pointer hover:bg-opacity-80 transition-all relative';
                container.onclick = () => uploadImage(imageId);
                container.style.backgroundImage = '';
            }
        }

        // Download HTML function
        function downloadHTML() {
            // Get current content
            const currentContent = {};
            document.querySelectorAll('.editable[contenteditable="true"]').forEach(element => {
                if (element.id) {
                    currentContent[element.id] = element.textContent.trim();
                }
            });
            
            // Escape quotes in content
            const escapeQuotes = (str) => str.replace(/"/g, '&quot;').replace(/'/g, '&#39;');
            
            // Create the video container HTML
            const videoContainerHTML = currentVideoUrl.includes('bilibili.com') 
                ? `<iframe width="100%" height="100%" src="${currentVideoUrl}" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true" class="rounded-lg aspect-video"></iframe>`
                : `<iframe width="100%" height="100%" src="${currentVideoUrl}" title="Project Video" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen class="rounded-lg"></iframe>`;
            
            // Merge edited content with translations
            const mergedTranslations = JSON.parse(JSON.stringify(translations));
            if (editedContent.en) {
                Object.assign(mergedTranslations.en, editedContent.en);
            }
            if (editedContent.zh) {
                Object.assign(mergedTranslations.zh, editedContent.zh);
            }
            
            // Create clean HTML without editing functionality
            const cleanHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voluntary Teaching Project - LANG 2077</title>
    <script src="https://cdn.tailwindcss.com"><\/script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'bg-light': '#FFFFFF',
                        'bg-dark': '#181818'
                    }
                }
            }
        }
    <\/script>
    <style>
        .section { scroll-margin-top: 100px; }
        .tab-button.active { background-color: #5D5CDE; color: white; }
        .placeholder-img {
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .dark .placeholder-img {
            background: linear-gradient(45deg, #404040 25%, transparent 25%), 
                        linear-gradient(-45deg, #404040 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #404040 75%), 
                        linear-gradient(-45deg, transparent 75%, #404040 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
    </style>
</head>
<body class="bg-bg-light dark:bg-bg-dark text-gray-900 dark:text-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <div class="flex justify-end mb-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-1 flex">
                <button onclick="switchLanguage('en')" id="lang-en" class="px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white">English</button>
                <button onclick="switchLanguage('zh')" id="lang-zh" class="px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary">中文</button>
            </div>
        </div>
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-primary mb-2" id="main-title">${escapeQuotes(currentContent['main-title'] || 'Voluntary Teaching Project')}</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400" id="main-subtitle">${escapeQuotes(currentContent['main-subtitle'] || 'HKBU LANG 2077 - Innovative Language Education Initiative')}</p>
        </div>
        <nav class="sticky top-0 bg-bg-light dark:bg-bg-dark z-10 pb-4">
            <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700 mb-8">
                <button onclick="scrollToSection('video')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-video">📹 Video</button>
                <button onclick="scrollToSection('contexts')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-contexts">🎯 Teaching Contexts</button>
                <button onclick="scrollToSection('ai-solutions')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-ai">🤖 AI Solutions</button>
                <button onclick="scrollToSection('reflection')" class="tab-button px-6 py-3 text-base font-medium text-gray-600 dark:text-gray-400 hover:text-primary transition-colors" id="nav-reflection">💭 Sharing & Reflection</button>
            </div>
        </nav>
        <div class="space-y-16">
            <section id="video" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-6 text-center" id="video-title">${escapeQuotes(currentContent['video-title'] || 'Project Overview Video')}</h2>
                    <div class="aspect-video max-w-4xl mx-auto" id="video-container">${videoContainerHTML}</div>
                    <p class="text-center text-gray-600 dark:text-gray-400 mt-4" id="video-description">${escapeQuotes(currentContent['video-description'] || 'Watch our comprehensive overview of the voluntary teaching project and its impact on language education.')}</p>
                </div>
            </section>
            <section id="contexts" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center" id="contexts-title">${escapeQuotes(currentContent['contexts-title'] || 'Teaching Contexts, Challenges and Needs')}</h2>
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="context1-title">${escapeQuotes(currentContent['context1-title'] || 'Educational Context')}</h3>
                            <p class="text-lg leading-relaxed mb-4" id="context1-p1">${escapeQuotes(currentContent['context1-p1'] || 'Our voluntary teaching project targets underserved communities where English language education resources are limited. We focus on providing quality language instruction to students who lack access to traditional educational opportunities.')}</p>
                            <p class="text-lg leading-relaxed" id="context1-p2">${escapeQuotes(currentContent['context1-p2'] || 'The project emphasizes practical communication skills, cultural exchange, and building confidence in English language use through interactive and engaging teaching methodologies.')}</p>
                        </div>
${uploadedImages['context1-img'] ? 
                            `<div class="rounded-lg h-64 flex items-center justify-center"><img src="${uploadedImages['context1-img']}" alt="${escapeQuotes(currentContent['context1-img'] || 'Teaching Context Image')}" class="w-full h-full object-cover rounded-lg"></div>` :
                            `<div class="placeholder-img rounded-lg h-64 flex items-center justify-center"><span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="context1-img">${escapeQuotes(currentContent['context1-img'] || 'Teaching Context Image')}</span></div>`
                        }
                    </div>
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
${uploadedImages['context2-img'] ? 
                            `<div class="rounded-lg h-64 flex items-center justify-center order-2 md:order-1"><img src="${uploadedImages['context2-img']}" alt="${escapeQuotes(currentContent['context2-img'] || 'Challenges Overview')}" class="w-full h-full object-cover rounded-lg"></div>` :
                            `<div class="placeholder-img rounded-lg h-64 flex items-center justify-center order-2 md:order-1"><span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="context2-img">${escapeQuotes(currentContent['context2-img'] || 'Challenges Overview')}</span></div>`
                        }
                        <div class="order-1 md:order-2">
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="context2-title">${escapeQuotes(currentContent['context2-title'] || 'Key Challenges')}</h3>
                            <ul class="text-lg leading-relaxed space-y-2">
                                <li class="flex items-start"><span class="text-primary mr-2">•</span><span id="challenge1">${escapeQuotes(currentContent['challenge1'] || 'Limited technological infrastructure in target communities')}</span></li>
                                <li class="flex items-start"><span class="text-primary mr-2">•</span><span id="challenge2">${escapeQuotes(currentContent['challenge2'] || 'Diverse learning levels and backgrounds among students')}</span></li>
                                <li class="flex items-start"><span class="text-primary mr-2">•</span><span id="challenge3">${escapeQuotes(currentContent['challenge3'] || 'Cultural and linguistic barriers affecting communication')}</span></li>
                                <li class="flex items-start"><span class="text-primary mr-2">•</span><span id="challenge4">${escapeQuotes(currentContent['challenge4'] || 'Time constraints and scheduling difficulties')}</span></li>
                            </ul>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="context3-title">${escapeQuotes(currentContent['context3-title'] || 'Community Needs')}</h3>
                            <p class="text-lg leading-relaxed mb-4" id="context3-p1">${escapeQuotes(currentContent['context3-p1'] || 'Through community engagement and needs assessment, we identified critical gaps in English language education. Local communities expressed strong desire for practical English skills that could enhance employment opportunities and social mobility.')}</p>
                            <p class="text-lg leading-relaxed" id="context3-p2">${escapeQuotes(currentContent['context3-p2'] || 'The program addresses these needs through tailored curriculum design, flexible scheduling, and culturally sensitive teaching approaches that respect local contexts while promoting global communication skills.')}</p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="context3-img">${escapeQuotes(currentContent['context3-img'] || 'Community Needs Assessment')}</span>
                        </div>
                    </div>
                </div>
            </section>
            <section id="ai-solutions" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center" id="ai-title">${escapeQuotes(currentContent['ai-title'] || 'AI-Powered Teaching Solutions')}</h2>
                    <div class="mb-12">
                        <h3 class="text-2xl font-semibold mb-4 text-primary" id="ai-intro-title">${escapeQuotes(currentContent['ai-intro-title'] || 'Innovative AI Integration')}</h3>
                        <p class="text-lg leading-relaxed mb-6" id="ai-intro-p">${escapeQuotes(currentContent['ai-intro-p'] || "Our project leverages cutting-edge AI technology to enhance the teaching and learning experience. We've developed customized chatbots and AI-assisted tools that provide personalized support to both teachers and students, making language learning more accessible and effective.")}</p>
                    </div>
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="chatbot-title">${escapeQuotes(currentContent['chatbot-title'] || 'Custom AI Teaching Assistant')}</h3>
                            <p class="text-lg leading-relaxed mb-4" id="chatbot-p">${escapeQuotes(currentContent['chatbot-p'] || 'Our AI chatbot serves as a 24/7 teaching assistant, providing instant feedback on grammar, pronunciation, and vocabulary. It adapts to individual learning styles and progress levels, offering personalized exercises and explanations.')}</p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2" id="chatbot-features-title">${escapeQuotes(currentContent['chatbot-features-title'] || 'Key Features:')}</h4>
                                <ul class="space-y-1 text-sm">
                                    <li id="feature1">${escapeQuotes(currentContent['feature1'] || '• Real-time grammar correction')}</li>
                                    <li id="feature2">${escapeQuotes(currentContent['feature2'] || '• Pronunciation guidance')}</li>
                                    <li id="feature3">${escapeQuotes(currentContent['feature3'] || '• Adaptive learning paths')}</li>
                                    <li id="feature4">${escapeQuotes(currentContent['feature4'] || '• Progress tracking')}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="placeholder-img rounded-lg h-80 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="chatbot-img">${escapeQuotes(currentContent['chatbot-img'] || 'AI Chatbot Interface Screenshot')}</span>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-80 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="analytics-img">${escapeQuotes(currentContent['analytics-img'] || 'AI Analytics Dashboard')}</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="analytics-title">${escapeQuotes(currentContent['analytics-title'] || 'Learning Analytics Platform')}</h3>
                            <p class="text-lg leading-relaxed mb-4" id="analytics-p">${escapeQuotes(currentContent['analytics-p'] || 'Advanced AI analytics help teachers understand student progress patterns, identify learning difficulties, and optimize teaching strategies. The platform provides detailed insights into student engagement and performance metrics.')}</p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2" id="analytics-features-title">${escapeQuotes(currentContent['analytics-features-title'] || 'Analytics Features:')}</h4>
                                <ul class="space-y-1 text-sm">
                                    <li id="analytics1">${escapeQuotes(currentContent['analytics1'] || '• Student progress visualization')}</li>
                                    <li id="analytics2">${escapeQuotes(currentContent['analytics2'] || '• Learning pattern analysis')}</li>
                                    <li id="analytics3">${escapeQuotes(currentContent['analytics3'] || '• Predictive performance modeling')}</li>
                                    <li id="analytics4">${escapeQuotes(currentContent['analytics4'] || '• Customized intervention recommendations')}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4 text-primary" id="impact-title">${escapeQuotes(currentContent['impact-title'] || 'Implementation Impact')}</h3>
                        <p class="text-lg leading-relaxed" id="impact-p">${escapeQuotes(currentContent['impact-p'] || 'The integration of AI solutions has resulted in improved learning outcomes, increased student engagement, and more efficient teaching processes. Students report higher confidence levels and teachers benefit from data-driven insights to enhance their instructional methods.')}</p>
                    </div>
                </div>
            </section>
            <section id="reflection" class="section">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-3xl font-bold mb-8 text-center" id="reflection-title">${escapeQuotes(currentContent['reflection-title'] || 'Sharing & Reflection')}</h2>
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="outcomes-title">${escapeQuotes(currentContent['outcomes-title'] || 'Project Outcomes')}</h3>
                            <p class="text-lg leading-relaxed mb-4" id="outcomes-p1">${escapeQuotes(currentContent['outcomes-p1'] || 'Our voluntary teaching project has yielded significant positive outcomes for both students and teachers. Student engagement has increased by 40%, and standardized test scores have improved across all participating communities.')}</p>
                            <p class="text-lg leading-relaxed" id="outcomes-p2">${escapeQuotes(currentContent['outcomes-p2'] || 'The project has also enhanced the professional development of HKBU students, providing them with valuable real-world teaching experience and cross-cultural communication skills.')}</p>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="outcomes-img">${escapeQuotes(currentContent['outcomes-img'] || 'Student Success Stories')}</span>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="teacher-img">${escapeQuotes(currentContent['teacher-img'] || 'Teacher Feedback Session')}</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="teacher-title">${escapeQuotes(currentContent['teacher-title'] || 'Teacher Reflections')}</h3>
                            <blockquote class="text-lg italic mb-4 border-l-4 border-primary pl-4" id="teacher-quote">${escapeQuotes(currentContent['teacher-quote'] || '"This project has transformed my understanding of language teaching. Working with diverse communities has made me a more adaptable and empathetic educator."')}</blockquote>
                            <p class="text-lg leading-relaxed" id="teacher-p">${escapeQuotes(currentContent['teacher-p'] || 'Participating teachers report enhanced cultural sensitivity, improved classroom management skills, and a deeper appreciation for the challenges faced by English language learners in underserved communities.')}</p>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-8 mb-12">
                        <div>
                            <h3 class="text-2xl font-semibold mb-4 text-primary" id="community-title">${escapeQuotes(currentContent['community-title'] || 'Community Impact')}</h3>
                            <p class="text-lg leading-relaxed mb-4" id="community-p">${escapeQuotes(currentContent['community-p'] || 'The project has strengthened community bonds and created lasting partnerships between HKBU and local organizations. Community leaders have expressed enthusiasm for continuing the program and expanding its reach.')}</p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-semibold mb-2" id="impact-stats-title">${escapeQuotes(currentContent['impact-stats-title'] || 'Measurable Impact:')}</h4>
                                <ul class="space-y-1">
                                    <li id="stat1">${escapeQuotes(currentContent['stat1'] || '• 150+ students directly benefited')}</li>
                                    <li id="stat2">${escapeQuotes(currentContent['stat2'] || '• 25 HKBU student teachers participated')}</li>
                                    <li id="stat3">${escapeQuotes(currentContent['stat3'] || '• 8 community partnerships established')}</li>
                                    <li id="stat4">${escapeQuotes(currentContent['stat4'] || '• 95% student satisfaction rate')}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="placeholder-img rounded-lg h-64 flex items-center justify-center">
                            <span class="text-gray-500 dark:text-gray-400 text-lg font-medium" id="community-img">${escapeQuotes(currentContent['community-img'] || 'Community Partnership Event')}</span>
                        </div>
                    </div>
                    <div class="bg-primary text-white rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4" id="future-title">${escapeQuotes(currentContent['future-title'] || 'Future Directions')}</h3>
                        <p class="text-lg leading-relaxed" id="future-p">${escapeQuotes(currentContent['future-p'] || "Building on our success, we plan to expand the program to additional communities, integrate more advanced AI technologies, and develop a sustainable model for long-term impact. We're also exploring partnerships with international organizations to scale our innovative teaching approaches globally.")}</p>
                    </div>
                </div>
            </section>
        </div>
    </div>
    <script>
        let currentLang = 'en';
        const translations = ${JSON.stringify(mergedTranslations)};
        
        function switchLanguage(lang) {
            currentLang = lang;
            document.getElementById('lang-en').className = lang === 'en' ? 'px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white' : 'px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary';
            document.getElementById('lang-zh').className = lang === 'zh' ? 'px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white' : 'px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary';
            
            if (translations[lang]) {
                for (const [id, text] of Object.entries(translations[lang])) {
                    const element = document.getElementById(id);
                    if (element) element.textContent = text;
                }
            }
        }
        
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });
        
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ behavior: 'smooth', block: 'start' });
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            const navButtons = document.querySelectorAll('.tab-button');
            
            function updateActiveTab() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 120;
                    if (window.scrollY >= sectionTop) {
                        current = section.getAttribute('id');
                    }
                });
                
                navButtons.forEach((button, index) => {
                    button.classList.remove('active');
                    const buttonSections = ['video', 'contexts', 'ai-solutions', 'reflection'];
                    if (buttonSections[index] === current) {
                        button.classList.add('active');
                    }
                });
            }
            
            window.addEventListener('scroll', updateActiveTab);
            updateActiveTab();
            
            if (navButtons.length > 0) {
                navButtons[0].classList.add('active');
            }
        });
    <\/script>
</body>
</html>`;
            
            // Create and download file
            const blob = new Blob([cleanHTML], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'voluntary-teaching-project-final.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Language switching function
        function switchLanguage(lang) {
            currentLang = lang;
            
            // Update language buttons
            document.getElementById('lang-en').className = lang === 'en' 
                ? 'px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white'
                : 'px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary';
            
            document.getElementById('lang-zh').className = lang === 'zh' 
                ? 'px-4 py-2 rounded-md text-sm font-medium transition-colors bg-primary text-white'
                : 'px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 dark:text-gray-400 hover:text-primary';
            
            // Update content
            for (const [id, text] of Object.entries(translations[lang])) {
                const element = document.getElementById(id);
                if (element) {
                    // Use edited content if available, otherwise use translation
                    const editedText = editedContent[lang] && editedContent[lang][id];
                    element.textContent = editedText || text;
                }
            }
        }

        // Dark mode detection
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Scroll to section function
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update active tab
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Update active tab based on scroll position
            const sections = document.querySelectorAll('.section');
            const navButtons = document.querySelectorAll('.tab-button');
            
            function updateActiveTab() {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 120;
                    if (window.scrollY >= sectionTop) {
                        current = section.getAttribute('id');
                    }
                });
                
                navButtons.forEach((button, index) => {
                    button.classList.remove('active');
                    const buttonSections = ['video', 'contexts', 'ai-solutions', 'reflection'];
                    if (buttonSections[index] === current) {
                        button.classList.add('active');
                    }
                });
            }
            
            window.addEventListener('scroll', updateActiveTab);
            updateActiveTab();
            
            // Set first tab as active initially
            if (navButtons.length > 0) {
                navButtons[0].classList.add('active');
            }
        });
    </script>


</body></html>