<!DOCTYPE html><html lang="zh-CN"><head><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com https://unpkg.com https://d3js.org https://threejs.org https://cdn.plot.ly https://stackpath.bootstrapcdn.com https://maps.googleapis.com https://cdn.tailwindcss.com https://ajax.googleapis.com https://kit.fontawesome.com https://cdn.datatables.net https://maxcdn.bootstrapcdn.com https://code.highcharts.com https://tako-static-assets-production.s3.amazonaws.com https://www.youtube.com https://fonts.googleapis.com https://fonts.gstatic.com https://pfst.cf2.poecdn.net https://puc.poecdn.net https://i.imgur.com https://wikimedia.org https://*.icons8.com https://*.giphy.com https://picsum.photos https://images.unsplash.com; frame-src 'self' https://www.youtube.com https://trytako.com; child-src 'self'; manifest-src 'self'; worker-src 'self'; upgrade-insecure-requests; block-all-mixed-content;">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>普通话辩论技巧学习游戏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#5D5CDE',
                        'bg-light': '#FFFFFF',
                        'bg-dark': '#181818'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .progress-bar {
            transition: width 0.3s ease;
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(93, 92, 222, 0.15);
        }
        
        .dark .bg-white {
            background-color: #2d2d2d;
        }
        
        .dark .text-gray-900 {
            color: #e5e5e5;
        }
        
        .dark .text-gray-600 {
            color: #a1a1aa;
        }
        
        .dark .text-gray-700 {
            color: #d4d4d8;
        }
        
        .dark .border-gray-200 {
            border-color: #404040;
        }
        
        .dark .bg-gray-50 {
            background-color: #2a2a2a;
        }
        
        .dark .bg-gray-100 {
            background-color: #333333;
        }
    </style>
</head>
<body class="bg-bg-light dark:bg-bg-dark min-h-screen transition-colors duration-300">
    <!-- 主容器 -->
    <div class="container mx-auto px-4 py-6 max-w-4xl">
        <!-- 标题和进度 -->
        <div class="text-center mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                🎯 普通话辩论技巧学习游戏
            </h1>
            <p class="text-gray-600 dark:text-gray-300 text-lg">
                掌握辩论技巧，提升表达能力
            </p>
            <div class="mt-4 bg-gray-200 dark:bg-gray-700 rounded-full h-3 w-full max-w-md mx-auto">
                <div id="overallProgress" class="bg-primary h-3 rounded-full progress-bar" style="width: 0%"></div>
            </div>
            <p id="progressText" class="text-sm text-gray-500 dark:text-gray-400 mt-2">进度: 0/12</p>
        </div>

        <!-- 主要游戏区域 -->
        <div id="gameContainer" class="fade-in">
            <!-- 主菜单 -->
            <div id="mainMenu" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 card-hover cursor-pointer" onclick="showSection('tutorial')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">📚</div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">辩论基础</h3>
                        <p class="text-gray-600 dark:text-gray-300">学习辩论的基本流程和规则</p>
                        <div class="mt-4 text-sm text-primary font-medium">4个模块</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 card-hover cursor-pointer" onclick="showSection('techniques')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">🛠️</div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">辩论技巧</h3>
                        <p class="text-gray-600 dark:text-gray-300">掌握各种实用的辩论技巧</p>
                        <div class="mt-4 text-sm text-primary font-medium">4个技巧</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 card-hover cursor-pointer" onclick="showSection('practice')">
                    <div class="text-center">
                        <div class="text-4xl mb-4">💪</div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">实战练习</h3>
                        <p class="text-gray-600 dark:text-gray-300">通过模拟场景练习辩论</p>
                        <div class="mt-4 text-sm text-primary font-medium">4个场景</div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 card-hover cursor-pointer" onclick="showAchievements()">
                    <div class="text-center">
                        <div class="text-4xl mb-4">🏆</div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">成就系统</h3>
                        <p class="text-gray-600 dark:text-gray-300">查看学习成果和获得的徽章</p>
                        <div id="achievementCount" class="mt-4 text-sm text-primary font-medium">0/8 徽章</div>
                    </div>
                </div>
            </div>

            <!-- 辩论基础教程 -->
            <div id="tutorialSection" class="hidden">
                <div class="mb-6">
                    <button onclick="showMainMenu()" class="flex items-center text-primary hover:text-purple-700 transition-colors">
                        <span class="mr-2">←</span> 返回主菜单
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="tutorialModules">
                    <!-- 动态生成教程模块 -->
                </div>
            </div>

            <!-- 辩论技巧 -->
            <div id="techniquesSection" class="hidden">
                <div class="mb-6">
                    <button onclick="showMainMenu()" class="flex items-center text-primary hover:text-purple-700 transition-colors">
                        <span class="mr-2">←</span> 返回主菜单
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="techniqueModules">
                    <!-- 动态生成技巧模块 -->
                </div>
            </div>

            <!-- 实战练习 -->
            <div id="practiceSection" class="hidden">
                <div class="mb-6">
                    <button onclick="showMainMenu()" class="flex items-center text-primary hover:text-purple-700 transition-colors">
                        <span class="mr-2">←</span> 返回主菜单
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="practiceModules">
                    <!-- 动态生成练习模块 -->
                </div>
            </div>

            <!-- 学习内容详情页 -->
            <div id="contentDetail" class="hidden">
                <div class="mb-6">
                    <button id="backButton" class="flex items-center text-primary hover:text-purple-700 transition-colors">
                        <span class="mr-2">←</span> 返回
                    </button>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <div id="contentBody">
                        <!-- 动态内容 -->
                    </div>
                </div>
            </div>

            <!-- 成就页面 -->
            <div id="achievementSection" class="hidden">
                <div class="mb-6">
                    <button onclick="showMainMenu()" class="flex items-center text-primary hover:text-purple-700 transition-colors">
                        <span class="mr-2">←</span> 返回主菜单
                    </button>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">🏆 成就徽章</h2>
                    <div id="achievementsList" class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <!-- 动态生成成就 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 深色模式检测
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // 游戏数据 (使用内存存储替代localStorage)
        let gameData = {
            progress: {},
            achievements: []
        };

        // 教程内容
        const tutorialContent = [
            {
                id: 'debate-format',
                title: '辩论流程',
                icon: '🗣️',
                content: `
                    <h3 class="text-xl font-bold mb-4">标准辩论流程</h3>
                    <div class="space-y-4">
                        <div class="border-l-4 border-primary pl-4">
                            <h4 class="font-semibold">1. 开篇立论 (3分钟)</h4>
                            <p class="text-gray-600 dark:text-gray-300">正反双方分别阐述己方观点和论据</p>
                        </div>
                        <div class="border-l-4 border-primary pl-4">
                            <h4 class="font-semibold">2. 攻辩环节 (8分钟)</h4>
                            <p class="text-gray-600 dark:text-gray-300">正反方轮流提问和回答，深入交锋</p>
                        </div>
                        <div class="border-l-4 border-primary pl-4">
                            <h4 class="font-semibold">3. 自由辩论 (6分钟)</h4>
                            <p class="text-gray-600 dark:text-gray-300">双方自由发言，激烈交锋</p>
                        </div>
                        <div class="border-l-4 border-primary pl-4">
                            <h4 class="font-semibold">4. 总结陈词 (4分钟)</h4>
                            <p class="text-gray-600 dark:text-gray-300">双方总结观点，最后发言</p>
                        </div>
                    </div>
                    <button onclick="completeModule('debate-format')" class="mt-6 bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        完成学习
                    </button>
                `
            },
            {
                id: 'roles',
                title: '辩手角色',
                icon: '👥',
                content: `
                    <h3 class="text-xl font-bold mb-4">辩手角色分工</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold text-primary">一辩手</h4>
                            <ul class="list-disc list-inside text-sm text-gray-600 dark:text-gray-300 mt-2">
                                <li>开篇立论</li>
                                <li>确立己方框架</li>
                                <li>提出核心论点</li>
                            </ul>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold text-primary">二辩手</h4>
                            <ul class="list-disc list-inside text-sm text-gray-600 dark:text-gray-300 mt-2">
                                <li>反驳对方论点</li>
                                <li>补充己方论据</li>
                                <li>主导攻辩</li>
                            </ul>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold text-primary">三辩手</h4>
                            <ul class="list-disc list-inside text-sm text-gray-600 dark:text-gray-300 mt-2">
                                <li>自由辩论主力</li>
                                <li>灵活应对</li>
                                <li>攻守兼备</li>
                            </ul>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold text-primary">四辩手</h4>
                            <ul class="list-disc list-inside text-sm text-gray-600 dark:text-gray-300 mt-2">
                                <li>总结陈词</li>
                                <li>升华主题</li>
                                <li>最终冲刺</li>
                            </ul>
                        </div>
                    </div>
                    <button onclick="completeModule('roles')" class="mt-6 bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        完成学习
                    </button>
                `
            },
            {
                id: 'preparation',
                title: '赛前准备',
                icon: '📋',
                content: `
                    <h3 class="text-xl font-bold mb-4">充分的赛前准备</h3>
                    <div class="space-y-4">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">🔍 研究辩题</h4>
                            <p class="text-gray-600 dark:text-gray-300">深入理解辩题含义，分析关键词和概念</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">📚 收集资料</h4>
                            <p class="text-gray-600 dark:text-gray-300">查找权威数据、案例和专家观点</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">🎯 构建论证</h4>
                            <p class="text-gray-600 dark:text-gray-300">形成完整的论证体系和逻辑链条</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">🛡️ 预设防御</h4>
                            <p class="text-gray-600 dark:text-gray-300">预判对方可能的攻击点，准备应对策略</p>
                        </div>
                    </div>
                    <button onclick="completeModule('preparation')" class="mt-6 bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        完成学习
                    </button>
                `
            },
            {
                id: 'evaluation',
                title: '评判标准',
                icon: '⚖️',
                content: `
                    <h3 class="text-xl font-bold mb-4">辩论评判标准</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <span class="font-medium">论证逻辑</span>
                            <span class="text-primary font-bold">30%</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <span class="font-medium">事实论据</span>
                            <span class="text-primary font-bold">25%</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <span class="font-medium">反驳能力</span>
                            <span class="text-primary font-bold">20%</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <span class="font-medium">语言表达</span>
                            <span class="text-primary font-bold">15%</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <span class="font-medium">风度礼仪</span>
                            <span class="text-primary font-bold">10%</span>
                        </div>
                    </div>
                    <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <h4 class="font-semibold text-blue-800 dark:text-blue-200">💡 评分技巧</h4>
                        <p class="text-blue-700 dark:text-blue-300 text-sm mt-2">
                            评委主要看重逻辑严密、论据充分、反驳有力。语言表达要清晰流畅，态度要沉稳大方。
                        </p>
                    </div>
                    <button onclick="completeModule('evaluation')" class="mt-6 bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        完成学习
                    </button>
                `
            }
        ];

        // 技巧内容
        const techniqueContent = [
            {
                id: 'logic-reasoning',
                title: '逻辑推理',
                icon: '🧠',
                content: `
                    <h3 class="text-xl font-bold mb-4">逻辑推理技巧</h3>
                    <div class="space-y-4">
                        <div class="border border-gray-200 dark:border-gray-600 p-4 rounded-lg">
                            <h4 class="font-semibold text-primary mb-2">三段论推理</h4>
                            <p class="text-gray-600 dark:text-gray-300 mb-2">大前提 → 小前提 → 结论</p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded text-sm">
                                <p><strong>例：</strong></p>
                                <p>大前提：所有学生都应该努力学习</p>
                                <p>小前提：小明是学生</p>
                                <p>结论：小明应该努力学习</p>
                            </div>
                        </div>
                        <div class="border border-gray-200 dark:border-gray-600 p-4 rounded-lg">
                            <h4 class="font-semibold text-primary mb-2">归纳推理</h4>
                            <p class="text-gray-600 dark:text-gray-300">从个别事实推出一般规律</p>
                        </div>
                        <div class="border border-gray-200 dark:border-gray-600 p-4 rounded-lg">
                            <h4 class="font-semibold text-primary mb-2">类比推理</h4>
                            <p class="text-gray-600 dark:text-gray-300">通过相似性进行推理</p>
                        </div>
                    </div>
                    <button onclick="completeModule('logic-reasoning')" class="mt-6 bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        掌握技巧
                    </button>
                `
            },
            {
                id: 'evidence-usage',
                title: '论据运用',
                icon: '📊',
                content: `
                    <h3 class="text-xl font-bold mb-4">有效运用论据</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                            <h4 class="font-semibold text-green-800 dark:text-green-200">✅ 优质论据</h4>
                            <ul class="list-disc list-inside text-sm text-green-700 dark:text-green-300 mt-2">
                                <li>权威数据统计</li>
                                <li>专家学者观点</li>
                                <li>历史经典案例</li>
                                <li>法律条文规定</li>
                            </ul>
                        </div>
                        <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                            <h4 class="font-semibold text-red-800 dark:text-red-200">❌ 避免使用</h4>
                            <ul class="list-disc list-inside text-sm text-red-700 dark:text-red-300 mt-2">
                                <li>网络传言</li>
                                <li>个人体验</li>
                                <li>过时数据</li>
                                <li>非权威来源</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200">💡 使用技巧</h4>
                        <p class="text-yellow-700 dark:text-yellow-300 text-sm mt-2">
                            论据要新颖、准确、典型。引用时要说明出处，增强说服力。数据要具体，避免模糊表述。
                        </p>
                    </div>
                    <button onclick="completeModule('evidence-usage')" class="mt-6 bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        掌握技巧
                    </button>
                `
            },
            {
                id: 'counter-argument',
                title: '反驳技巧',
                icon: '⚡',
                content: `
                    <h3 class="text-xl font-bold mb-4">有效反驳策略</h3>
                    <div class="space-y-4">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">🎯 针对论据反驳</h4>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">质疑对方论据的真实性、时效性、代表性</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">🔗 针对逻辑反驳</h4>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">指出对方推理过程中的逻辑漏洞</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">🎪 针对概念反驳</h4>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">重新定义关键概念，改变论证基础</p>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">💰 针对价值反驳</h4>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">质疑对方价值判断的标准和合理性</p>
                        </div>
                    </div>
                    <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                        <h4 class="font-semibold text-purple-800 dark:text-purple-200">🌟 反驳原则</h4>
                        <p class="text-purple-700 dark:text-purple-300 text-sm mt-2">
                            有理有据，对事不对人。抓住要害，一击即中。态度诚恳，避免情绪化。
                        </p>
                    </div>
                    <button onclick="completeModule('counter-argument')" class="mt-6 bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        掌握技巧
                    </button>
                `
            },
            {
                id: 'speech-skills',
                title: '表达技巧',
                icon: '🎤',
                content: `
                    <h3 class="text-xl font-bold mb-4">语言表达技巧</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <h4 class="font-semibold text-primary">语音语调</h4>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-center"><span class="mr-2">🎵</span>语速适中，张弛有度</li>
                                <li class="flex items-center"><span class="mr-2">📢</span>声音洪亮，吐字清晰</li>
                                <li class="flex items-center"><span class="mr-2">🎭</span>语调变化，富有感染力</li>
                            </ul>
                        </div>
                        <div class="space-y-3">
                            <h4 class="font-semibold text-primary">肢体语言</h4>
                            <ul class="space-y-2 text-sm">
                                <li class="flex items-center"><span class="mr-2">👁️</span>眼神交流，充满自信</li>
                                <li class="flex items-center"><span class="mr-2">🤝</span>手势得体，配合发言</li>
                                <li class="flex items-center"><span class="mr-2">🚶</span>站姿端正，仪态大方</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4 space-y-3">
                        <h4 class="font-semibold text-primary">语言技巧</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                            <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded border border-blue-200 dark:border-blue-800">
                                <div class="font-medium text-blue-800 dark:text-blue-200">修辞手法</div>
                                <div class="text-blue-600 dark:text-blue-300">比喻、排比、反问</div>
                            </div>
                            <div class="bg-green-50 dark:bg-green-900/20 p-3 rounded border border-green-200 dark:border-green-800">
                                <div class="font-medium text-green-800 dark:text-green-200">逻辑词汇</div>
                                <div class="text-green-600 dark:text-green-300">因此、但是、然而</div>
                            </div>
                            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded border border-yellow-200 dark:border-yellow-800">
                                <div class="font-medium text-yellow-800 dark:text-yellow-200">情感表达</div>
                                <div class="text-yellow-600 dark:text-yellow-300">激情澎湃，理性克制</div>
                            </div>
                        </div>
                    </div>
                    <button onclick="completeModule('speech-skills')" class="mt-6 bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        掌握技巧
                    </button>
                `
            }
        ];

        // 练习场景
        const practiceContent = [
            {
                id: 'education-topic',
                title: '教育话题',
                icon: '🎓',
                topic: '学生是否应该自由选择专业',
                content: `
                    <h3 class="text-xl font-bold mb-4">练习辩题：学生是否应该自由选择专业</h3>
                    <div class="mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                                <h4 class="font-semibold text-green-800 dark:text-green-200">正方观点</h4>
                                <p class="text-green-700 dark:text-green-300 text-sm mt-2">学生应该自由选择专业</p>
                            </div>
                            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                                <h4 class="font-semibold text-red-800 dark:text-red-200">反方观点</h4>
                                <p class="text-red-700 dark:text-red-300 text-sm mt-2">学生不应该完全自由选择专业</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold mb-2">请选择你的立场并准备论据：</h4>
                            <div class="space-y-2">
                                <button onclick="chooseSide('pro', 'education-topic')" class="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    支持学生自由选择专业
                                </button>
                                <button onclick="chooseSide('con', 'education-topic')" class="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    反对学生完全自由选择专业
                                </button>
                            </div>
                        </div>
                    </div>
                `
            },
            {
                id: 'technology-topic',
                title: '科技话题',
                icon: '📱',
                topic: '社交媒体对青少年的影响是利大于弊还是弊大于利',
                content: `
                    <h3 class="text-xl font-bold mb-4">练习辩题：社交媒体对青少年的影响</h3>
                    <div class="mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                                <h4 class="font-semibold text-green-800 dark:text-green-200">正方观点</h4>
                                <p class="text-green-700 dark:text-green-300 text-sm mt-2">社交媒体对青少年利大于弊</p>
                            </div>
                            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                                <h4 class="font-semibold text-red-800 dark:text-red-200">反方观点</h4>
                                <p class="text-red-700 dark:text-red-300 text-sm mt-2">社交媒体对青少年弊大于利</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold mb-2">请选择你的立场并准备论据：</h4>
                            <div class="space-y-2">
                                <button onclick="chooseSide('pro', 'technology-topic')" class="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    支持利大于弊
                                </button>
                                <button onclick="chooseSide('con', 'technology-topic')" class="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    支持弊大于利
                                </button>
                            </div>
                        </div>
                    </div>
                `
            },
            {
                id: 'social-topic',
                title: '社会话题',
                icon: '🏛️',
                topic: '城市化进程是否应该优先考虑经济发展',
                content: `
                    <h3 class="text-xl font-bold mb-4">练习辩题：城市化进程中的优先考虑</h3>
                    <div class="mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                                <h4 class="font-semibold text-green-800 dark:text-green-200">正方观点</h4>
                                <p class="text-green-700 dark:text-green-300 text-sm mt-2">应该优先考虑经济发展</p>
                            </div>
                            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                                <h4 class="font-semibold text-red-800 dark:text-red-200">反方观点</h4>
                                <p class="text-red-700 dark:text-red-300 text-sm mt-2">不应该优先考虑经济发展</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold mb-2">请选择你的立场并准备论据：</h4>
                            <div class="space-y-2">
                                <button onclick="chooseSide('pro', 'social-topic')" class="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    支持优先经济发展
                                </button>
                                <button onclick="chooseSide('con', 'social-topic')" class="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    反对优先经济发展
                                </button>
                            </div>
                        </div>
                    </div>
                `
            },
            {
                id: 'ethics-topic',
                title: '伦理话题',
                icon: '⚖️',
                topic: '人工智能发展是否应该受到严格限制',
                content: `
                    <h3 class="text-xl font-bold mb-4">练习辩题：人工智能发展的限制问题</h3>
                    <div class="mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                                <h4 class="font-semibold text-green-800 dark:text-green-200">正方观点</h4>
                                <p class="text-green-700 dark:text-green-300 text-sm mt-2">人工智能发展应该受到严格限制</p>
                            </div>
                            <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                                <h4 class="font-semibold text-red-800 dark:text-red-200">反方观点</h4>
                                <p class="text-red-700 dark:text-red-300 text-sm mt-2">人工智能发展不应该受到严格限制</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold mb-2">请选择你的立场并准备论据：</h4>
                            <div class="space-y-2">
                                <button onclick="chooseSide('pro', 'ethics-topic')" class="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    支持严格限制
                                </button>
                                <button onclick="chooseSide('con', 'ethics-topic')" class="w-full text-left p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    反对严格限制
                                </button>
                            </div>
                        </div>
                    </div>
                `
            }
        ];

        // 成就系统
        const achievements = [
            { id: 'first-learn', title: '初学者', desc: '完成第一个学习模块', icon: '🌱' },
            { id: 'tutorial-master', title: '基础掌握', desc: '完成所有基础教程', icon: '📚' },
            { id: 'technique-learner', title: '技巧学习者', desc: '掌握所有辩论技巧', icon: '🛠️' },
            { id: 'practice-starter', title: '实战新手', desc: '完成第一次实战练习', icon: '⭐' },
            { id: 'debater', title: '辩论者', desc: '完成所有实战练习', icon: '🎯' },
            { id: 'logic-master', title: '逻辑大师', desc: '精通逻辑推理技巧', icon: '🧠' },
            { id: 'speaker', title: '演说家', desc: '掌握表达技巧', icon: '🎤' },
            { id: 'champion', title: '辩论冠军', desc: '完成所有学习内容', icon: '🏆' }
        ];

        // 初始化游戏
        function initGame() {
            updateProgress();
            generateTutorialModules();
            generateTechniqueModules();
            generatePracticeModules();
            updateAchievements();
        }

        // 显示主菜单
        function showMainMenu() {
            hideAllSections();
            document.getElementById('mainMenu').classList.remove('hidden');
        }

        // 显示指定章节
        function showSection(section) {
            hideAllSections();
            document.getElementById(section + 'Section').classList.remove('hidden');
        }

        // 隐藏所有章节
        function hideAllSections() {
            const sections = ['mainMenu', 'tutorialSection', 'techniquesSection', 'practiceSection', 'contentDetail', 'achievementSection'];
            sections.forEach(id => {
                document.getElementById(id).classList.add('hidden');
            });
        }

        // 生成教程模块
        function generateTutorialModules() {
            const container = document.getElementById('tutorialModules');
            container.innerHTML = '';
            
            tutorialContent.forEach(module => {
                const isCompleted = gameData.progress[module.id];
                const moduleEl = document.createElement('div');
                moduleEl.className = `bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover cursor-pointer ${isCompleted ? 'ring-2 ring-green-500' : ''}`;
                moduleEl.onclick = () => showContent(module, 'tutorial');
                
                moduleEl.innerHTML = `
                    <div class="text-center">
                        <div class="text-3xl mb-2">${module.icon}</div>
                        <h3 class="font-semibold text-gray-900 dark:text-white">${module.title}</h3>
                        ${isCompleted ? '<div class="text-green-500 text-sm mt-2">✅ 已完成</div>' : '<div class="text-gray-500 text-sm mt-2">📖 学习中</div>'}
                    </div>
                `;
                
                container.appendChild(moduleEl);
            });
        }

        // 生成技巧模块
        function generateTechniqueModules() {
            const container = document.getElementById('techniqueModules');
            container.innerHTML = '';
            
            techniqueContent.forEach(module => {
                const isCompleted = gameData.progress[module.id];
                const moduleEl = document.createElement('div');
                moduleEl.className = `bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover cursor-pointer ${isCompleted ? 'ring-2 ring-green-500' : ''}`;
                moduleEl.onclick = () => showContent(module, 'techniques');
                
                moduleEl.innerHTML = `
                    <div class="text-center">
                        <div class="text-3xl mb-2">${module.icon}</div>
                        <h3 class="font-semibold text-gray-900 dark:text-white">${module.title}</h3>
                        ${isCompleted ? '<div class="text-green-500 text-sm mt-2">✅ 已掌握</div>' : '<div class="text-gray-500 text-sm mt-2">🎯 学习中</div>'}
                    </div>
                `;
                
                container.appendChild(moduleEl);
            });
        }

        // 生成练习模块
        function generatePracticeModules() {
            const container = document.getElementById('practiceModules');
            container.innerHTML = '';
            
            practiceContent.forEach(module => {
                const isCompleted = gameData.progress[module.id];
                const moduleEl = document.createElement('div');
                moduleEl.className = `bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover cursor-pointer ${isCompleted ? 'ring-2 ring-green-500' : ''}`;
                moduleEl.onclick = () => showContent(module, 'practice');
                
                moduleEl.innerHTML = `
                    <div class="text-center">
                        <div class="text-3xl mb-2">${module.icon}</div>
                        <h3 class="font-semibold text-gray-900 dark:text-white">${module.title}</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mt-1">${module.topic}</p>
                        ${isCompleted ? '<div class="text-green-500 text-sm mt-2">✅ 已练习</div>' : '<div class="text-gray-500 text-sm mt-2">💪 待练习</div>'}
                    </div>
                `;
                
                container.appendChild(moduleEl);
            });
        }

        // 显示内容详情
        function showContent(module, section) {
            hideAllSections();
            document.getElementById('contentDetail').classList.remove('hidden');
            document.getElementById('contentBody').innerHTML = module.content;
            
            document.getElementById('backButton').onclick = () => showSection(section);
        }

        // 完成模块
        function completeModule(moduleId) {
            gameData.progress[moduleId] = true;
            
            updateProgress();
            updateAchievements();
            
            // 显示完成提示
            showCompletionMessage();
            
            // 返回相应章节
            setTimeout(() => {
                if (tutorialContent.some(m => m.id === moduleId)) {
                    showSection('tutorial');
                    generateTutorialModules();
                } else if (techniqueContent.some(m => m.id === moduleId)) {
                    showSection('techniques');
                    generateTechniqueModules();
                } else if (practiceContent.some(m => m.id === moduleId)) {
                    showSection('practice');
                    generatePracticeModules();
                }
            }, 2000);
        }

        // 选择立场
        function chooseSide(side, topicId) {
            const sideText = side === 'pro' ? '正方' : '反方';
            const encouragement = [
                '很好的选择！',
                '准备好你的论据！',
                '展示你的辩论技巧！',
                '相信你能说服评委！'
            ];
            
            document.getElementById('contentBody').innerHTML = `
                <div class="text-center">
                    <div class="text-6xl mb-4">🎯</div>
                    <h3 class="text-xl font-bold mb-4">你选择了${sideText}立场</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">${encouragement[Math.floor(Math.random() * encouragement.length)]}</p>
                    
                    <div class="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6">
                        <h4 class="font-semibold text-primary mb-2">💡 练习提示</h4>
                        <p class="text-sm text-gray-700 dark:text-gray-300">
                            现在请花5分钟时间思考并准备3个强有力的论据来支持你的观点。
                            记住运用我们学过的逻辑推理和论据运用技巧！
                        </p>
                    </div>
                    
                    <button onclick="completeModule('${topicId}')" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        完成练习
                    </button>
                </div>
            `;
        }

        // 显示完成提示
        function showCompletionMessage() {
            const messages = [
                '太棒了！你又掌握了一项新技能！🎉',
                '继续保持，你正在成为辩论高手！💪',
                '知识又增加了！继续加油！⭐',
                '完成得很好！下一个挑战等着你！🚀'
            ];
            
            const message = messages[Math.floor(Math.random() * messages.length)];
            
            document.getElementById('contentBody').innerHTML = `
                <div class="text-center">
                    <div class="text-6xl mb-4">🎉</div>
                    <h3 class="text-xl font-bold mb-4">恭喜完成！</h3>
                    <p class="text-gray-600 dark:text-gray-300">${message}</p>
                </div>
            `;
        }

        // 更新进度
        function updateProgress() {
            const totalModules = tutorialContent.length + techniqueContent.length + practiceContent.length;
            const completedModules = Object.keys(gameData.progress).length;
            const progressPercent = (completedModules / totalModules) * 100;
            
            document.getElementById('overallProgress').style.width = progressPercent + '%';
            document.getElementById('progressText').textContent = `进度: ${completedModules}/${totalModules}`;
        }

        // 显示成就页面
        function showAchievements() {
            hideAllSections();
            document.getElementById('achievementSection').classList.remove('hidden');
            
            const container = document.getElementById('achievementsList');
            container.innerHTML = '';
            
            achievements.forEach(achievement => {
                const isUnlocked = gameData.achievements.includes(achievement.id);
                const achievementEl = document.createElement('div');
                achievementEl.className = `bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 text-center ${isUnlocked ? 'ring-2 ring-yellow-500' : 'opacity-50'}`;
                
                achievementEl.innerHTML = `
                    <div class="text-3xl mb-2">${achievement.icon}</div>
                    <h4 class="font-semibold text-gray-900 dark:text-white text-sm">${achievement.title}</h4>
                    <p class="text-gray-600 dark:text-gray-300 text-xs mt-1">${achievement.desc}</p>
                    ${isUnlocked ? '<div class="text-yellow-500 text-xs mt-2">✨ 已获得</div>' : '<div class="text-gray-400 text-xs mt-2">🔒 未解锁</div>'}
                `;
                
                container.appendChild(achievementEl);
            });
        }

        // 更新成就
        function updateAchievements() {
            const completed = Object.keys(gameData.progress);
            const newAchievements = [];
            
            // 检查各种成就条件
            if (completed.length >= 1 && !gameData.achievements.includes('first-learn')) {
                newAchievements.push('first-learn');
            }
            
            if (completed.filter(id => tutorialContent.some(t => t.id === id)).length === tutorialContent.length && !gameData.achievements.includes('tutorial-master')) {
                newAchievements.push('tutorial-master');
            }
            
            if (completed.filter(id => techniqueContent.some(t => t.id === id)).length === techniqueContent.length && !gameData.achievements.includes('technique-learner')) {
                newAchievements.push('technique-learner');
            }
            
            if (completed.filter(id => practiceContent.some(p => p.id === id)).length >= 1 && !gameData.achievements.includes('practice-starter')) {
                newAchievements.push('practice-starter');
            }
            
            if (completed.filter(id => practiceContent.some(p => p.id === id)).length === practiceContent.length && !gameData.achievements.includes('debater')) {
                newAchievements.push('debater');
            }
            
            if (completed.includes('logic-reasoning') && !gameData.achievements.includes('logic-master')) {
                newAchievements.push('logic-master');
            }
            
            if (completed.includes('speech-skills') && !gameData.achievements.includes('speaker')) {
                newAchievements.push('speaker');
            }
            
            if (completed.length === (tutorialContent.length + techniqueContent.length + practiceContent.length) && !gameData.achievements.includes('champion')) {
                newAchievements.push('champion');
            }
            
            // 添加新成就
            gameData.achievements = [...gameData.achievements, ...newAchievements];
            
            // 更新成就计数
            document.getElementById('achievementCount').textContent = `${gameData.achievements.length}/8 徽章`;
        }

        // 初始化游戏
        initGame();
    </script>


</body></html>