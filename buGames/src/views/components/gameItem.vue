<template>
  <div class="game-item">
    <h3>{{ props.gameData.game_name }}</h3>
    <p class="truncate-text">Describe: {{ props.gameData.game_desc }}</p>
    <div class="game-item-choice">
      <el-button v-if="userStore.isLogin" type="success" size="default" color="#6c757d" @click.stop="gameEdit(props.gameData.game_id)">Edit</el-button>
      <el-button type="success" size="default" color="#1f883d" @click.stop="gamePlay(props.gameData.game_name)">Play</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/useStore'
import { defineProps } from 'vue'

const userStore = useUserStore()

// 定义props来接收游戏数据
const props = defineProps({
    gameData: {
        type: Object,
        default: () => ({
            game_id: 1,
            game_name: 'GuessNum',
            game_desc: '这是一段很长的描述文本，我想让它只显示两行，超出部分用省略号表示，内容可能非常长，超出了两行就会被截断。'
        })
    }
})
const router = useRouter()
const gamePlay = ( gameName) => {
    // URL encode the game name to handle spaces and special characters
    const encodedGameName = encodeURIComponent(gameName)
        const Base_URL = 'http://games.hkbu.life'
    // const Base_URL = 'http://localhost:3000'
    const gameUrl = Base_URL + '/GamePlay/'+ encodedGameName
    window.open(gameUrl, '_blank')
}
const gameEdit = (gameId) => {
    router.push(`/editGame/${gameId}`)
}

</script>

<style>
.game-item {
  background-color: #fff;
  height: 180px;
  min-width: 200px;
  max-width: 300px;
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
}

/* 响应式设计 - 手机端适配 */
@media (max-width: 768px) {
  .game-item {
    min-width: 150px;
    max-width: 100%;
    height: 160px;
    padding: 12px 15px;
    margin-bottom: 10px;
  }
}

@media (max-width: 480px) {
  .game-item {
    min-width: 120px;
    height: 140px;
    padding: 10px 12px;
  }
}

/* .game-item-choice {
  display: flex;
} */

.truncate-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  width: 100%;
  line-height: 1.4em;
  max-height: 2.8em; /* 两行高度 */
  flex: 1;
  margin-bottom: 0px;
}

.game-item:hover {
  cursor: pointer;
}

.game-item h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.game-item-choice {
  display: flex;
  gap: 10px;
  margin-top: auto;
}

.game-item button {
  width: 90px;
  flex-shrink: 0;
}

</style>