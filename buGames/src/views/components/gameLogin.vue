<script setup>
    import { ref } from 'vue'
    import { useRouter } from 'vue-router'
    import { ElMessage } from 'element-plus'
    import { loginAPI } from '@/utils/request'
    import { useUserStore } from '@/stores/useStore'

    const router = useRouter()
    const userStore = useUserStore()
    const formRef = ref(null)
    const form = ref({
        username: '',
        password: ''
    })
    const rules = ref({
        username: [{ required: true, message: 'Please input username' }],
        password: [{ required: true, message: 'Please input password' }]
    })

    const login = async () => {
        formRef.value.validate(async (valid) => {
            if (valid) {
                try {
                    const data = await loginAPI(form.value.username, form.value.password)

                    if (data.status === 0) {
                        // 登录成功
                        localStorage.setItem('token', data.token)
                        userStore.isLogin = true
                        ElMessage.success({
                            message: 'Login successfully',
                            type: 'success',
                            duration: 3000
                        })
                        router.push('/')
                    } else {
                        // 登录失败
                        ElMessage.error({
                            message: 'Lo<PERSON> failed',
                            type: 'error',
                            duration: 3000
                        })
                    }
                } catch (error) {
                    console.error('Lo<PERSON> failed:', error)
                    ElMessage.error({
                        message: 'Login failed',
                        type: 'error',
                        duration: 3000
                    })
                }
            }
        })
    }

    const Cancel = () => {
        router.push('/')
    }
</script>

<template>
    <div class="login-container">
        <div class="breadcrumb-container">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }">Home</el-breadcrumb-item>
                <el-breadcrumb-item>Login</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="login-form">
            <el-form label-width="120px" ref="formRef" :model="form" :rules="rules" @keyup.enter="login">
                <el-form-item label="Username" size="large" prop="username" class="login-form-item">
                    <el-input v-model="form.username" placeholder="Username" ></el-input>
                </el-form-item>
                <el-form-item label="Password" size="large" prop="password" class="login-form-item">
                    <el-input v-model="form.password" placeholder="Password" show-password ></el-input>
                </el-form-item>
                <div class="button-container">
                    <el-button type="primary" @click="Cancel" size="large" color="#4a4a4a">Cancel</el-button>
                    <el-button type="primary" @click="login" size="large" color="#1f883d">Login</el-button>
                </div>
            </el-form>
        </div>
    </div>
</template>

<style>
    .login-container {
        background-color: #f0f0f0;
        border: 1px solid #dcdfe6;
        border-top: none;
        padding: 20px;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        margin: 0 auto;
    }

    .login-container .breadcrumb-container {
        margin-bottom: 20px;
    }

    .login-container .button-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    .login-container .button-container .el-button {
        margin: 0 10px;
    }

    .login-container .login-form-item {
        /* width: 300px; */
        width: 45%;
        min-width: 280px;
        max-width: 400px;
        /* margin-left: 250px;  */
        margin: 0 auto 20px 22%;
        margin-bottom: 20px;
    }

    /* 响应式设计 - 平板端适配 */
    @media (max-width: 768px) {
        .login-container .login-form-item {
            width: 70%;
            min-width: 250px;
            margin: 0 auto 20px 15%;
        }
    }

    /* 响应式设计 - 手机端适配 */
    @media (max-width: 480px) {
        .login-container .login-form-item {
            width: 85%;
            min-width: 200px;
            margin: 0 auto 20px 7.5%;
        }
    }
</style>