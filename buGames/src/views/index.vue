<template>
  <el-container>
        <el-header class="custom-header">
            <home-header />
        </el-header>
        <el-main>
            <router-view />
        </el-main>
    </el-container>
</template>

<script setup>
    import HomeHeader from './components/homeHeader.vue'
</script>

<style>
.el-header {
  padding: 0;
  margin: 0;

  
}
.el-main {
  padding: 0;
  margin: 0;
}

    .breadcrumb-container {
        border-bottom: 1px solid #dcdfe6;
        margin: 0 auto;
        padding-bottom: 15px;
    }

    .breadcrumb-container .el-breadcrumb__inner {
        font-size: 17px;
    }

    
</style>