import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore('useUserStore', () => {
  // 检查token是否过期的函数
  const checkTokenExpiry = () => {
    const token = localStorage.getItem('token')
    if (!token) {
      return false
    }

    try {
      // 解析JWT token的payload部分
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)

      // 检查token是否过期
      if (payload.exp && payload.exp < currentTime) {
        // token已过期，清除本地存储
        localStorage.removeItem('token')
        return false
      }

      return true
    } catch (error) {
      // token格式错误，清除本地存储
      console.error('Invalid token format:', error)
      localStorage.removeItem('token')
      return false
    }
  }

  // 初始化时检查token有效性
  const isLogin = ref(checkTokenExpiry())

  const Logout = () => {
    localStorage.removeItem('token')
    isLogin.value = false
  }

  // 提供一个方法供外部调用来检查token状态
  const validateToken = () => {
    const isValid = checkTokenExpiry()
    isLogin.value = isValid
    return isValid
  }

  return { isLogin, Logout, validateToken }
})
