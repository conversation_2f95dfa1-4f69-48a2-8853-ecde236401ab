# 远程上传接口说明

## 概述
远程上传功能支持通过JSON格式直接发送游戏名称和完整的HTML/CSS/JS代码来创建游戏。

## 请求格式

### 请求方式
- **方法**: POST
- **URL**: `/upload/upload`
- **Content-Type**: `application/json`

### 请求体格式
```json
{
  "game_name": "游戏名称",
  "html_content": "完整的HTML代码（包含CSS和JS）"
}
```

### 示例请求
```json
{
  "game_name": "my_awesome_game",
  "html_content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>My Awesome Game</title>\n    <style>\n        body { text-align: center; padding: 50px; }\n        button { padding: 20px; font-size: 18px; }\n    </style>\n</head>\n<body>\n    <h1>Welcome to My Game!</h1>\n    <p>This is a simple game.</p>\n    <button onclick=\"alert('Hello!')\">Click Me!</button>\n</body>\n</html>"
}
```

## 游戏名称规则
- 只能包含字母、数字和下划线
- 不能为空
- 不能与现有游戏重名
- 正则表达式：`^[a-zA-Z0-9_]+$`

## 参数说明

### game_name（必需）
- 类型：字符串
- 游戏的唯一标识名称
- 只能包含字母、数字和下划线
- 不能为空
- 不能与现有游戏重名
- 正则表达式：`^[a-zA-Z0-9_]+$`

### html_content（必需）
- 类型：字符串
- 完整的HTML代码，可以包含CSS和JavaScript
- 不能为空
- 支持完整的HTML5语法

## 认证
- 不需要身份验证

## 响应格式

### 成功响应
```json
{
  "status": 0,
  "message": "游戏上传成功",
  "data": {
    "game_id": 9,
    "game_name": "my_awesome_game",
    "game_desc": "",
    "file_path": "upload/my_awesome_game/my_awesome_game.html"
  }
}
```

### 错误响应
```json
{
  "status": 1,
  "message": "错误信息"
}
```

## 常见错误
- `游戏名称不能为空` - game_name参数为空或未提供
- `游戏名称只能包含字母、数字和下划线` - game_name格式不符合要求
- `HTML内容不能为空` - html_content参数为空或未提供
- `游戏名已存在` - 游戏名称重复
- `文件保存失败` - 服务器文件系统错误
- `添加游戏失败` - 数据库操作失败

## 文件存储
- HTML文件保存路径：`upload/{游戏名称}/{游戏名称}.html`
- 数据库记录：games表，game_desc字段为空字符串

## 使用示例

### 使用curl测试
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "game_name": "simple_clicker",
    "html_content": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Simple Clicker</title>\n    <style>\n        body { text-align: center; padding: 50px; }\n        button { padding: 20px; font-size: 18px; }\n    </style>\n</head>\n<body>\n    <h1>Simple Clicker Game</h1>\n    <p>Score: <span id=\"score\">0</span></p>\n    <button onclick=\"increaseScore()\">Click Me!</button>\n    <script>\n        let score = 0;\n        function increaseScore() {\n            score++;\n            document.getElementById(\"score\").textContent = score;\n        }\n    </script>\n</body>\n</html>"
  }' \
  http://localhost:3000/upload/upload
```

### JavaScript示例
```javascript
const gameData = {
  game_name: "simple_clicker",
  html_content: `<!DOCTYPE html>
<html>
<head>
    <title>Simple Clicker</title>
    <style>
        body { text-align: center; padding: 50px; }
        button { padding: 20px; font-size: 18px; }
    </style>
</head>
<body>
    <h1>Simple Clicker Game</h1>
    <p>Score: <span id="score">0</span></p>
    <button onclick="increaseScore()">Click Me!</button>
    <script>
        let score = 0;
        function increaseScore() {
            score++;
            document.getElementById("score").textContent = score;
        }
    </script>
</body>
</html>`
};

fetch('http://localhost:3000/upload/upload', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(gameData)
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```
